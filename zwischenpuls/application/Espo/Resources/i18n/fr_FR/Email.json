{"fields": {"status": "Statut", "dateSent": "Date d'envoi", "from": "De", "to": "À", "replyTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replyToString": "<PERSON><PERSON><PERSON><PERSON><PERSON> (texte)", "body": "Corps", "subject": "Objet", "attachments": "Pièces-jointes", "selectTemplate": "Sélectionner un modèle", "fromAddress": "De<PERSON>is l'adresse", "emailAddress": "<PERSON><PERSON><PERSON>", "deliveryDate": "Date de remise", "account": "<PERSON><PERSON><PERSON>", "users": "Utilisateurs", "replied": "Répondu", "replies": "Réponses", "isRead": "<PERSON>", "isNotRead": "Non lu", "isImportant": "Important", "isUsers": "Utilisateurs", "inTrash": "Supprimé", "bodyPlain": "Corps (plaine)", "ccEmailAddresses": "Adresses mail CC", "messageId": "ID du message", "messageIdInternal": "Identifiant du message (interne)", "folderId": "ID de dossier", "fromName": "De nom", "fromString": "De la corde", "isSystem": "Est système", "personStringData": "<PERSON>n<PERSON> de chaîne de personne", "fromEmailAddress": "De l'adresse (lien)", "replyToName": "Nom de réponse", "replyToAddress": "Ré<PERSON>ndre à l'adresse", "icsContents": "Contenu de l'ICS", "icsEventData": "Données d'événement ICS", "icsEventUid": "UID de l'événement ICS", "createdEvent": "<PERSON><PERSON><PERSON> un évènement", "event": "<PERSON><PERSON><PERSON><PERSON>", "icsEventDateStart": "Date de début de l'événement ICS", "groupFolder": "Dossier du groupe", "isUsersSent": "Est envoyé par l'utilisateur", "inArchive": "En archive", "groupStatusFolder": "Dossier D'état Du Groupe", "sendAt": "Envoyer à", "toEmailAddresses": "Vers les adresses e-mail", "bccEmailAddresses": "BBC les adresses e-mail", "replyToEmailAddresses": "Adresses e-mail de réponse"}, "links": {"replied": "Répondu", "replies": "Réponses", "attachments": "Les pièces jointes", "fromEmailAddress": "De l'adresse e-mail", "groupFolder": "Dossier du groupe", "createdEvent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "toEmailAddresses": "<PERSON><PERSON> destina<PERSON>", "ccEmailAddresses": "Destinataires en copie CC", "bccEmailAddresses": "Destinataires en copie cachée BCC", "replyToEmailAddresses": "Adresses e-mail de réponse"}, "options": {"status": {"Draft": "Brouillon", "Sending": "En cours d'envoi", "Sent": "<PERSON><PERSON><PERSON>", "Received": "<PERSON><PERSON><PERSON>", "Failed": "Échec", "Archived": "Importé"}, "groupStatusFolder": {"Trash": "<PERSON><PERSON><PERSON><PERSON>"}}, "labels": {"Create Email": "Archiver l'eMail", "Archive Email": "Archiver l'eMail", "Compose": "<PERSON><PERSON><PERSON><PERSON>", "Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reply to All": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous", "Forward": "<PERSON><PERSON><PERSON><PERSON>", "Original message": "Message Original", "Forwarded message": "Message transféré", "Email Accounts": "Comptes emails", "Inbound Emails": "Comptes email communs", "Email Templates": "Mod<PERSON><PERSON> d'email", "Send Test Email": "Envoyer message test", "Send": "Envoyer", "Email Address": "<PERSON><PERSON><PERSON>", "Mark Read": "Marquer comme lu", "Sending...": "Envoi en cours...", "Save Draft": "Sauvegarder le brouillon", "Mark all as read": "<PERSON><PERSON> tout comme lu", "Show Plain Text": "Afficher texte brut", "Mark as Important": "<PERSON>quer comme important", "Unmark Importance": "Marquer comme non important", "Move to Trash": "Mettre à la corbeille", "Retrieve from Trash": "<PERSON><PERSON><PERSON> <PERSON> la corbeille", "View Users": "Afficher les utilisateurs", "No Subject": "Aucun Objet", "Insert Field": "Insère un champ", "Event": "<PERSON><PERSON><PERSON><PERSON>", "Group Folders": "Dossier de groupe", "View Attachments": "Voir les pièces jointes", "Import EML": "Importer EML", "Moved to Archive": "Déplacé vers l'archive", "Moved to Trash": "Déplacé vers la corbeille", "Retrieved from Trash": "Récup<PERSON><PERSON> de la Corbeille", "No Records Moved": "Aucun enregistrement déplacé", "Schedule Send": "Planifier l'envoi"}, "messages": {"testEmailSent": "L'eMail de test a été envoyé", "emailSent": "L'eMail a été envoyé", "savedAsDraft": "Sauvegardé en tant que brouillon", "confirmInsertTemplate": "Le corps de l'e-mail sera perdu. Êtes-vous sûr de vouloir insérer le modèle?", "noSmtpSetup": "SMTP n'est pas configuré: {link}", "confirmSend": "Envoyer l'email ?", "sendConfirm": "Envoyer l'email ?", "removeSelectedRecordsConfirmation": "Êtes-vous sûr de vouloir supprimer les courriels sélectionnés ?\n\nIls seront supprimés pour tous les utilisateurs.", "removeRecordConfirmation": "Êtes-vous sûr de vouloir supprimer ce courriel ?\n\nIl sera supprimé pour tous les utilisateurs.", "invalidCredentials": "Informations d'identification invalides.", "unknownError": "<PERSON><PERSON><PERSON> inconnue.", "recipientAddressRejected": "L'adresse du destinataire a été rejetée.", "alreadyImported": "Le [email]({link}) existe déjà dans le système.", "couldNotSentScheduledEmail": "Impossible d'envoyer le [email]({link}) programmé", "notEditAccess": "Pas d'accès au courrier électronique pour l'édition.", "groupFolderNoAccess": "Pas accès au dossier de groupe", "groupMoveOutNoEditAccess": "Impossible de quitter le dossier de groupe. Dr<PERSON> manquant.", "groupMoveToNoEditAccess": "Impossible de déplacer vers le dossier de groupe. <PERSON><PERSON> manquant.", "groupMoveToTrashNoEditAccess": "Impossible de déplacer l’e-mail du dossier de groupe vers la corbeille. <PERSON><PERSON> manqua<PERSON>.", "groupMoveToArchiveNoEditAccess": "Impossible de déplacer vers Archive. <PERSON><PERSON> manquant."}, "presetFilters": {"sent": "<PERSON><PERSON><PERSON>", "inbox": "Boite de r<PERSON>", "drafts": "Brouillons", "trash": "<PERSON><PERSON><PERSON><PERSON>", "archived": "Importé"}, "massActions": {"markAsRead": "<PERSON>quer commer lu", "markAsNotRead": "Marquer comme non-lu", "markAsImportant": "<PERSON>quer comme important", "markAsNotImportant": "Marquer comme non important", "moveToTrash": "Mettre à la corbeille", "moveToFolder": "Deplacer vers", "retrieveFromTrash": "Récupérer de la corbeille"}, "strings": {"sendingFailed": "Échec d'envoi du courriel", "group": "Groupe"}, "otherFields": {"file": "<PERSON><PERSON><PERSON>"}}