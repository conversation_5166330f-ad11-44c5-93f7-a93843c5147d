{"fields": {"name": "Nom", "status": "Statut", "job": "<PERSON><PERSON><PERSON>", "scheduling": "Planification (crontab notation)"}, "labels": {"Create ScheduledJob": "<PERSON><PERSON>er une tâche planifiée", "As often as possible": "Aussi souvent que possible"}, "options": {"job": {"Cleanup": "<PERSON><PERSON><PERSON>", "CheckInboundEmails": "Vérifier les emails entrants", "CheckEmailAccounts": "Vérifier ses comptes emails personnels", "SendEmailReminders": "Envoyer des notifications par email", "CheckNewVersion": "Vérifier la nouvelle version", "ProcessWebhookQueue": "Traiter la file d'attente Webhook", "SendScheduledEmails": "Envoyer des e-mails programmés"}, "cronSetup": {"linux": "Note: <PERSON><PERSON><PERSON><PERSON> cette ligne dans les tâches planifiées afin de l'exécuter :", "mac": "Note: <PERSON><PERSON><PERSON><PERSON> cette ligne dans les tâches planifiées afin de l'exécuter :", "windows": "Note: <PERSON><PERSON>ez un fichier de commandes avec les commandes suivantes pour exécuter des tâches planifiées Windows", "default": "Note: A<PERSON><PERSON> cette commande pour Cron (tâche planifiée):"}, "status": {"Active": "Actif", "Inactive": "Inactif"}}, "tooltips": {"scheduling": "La notation des tâches automatiques (Crontab) marque la fréquence des exécutions de tâches.\n\n`*/5 * * * *` - toutes les 5 minutes\n\n`0 */2 * * *` - toutes les 2 heures\n\n`30 1 * * *` - à 01h30 une fois par jour\n\n`0 0 1 * *` - le premier jour du mois"}}