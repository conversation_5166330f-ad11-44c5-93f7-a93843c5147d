{"labels": {"Enabled": "Actif", "Disabled": "Inactif", "System": "Système", "Users": "Utilisateurs", "Data": "<PERSON><PERSON><PERSON>", "Customization": "Personnalisation", "Available Fields": "Champs Disponibles", "Layout": "Mise en page", "Entity Manager": "Gestionnaire des Entités", "Add Panel": "Ajouter un panneau", "Add Field": "Ajouter un champ", "Settings": "Paramètres", "Scheduled Jobs": "Tâches planifiées", "Upgrade": "Mettre à jour", "Clear Cache": "Vider le cache", "Rebuild": "Reconstruction", "Teams": "Équipes", "Roles": "<PERSON><PERSON><PERSON>", "Portal": "Portail", "Portals": "Portails", "Portal Roles": "<PERSON><PERSON><PERSON>", "Outbound Emails": "Emails sortants", "Group Email Accounts": "Comptes emails communs", "Personal Email Accounts": "Comptes emails personnels", "Inbound Emails": "Emails entrants", "Email Templates": "Mod<PERSON><PERSON> d'email", "Layout Manager": "Gestionnaire de la mise en page", "User Interface": "Interface utilisateur", "Auth Tokens": "Jetons d'authentification", "Authentication": "Authentification", "Currency": "<PERSON><PERSON>", "Integrations": "Intégrations", "Upload": "Mettre en ligne", "Installing...": "Installation...", "Upgrading...": "Mise à jour...", "Upgraded successfully": "Mise à jour réussie", "Installed successfully": "Installé avec succès", "Ready for upgrade": "<PERSON>r<PERSON><PERSON> pour la mise à jour", "Run Upgrade": "<PERSON><PERSON><PERSON><PERSON> la mise à jour", "Install": "Installer", "Ready for installation": "<PERSON>r<PERSON><PERSON> pour l'installation", "Uninstalling...": "Désinstallation...", "Uninstalled": "Désinstallé", "Create Entity": "Créer une Fonctionnalité", "Edit Entity": "Modifier une Fonctionnalités", "Create Link": "<PERSON><PERSON><PERSON>", "Edit Link": "<PERSON><PERSON> <PERSON>", "Jobs": "<PERSON><PERSON><PERSON><PERSON>", "Reset to Default": "Valeurs par défaut", "Email Filters": "Filtres Email", "Action History": "Historique Action", "Auth Log": "Journal d'authentification", "Lead Capture": "Capture de prospects", "Attachments": "Les pièces jointes", "API Users": "Utilisateurs d'API", "Template Manager": "Gestionnaire de modèles", "System Requirements": "Configuration requise", "PHP Settings": "Paramètres PHP", "Database Settings": "Paramètres de la base de données", "Permissions": "Les permissions", "Success": "Su<PERSON>ès", "Fail": "<PERSON><PERSON><PERSON>", "is recommended": "est recommandé", "extension is missing": "l'extension est manquante", "PDF Templates": "<PERSON>d<PERSON><PERSON> PDF", "Dashboard Templates": "<PERSON><PERSON><PERSON><PERSON> tableau de bord", "Email Addresses": "Adresses email", "Phone Numbers": "Numéros de téléphone", "Layout Sets": "Modèles de Disposition d'écrans", "Messaging": "Messageries", "Misc": "Divers", "Job Settings": "Paramètres des tâches", "Configuration Instructions": "Instructions de configuration", "Formula Sandbox": "Bac à sable de formule", "Working Time Calendars": "Calendriers des jours travaillés", "Group Email Folders": "Dossiers eMail du groupe", "Authentication Providers": "Prestataires d'authentification", "Setup": "Installation", "App Log": "Journal de l'application", "Address Countries": "<PERSON><PERSON><PERSON> Pays", "App Secrets": "Secrets d'application", "OAuth Providers": "Prestataires OAuth"}, "layouts": {"list": "Liste", "detail": "Détail", "listSmall": "Liste (Réduite)", "detailSmall": "Détail (Réduit)", "filters": "Filtres de recherche", "massUpdate": "Mise à jour groupée", "relationships": "Volets de Relations", "sidePanelsDetail": "Volets latéraux (Detail)", "sidePanelsEdit": "Volets latéraux (Edit)", "sidePanelsDetailSmall": "Volets latéraux (Detail Small)", "sidePanelsEditSmall": "<PERSON>ets latéraux (Edit Small)", "detailPortal": "<PERSON><PERSON><PERSON> (portail)", "detailSmallPortal": "Détail (petit, portail)", "listSmallPortal": "Liste (petit, portail)", "listPortal": "Liste (portail)", "relationshipsPortal": "Volets de Relations (portail)", "defaultSidePanel": "Champs du volet latéral", "bottomPanelsDetail": "Volets inférieurs", "bottomPanelsEdit": "Volets inférieurs (modification)", "bottomPanelsDetailSmall": "Volets inférieurs (détails réduits)", "bottomPanelsEditSmall": "Volets inférieurs (modification réduite)"}, "fieldTypes": {"address": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON>", "foreign": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "password": "Mot de passe", "personName": "Nom", "autoincrement": "Auto-incrément", "bool": "Case à cocher", "currency": "<PERSON><PERSON>", "enum": "Énumération", "enumInt": "<PERSON><PERSON>", "enumFloat": "Enum Flottant", "float": "Flottant", "linkMultiple": "<PERSON><PERSON>", "linkParent": "<PERSON><PERSON>", "phone": "Téléphone", "text": "Texte", "varchar": "Phrase", "file": "<PERSON><PERSON><PERSON>", "multiEnum": "Multi-Enumération", "attachmentMultiple": "Pièces jointes multiples", "rangeInt": "Plage d'entiers", "rangeFloat": "Intervalle réel", "rangeCurrency": "Plage de devises", "map": "<PERSON><PERSON>", "int": "Int", "number": "Number", "jsonObject": "Ob<PERSON>", "datetime": "Date-heure", "datetimeOptional": "Date / Date-<PERSON><PERSON>", "checklist": "Liste de contrôle", "linkOne": "<PERSON><PERSON>", "barcode": "Code barre", "urlMultiple": "URL Multiple\n"}, "fields": {"name": "Nom", "label": "Libellé", "required": "Requis", "default": "<PERSON><PERSON> <PERSON><PERSON>", "maxLength": "Longueur max", "after": "<PERSON><PERSON> (le champ)", "before": "Avant (le champ)", "link": "<PERSON><PERSON>", "field": "<PERSON><PERSON>", "translation": "Traduction", "previewSize": "Prévisualiser la taille", "defaultType": "Type par défaut", "seeMoreDisabled": "Désactiver l'abréviation de texte", "entityList": "Liste des entités", "isSorted": "Trié (alphabétique)", "audited": "Audité", "trim": "<PERSON><PERSON><PERSON>", "height": "<PERSON><PERSON> (px)", "minHeight": "Hauteur min. (px)", "provider": "Presta<PERSON>", "typeList": "Liste de types", "lengthOfCut": "Longueur de coupe", "sourceList": "Liste des sources", "tooltipText": "Info-bulle", "dynamicLogicVisible": "Conditions rendant le champ visible\n", "dynamicLogicReadOnly": "Conditions mettant le champ en lecture seule\n", "dynamicLogicRequired": "Conditions rendant ce champ obligatoire", "readOnly": "Lecture seule", "maxFileSize": "<PERSON>lle maximale du fi<PERSON> (Mo)", "isPersonalData": "Est-ce une donnée personnelle", "useIframe": "<PERSON><PERSON><PERSON><PERSON>", "useNumericFormat": "Utiliser le format numérique", "strip": "Bande", "cutHeight": "Hauteur de coupe (px)", "inlineEditDisabled": "Désactiver la modification en ligne", "displayAsLabel": "Afficher comme étiquette", "allowCustomOptions": "Autoriser les options personnalisées", "maxCount": "Nombre d'éléments maximum", "displayRawText": "Affiche<PERSON> le texte brut (pas de démarques)", "notActualOptions": "Options non réelles", "accept": "<PERSON><PERSON><PERSON><PERSON>", "displayAsList": "Afficher en Liste", "viewMap": "Bouton Voir la carte", "codeType": "Code", "lastChar": "<PERSON><PERSON>", "listPreviewSize": "Taille de l'aperçu en mode Liste", "onlyDefaultCurrency": "Seule devise par défaut", "dynamicLogicInvalid": "Conditions rendant le champ invalide", "conversionDisabled": "Désactiver la conversion", "decimalPlaces": "<PERSON><PERSON><PERSON>", "pattern": "<PERSON><PERSON><PERSON><PERSON>", "globalRestrictions": "Restrictions générales", "decimal": "Décimal", "optionsReference": "Référence des options", "copyToClipboard": "Bouton copier dans le presse-papiers", "rows": "Nombre maximum de lignes", "readOnlyAfterCreate": "Lecture seule après la création", "createButton": "Bouton de création", "autocompleteOnEmpty": "Autocomplétion en cas d'entrée vide", "relateOnImport": "Relier à l'importation", "aclScope": "Champ d'application ACL", "onlyAdmin": "Uniquement pour l'administrateur", "activeOptions": "Options actives", "labelType": "Type d'étiquette", "preview": "<PERSON><PERSON><PERSON><PERSON>", "attachmentField": "Champ de pièce jointe", "dynamicLogicReadOnlySaved": "Conditions d'état sauvegardé rendant le champ en lecture seule", "notStorable": "Non stockable"}, "messages": {"selectEntityType": "Sélectionner le type de Fonctionnalité dans le menu de gauche.", "selectUpgradePackage": "Sélectionner le pack de mise à jour", "selectLayout": "Sélectionnez le modèle dans le menu de gauche et modifiez-le.", "selectExtensionPackage": "Sélectionner un pack d'extension", "extensionInstalled": "L'extension {name} {version} a été installé.", "installExtension": "L'extension {name} {version} est prête pour l'installation.", "upgradeBackup": "Nous vous recommandons de faire une sauvegarde de vos fichiers et données d'EspoCRM avant de procéder à la mise à jour.", "uninstallConfirmation": "Êtes-vous sûr de vouloir désinstaller l'extension?", "cronIsNotConfigured": "Les travaux planifiés ne sont pas en cours d'exécution. Par conséquent, les courriels entrants, les notifications et les rappels ne fonctionnent pas. Suivez les [instructions](https://www.espocrm.com/documentation/administration/server-configuration/#user-content-setup-a-crontab) pour configurer le travail cron.", "newExtensionVersionIsAvailable": "La nouvelle version de {extensionName} {latestVersion} est disponible.", "upgradeVersion": "EspoCRM sera mis à niveau vers la version ** {version} **. S'il vous plaît soyez patient car cela peut prendre un certain temps.", "upgradeDone": "EspoCRM a été mis à niveau vers la version ** {version} **.", "downloadUpgradePackage": "Téléchargez le (s) package (s) de mise à niveau [ici]({url}).", "upgradeInfo": "Consultez la [documentation]({url}) sur la mise à niveau de votre instance EspoCRM.", "upgradeRecommendation": "Cette méthode de mise à niveau n'est pas recommandée. Il est préférable de mettre à niveau à partir de CLI.", "newVersionIsAvailable": "Nouvelle version de EspoCRM {latestVersion} disponible. Veuillez suivre les [instructions](https://www.espocrm.com/documentation/administration/upgrading/) pour mettre à jour votre instance.", "formulaFunctions": "Plus de fonctions peuvent être trouvées dans  [documentation]({documentationUrl}).", "rebuildRequired": "<PERSON><PERSON> <PERSON> exécuter la reconstruction à partir de la CLI.", "cronIsDisabled": "<PERSON>ron est désactivé, l'application n'est pas entièrement fonctionnelle. Activez le cron dans les [settings] (#Admin/settings).", "cacheIsDisabled": "Si le cache est désactivé, l'application fonctionnera lentement. Activez le cache dans les [settings] (#Admin/settings)."}, "descriptions": {"settings": "Paramètres système de l'application.", "scheduledJob": "Tâches qui sont exécutées avec le cron.", "upgrade": "Mettre à jour le système.", "clearCache": "Vider tout le cache.", "rebuild": "Reconstruire et vider tout le cache.", "users": "Gestion des utilisateurs.", "teams": "Gestion des équipes.", "roles": "Gestion des rôles.", "portals": "Gestion des portails", "portalRoles": "Rôles du portail", "outboundEmails": "Paramètres SMTP pour les eMails sortants.", "groupEmailAccounts": "Comptes email IMAP communs", "personalEmailAccounts": "Comptes email utilisateurs", "emailTemplates": "Modèles pour les emails sortants.", "import": "Importer les données d'un fichier CSV.", "layoutManager": "Personnalisation des interfaces (listes, détails, édition, recherche, mise à jour groupée).", "userInterface": "Configurer l'interface utilisateur.", "authTokens": "Auth sessions actives. Adresse IP et date du dernier accès.", "authentication": "Paramètres d'authentification.", "currency": "Réglages des devises et des taux.", "extensions": "Installer ou désinstaller des extensions.", "integrations": "Intégration avec des services tiers.", "notifications": "Paramètres des notifications emails et internes au CRM.", "inboundEmails": "Group IMAP email accouts. Email import and Email-to-Case.", "entityManager": "Créer et modifier les Fonctionnalités personnalisées. Organiser les champs et leurs relations.", "authLog": "Historique de connexion.", "attachments": "Toutes les pièces jointes stockées dans le système.", "templateManager": "Personnaliser les modèles de message.", "systemRequirements": "Configuration système requise pour EspoCRM.", "apiUsers": "<PERSON><PERSON><PERSON>er les utilisateurs à des fins d'intégration.", "jobs": "Les tâches exécutent des tâches en arrière-plan.", "pdfTemplates": "Modèles pour l'impression au format PDF.", "webhooks": "<PERSON><PERSON><PERSON> les webhooks.", "dashboardTemplates": "Déployer des tableaux de bord pour les utilisateurs.", "phoneNumbers": "Tous les numéros de téléphone stocké dans le système.", "emailAddresses": "Toutes les adresses e-mail stockées dans le système.", "layoutSets": "Collections de thèmes pouvant être attribuées aux équipes et aux portails.", "jobsSettings": "Paramètres de traitement des travaux. Les travaux exécutent des tâches en arrière-plan.", "sms": "Paramètres des SMS.", "formulaSandbox": "Écrire et tester des scripts de formule.", "workingTimeCalendars": "Temps de travail", "groupEmailFolders": "Dossiers de courrier électronique partagés par les équipes.", "authenticationProviders": "Fournisseurs d'authentification supplémentaires pour les portails.", "appLog": "Journal d'application.", "addressCountries": "Pays disponibles pour les champs d'adresse.", "appSecrets": "Conserver les informations sensibles tels que clés API et mots de passe.", "leadCapture": "Formulaires web et endpoints pour la génération de leads.", "oAuthProviders": "Fournisseurs OAuth pour les intégrations."}, "options": {"previewSize": {"x-small": "<PERSON><PERSON><PERSON> petit", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "": "Défaut"}, "labelType": {"regular": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "systemRequirements": {"requiredPhpVersion": "Version PHP", "requiredMysqlVersion": "Version MySQL", "host": "Nom d'hôte", "dbname": "Nom de la base de données", "user": "Nom d'utilisateur", "writable": "Enregistrable", "readable": "Lisible", "requiredMariadbVersion": "Version de MariaDB", "requiredPostgresqlVersion": "Version PostgreSQL"}, "templates": {"accessInfo": "Informations d'accès", "accessInfoPortal": "Informations d'accès pour les portails", "assignment": "Affectation", "notePost": "Note sur le post", "notePostNoParent": "Note sur Post (pas de parent)", "noteStatus": "Note sur la mise à jour du statut", "passwordChangeLink": "Lien de changement de mot de passe", "noteEmailReceived": "Note à propos de l'Email reçu", "twoFactorCode": "Code 2 facteurs (2FA)"}, "strings": {"rebuildRequired": "Une reconstruction est obligatoire"}, "keywords": {"settings": "système", "userInterface": "ui,thème,onglets,logo,tableau de bord", "scheduledJob": "tâches récurrentes (cron)", "authLog": "journal,historique", "authTokens": "historique,accès,journal", "entityManager": "champs,relations,liens", "jobs": "<PERSON><PERSON>", "authentication": "mot de passe,sécurité,ldap", "labelManager": "langue,traduction", "appSecrets": "clé, clés, mot de passe", "leadCapture": "formulaires web"}, "tooltips": {"tabUrl": "Peut commencer par `#` pour naviguer vers une page d'application.", "tabUrlAclScope": "L'onglet sera disponible pour les utilisateurs qui ont accès à l'étendue spécifiée."}}