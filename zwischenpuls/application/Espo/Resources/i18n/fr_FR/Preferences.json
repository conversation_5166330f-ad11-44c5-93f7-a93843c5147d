{"fields": {"dateFormat": "Format de la date", "timeFormat": "Format de l'heure", "timeZone": "<PERSON><PERSON> ho<PERSON>", "weekStart": "Premier j<PERSON> de la semaine", "thousandSeparator": "Séparateur de milliers", "decimalMark": "Symbole décimal", "defaultCurrency": "<PERSON><PERSON> par défaut", "currencyList": "Liste des devises", "language": "Langage", "exportDelimiter": "Délimitant pour l'export", "signature": "Signature email", "dashboardTabList": "Liste des onglets", "tabList": "Liste des onglets", "defaultReminders": "Notifications par défaut", "theme": "Thème", "useCustomTabList": "Liste d'onglets personnalisé", "dashboardLayout": "Thème du tableau de bord", "emailReplyForceHtml": "Réponse au format HTML", "autoFollowEntityTypeList": "Suivi automatique global", "emailReplyToAllByDefault": "Ré<PERSON>nd<PERSON> à tous par défaut", "doNotFillAssignedUserIfNotRequired": "Désactiver le préremplissage de l’utilisateur assigné", "followEntityOnStreamPost": "Suivi auto après publication dans le flux", "followCreatedEntities": "Suivi automatique des enregistrements créés", "followCreatedEntityTypeList": "Suivre automatiquement les éléments créés parmi les Fonctionnalités sélectionnées", "emailUseExternalClient": "Utiliser un client de messagerie externe", "assignmentNotificationsIgnoreEntityTypeList": "Notifications d'affectation dans l'application", "assignmentEmailNotificationsIgnoreEntityTypeList": "Notifications d'attribution de courrier électronique", "dashboardLocked": "Verrouiller le tableau de bord", "textSearchStoringDisabled": "Désactiver le stockage du filtre de texte", "calendarSlotDuration": "Du<PERSON>e du créneau du calendrier", "calendarScrollHour": "Faire défiler le calendrier jusqu'à l'heure\n", "defaultRemindersTask": "Rappels par défaut pour les tâches", "addCustomTabs": "Garder les éléments par défaut", "reactionNotifications": "Notifications de réactions dans l'application", "pageContentWidth": "<PERSON>ur du contenu"}, "options": {"weekStart": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON>"}, "pageContentWidth": {"Wide": "Large"}}, "labels": {"User Interface": "Interface utilisateurs", "Misc": "Divers", "Locale": "Région", "Reset Dashboard to Default": "Réinitialiser le tableau de bord par défaut"}, "tooltips": {"autoFollowEntityTypeList": "Suivre automatiquement tous les nouveaux enregistrements (créés par n’importe quel utilisateur) des Fonctionnalités sélectionnés. Permet de les voir dans le flux et de recevoir des notifications sur tous les enregistrements.", "doNotFillAssignedUserIfNotRequired": "Lors de la création d'un enregistrement, l'utilisateur attribué ne sera pas renseigné avec son propre utilisateur, sauf si le champ est obligatoire.", "followCreatedEntities": "Lors de la création de nouveaux enregistrements, ils seront automatiquement suivis même s'ils sont attribués à un autre utilisateur.", "followCreatedEntityTypeList": "Vous suivrez automatiquement les éléments que vous créerez s'ils font partie de votre sélection de Fonctionnalités, même s’ils sont attribués à un autre utilisateur.", "addCustomTabs": "Si cette option est cochée, les onglets personnalisés seront ajoutés au menu par défaut. Sinon, seuls les onglets personnalisés seront affichés."}, "tabFields": {"label": "Étiquette", "iconClass": "Icône", "color": "<PERSON><PERSON><PERSON>"}}