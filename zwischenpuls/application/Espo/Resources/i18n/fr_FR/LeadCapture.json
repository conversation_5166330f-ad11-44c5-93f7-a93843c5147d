{"fields": {"name": "Nom", "campaign": "Campagne", "isActive": "Actif", "subscribeToTargetList": "S'abonner à la liste des cibles", "subscribeContactToTargetList": "S'abonner Contact si existe", "targetList": "Liste de cibles", "fieldList": "Champs de charge utile", "optInConfirmation": "Double opt-in", "optInConfirmationEmailTemplate": "Modèle d'e-mail de confirmation d'adhésion", "optInConfirmationLifetime": "Opt-in confirmation à vie (heures)", "optInConfirmationSuccessMessage": "Texte à afficher après la confirmation d'adhésion", "leadSource": "Source principale", "apiKey": "clé API", "targetTeam": "Équipe c<PERSON>", "exampleRequestMethod": "Méthode", "exampleRequestPayload": "Charge utile", "createLeadBeforeOptInConfirmation": "<PERSON><PERSON>er un prospect avant confirmation", "duplicateCheck": "Contr<PERSON>le en double", "skipOptInConfirmationIfSubscribed": "Ignorer la confirmation si le prospect est déjà dans la liste des cibles", "smtpAccount": "Compte SMTP", "inboundEmail": "Compte de messagerie de groupe", "exampleRequestHeaders": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumberCountry": "Indicatif téléphonique national", "fieldParams": "Paramètres du champ", "formId": "Formulaire ID", "formEnabled": "Formulaire Web", "formUrl": "URL du formulaire", "formSuccessText": "Texte à afficher après la soumission du formulaire", "formText": "Texte à afficher sur le formulaire", "formSuccessRedirectUrl": "URL à rediriger après la soumission du formulaire", "formLanguage": "Langue utilisée dans le formulaire", "formFrameAncestors": "Hôtes autorisés pour l'intégration de formulaires", "formCaptcha": "Utiliser <PERSON>", "formTitle": "Titre du formulaire", "formTheme": "Thème du formulaire"}, "links": {"targetList": "Liste de cibles", "campaign": "Campagne", "optInConfirmationEmailTemplate": "Modèle d'e-mail de confirmation d'adhésion", "targetTeam": "Équipe c<PERSON>", "logRecords": "Bûche", "inboundEmail": "Compte de messagerie de groupe"}, "labels": {"Create LeadCapture": "<PERSON><PERSON>er un point d'entrée", "Generate New API Key": "Générer une nouvelle clé API", "Request": "<PERSON><PERSON><PERSON>", "Confirm Opt-In": "Confirmer la <PERSON>", "Generate New Form ID": "Générer un nouvel ID de formulaire", "Web Form": "Formulaire Web"}, "messages": {"generateApiKey": "<PERSON><PERSON>er une nouvelle clé API", "optInConfirmationExpired": "Le lien de confirmation d'adhésion a expiré.", "optInIsConfirmed": "L'inscription est confirmée."}, "tooltips": {"optInConfirmationSuccessMessage": "Markdown est pris en charge.", "formCaptcha": "Pour pouvoir utiliser <PERSON><PERSON>, vous devez le configurer sous **Administration** > **Intégrations**."}}