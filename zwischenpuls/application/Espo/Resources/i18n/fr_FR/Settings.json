{"fields": {"useCache": "Utiliser la mise en cache", "dateFormat": "Format de la date", "timeFormat": "Format horaire", "timeZone": "<PERSON><PERSON> ho<PERSON>", "weekStart": "Premier j<PERSON> de la semaine", "thousandSeparator": "Séparateur de millier", "decimalMark": "Symbole décimal", "defaultCurrency": "<PERSON><PERSON> par défaut", "baseCurrency": "Devise de <PERSON>", "currencyRates": "Taux des devises", "currencyList": "Liste des devises", "language": "<PERSON><PERSON>", "companyLogo": "Logo", "ldapSecurity": "Sécurité", "ldapPassword": "Mot de passe", "outboundEmailFromName": "De", "outboundEmailIsShared": "Est partagé", "recordsPerPage": "Enregistrements par page", "recordsPerPageSmall": "Enregistrements par page (réduits)", "tabList": "Liste des onglets", "quickCreateList": "Liste des créations rapides", "exportDelimiter": "Délimitant en exportation", "globalSearchEntityList": "Liste des Fonctionnalités pour la Recherche globale", "authenticationMethod": "Méthode d'authentification", "ldapHost": "<PERSON><PERSON><PERSON>", "ldapAccountCanonicalForm": "Compte forme canonique", "ldapAccountDomainName": "Compte du nom de domaine", "ldapCreateEspoUser": "Créer un utilisateur dans EspoCRM", "ldapUserLoginFilter": "Filtre des connexions utilisateur", "ldapAccountDomainNameShort": "Compte du nom de domaine réduit", "ldapOptReferrals": "Abonnés <PERSON>", "exportDisabled": "Désactiver l'Export (seul l'administrateur pourra le faire)", "b2cMode": "Mode B2C", "avatarsDisabled": "Désactiver les avatars", "displayListViewRecordCount": "Afficher le Nombre Total (listes)", "theme": "Thème", "userThemesDisabled": "Désactiver les thèmes utilisateur", "emailMessageMaxSize": "<PERSON>lle maximale d'Emails (Mo)", "dashboardLayout": "Thème de Tableau de bords (défaut)", "siteUrl": "URL du site", "addressPreview": "Aper<PERSON><PERSON> d'adresse", "addressFormat": "Format d'adresse", "applicationName": "Nom de l'application", "ldapUserPhoneNumberAttribute": "Attribut du numéro de téléphone de l'utilisateur", "assignmentNotificationsEntityList": "Fonctionnalités à notifier lors de l'attribution", "calendarEntityList": "<PERSON>e Cale<PERSON>rier", "activitiesEntityList": "Liste Activités", "historyEntityList": "Liste Historique", "currencyDecimalPlaces": "Nombre de décimale courant", "followCreatedEntities": "Suivi les enregistrements créés", "aclAllowDeleteCreated": "Autoriser la suppression des enregistrements créés", "adminNotifications": "Notifications du système dans le volet Administration", "adminNotificationsNewVersion": "Afficher la notification lorsque la nouvelle version d'EspoCRM est disponible", "massEmailMaxPerHourCount": "Nombre maximum d'Emails par heure", "maxEmailAccountCount": "Nombre maximum de comptes Emails personnels par utilisateur", "streamEmailNotificationsTypeList": "Ce qu'il faut signaler", "authTokenPreventConcurrent": "Un seul jeton d'authentification par utilisateur", "scopeColorsDisabled": "Désactiver les couleurs de la portée", "tabColorsDisabled": "Désactiver les couleurs des onglets", "tabIconsDisabled": "Désactiver les icônes d'onglets", "textFilterUseContainsForVarchar": "Utilisez l'opérateur 'contient' lors du filtrage des champs varchar", "emailAddressIsOptedOutByDefault": "Marquer les nouvelles adresses e-mail comme désactivées", "outboundEmailBccAddress": "Adresse BCC pour les clients externes", "adminNotificationsNewExtensionVersion": "Afficher la notification lorsque de nouvelles versions d'extensions sont disponibles", "cleanupDeletedRecords": "Nettoyer les enregistrements supprimés", "ldapPortalUserLdapAuth": "Utiliser l'authentification LDAP pour les utilisateurs du portail", "ldapPortalUserPortals": "Portails par défaut pour un utilisateur du portail", "ldapPortalUserRoles": "Rôles par défaut pour un utilisateur du portail", "fiscalYearShift": "Année fiscale début", "jobRunInParallel": "Travaux exécutés en parallèle", "jobMaxPortion": "Emplois Portion Max", "jobPoolConcurrencyNumber": "Numéro d'accès simultané au pool d'emplois", "daemonInterval": "Intervalle de processus", "daemonMaxProcessNumber": "Nombre de processus (démon) Max", "daemonProcessTimeout": "<PERSON><PERSON><PERSON> de traitement du processus", "addressCityList": "Adresse Ville Liste de saisie semi-automatique", "addressStateList": "Liste de complétion automatique d'état d'adresse", "cronDisabled": "<PERSON><PERSON><PERSON><PERSON>", "maintenanceMode": "Mode de Maintenance", "useWebSocket": "Utiliser WebSocket", "emailNotificationsDelay": "<PERSON><PERSON>lai de notification par courrier électronique (en secondes)", "massEmailOpenTracking": "Suivi des e-mails ouverts", "passwordRecoveryDisabled": "Désactiver la récupération du mot de passe", "passwordRecoveryForAdminDisabled": "Désactiver la récupération de mot de passe pour les utilisateurs administrateurs", "passwordGenerateLength": "Longueur des mots de passe générés", "passwordStrengthLength": "Longueur minimale du mot de passe", "passwordStrengthLetterCount": "Nombre de lettres requises dans le mot de passe", "passwordStrengthNumberCount": "Nombre de chiffres requis dans le mot de passe", "passwordStrengthBothCases": "Le mot de passe doit contenir des lettres en majuscule et en minuscule", "auth2FA": "Activer l'authentification à 2 facteurs", "auth2FAMethodList": "Méthodes 2FA disponibles", "personNameFormat": "Format du nom", "newNotificationCountInTitle": "Afficher le nombre de nouvelles notifications dans le titre de la page web", "massEmailVerp": "Utiliser VERP", "emailAddressLookupEntityTypeList": "Étendues de recherche d'adresse eMail", "busyRangesEntityList": "Liste de Fonctionnalités libres/inactives", "passwordRecoveryForInternalUsersDisabled": "Désactiver la récupération de mot de passe pour les utilisateurs internes", "passwordRecoveryNoExposure": "Évi<PERSON><PERSON> d'afficher l'adresse courriel sur le formulaire de récupération de mot de passe", "auth2FAForced": "Forcer les utilisateurs réguliers à utiliser l'authentification à deux facteurs", "smsProvider": "Opérateur SMS", "outboundSmsFromNumber": "Numéro du <PERSON>", "recordsPerPageSelect": "Enregistrement par page (Select)", "attachmentUploadMaxSize": "<PERSON><PERSON> chargée (Mo)", "attachmentUploadChunkSize": "<PERSON>lle du morceau à télécharger (Mb)", "workingTimeCalendar": "Calendriers des jours travaillés", "oidcClientId": "ID du client de l'OIDC", "oidcClientSecret": "Secret client de l'OIDC", "oidcAuthorizationRedirectUri": "URI de redirection d'autorisation OIDC", "oidcAuthorizationEndpoint": "Point final d'autorisation OIDC", "oidcTokenEndpoint": "Point final du jeton OIDC", "oidcJwksEndpoint": "Point final set de clés Web JSON de l'OIDC", "oidcJwtSignatureAlgorithmList": "Algorithmes de signature OIDC JWT autorisés", "oidcScopes": "Champ d'application de l'OIDC", "oidcGroupClaim": "Réclamation du groupe de l'OIDC", "oidcCreateUser": "OIDC Créer un utilisateur", "oidcUsernameClaim": "Nom d'utilisateur OIDC Réclamation", "oidcTeams": "Équipes OIDC", "oidcSync": "Synchronisation OIDC", "oidcSyncTeams": "Équipes OIDC Sync", "oidcFallback": "Connexion de repli de l'OIDC", "oidcAllowRegularUserFallback": "OIDC Autoriser la connexion de repli pour les utilisateurs réguliers", "oidcAllowAdminUser": "OIDC Autoriser la connexion à l'OIDC pour les utilisateurs administrateurs", "oidcLogoutUrl": "URL de déconnexion de l'OIDC", "pdfEngine": "Moteur PDF", "recordsPerPageKanban": "Enregistrement par page (Kanban)", "auth2FAInPortal": "Autoriser le 2FA dans les portails", "massEmailMaxPerBatchCount": "Nombre maximum d'e-mails envoyés par lot", "phoneNumberNumericSearch": "Recherche de numéro de téléphone", "phoneNumberInternational": "Numéro de téléphone international", "phoneNumberPreferredCountryList": "Indicatifs téléphoniques des pays préférés", "jobForceUtc": "Impose la zone UTC", "emailAddressSelectEntityTypeList": "Adresse électronique sélectionner les champs d'application", "phoneNumberExtensions": "Extensions du numéro de téléphone", "oidcAuthorizationPrompt": "Invite d'autorisation de l'OIDC", "quickSearchFullTextAppendWildcard": "Ajouter un joker dans la recherche rapide", "authIpAddressCheck": "Restreindre l'accès par l'adresse IP", "authIpAddressWhitelist": "Liste blanche d'adresses IP", "authIpAddressCheckExcludedUsers": "Utilisateurs exclus de la vérification", "streamEmailWithContentEntityTypeList": "Fonctionnalités affichant le contenu des emails dans le flux", "emailScheduledBatchCount": "Nombre maximal d'e-mails programmés envoyés par lot", "passwordStrengthSpecialCharacterCount": "Nombre de caractères spéciaux requis dans le mot de passe", "availableReactions": "Réactions disponibles", "outboundEmailFromAddress": "Adresse e-mail du système", "oidcUserInfoEndpoint": "Point final OIDC UserInfo"}, "tooltips": {"recordsPerPageSmall": "Nombre d'enregistrements dans les volets Relations.", "followCreatedEntities": "Les utilisateurs suivront automatiquement leurs entrées", "userThemesDisabled": "Si coché alors les utilisateurs ne pourront pas sélectionner un autre thème", "ldapUsername": "Le DN complet de l'utilisateur du système qui permet de rechercher d'autres utilisateurs. Par exemple \"CN=LDAP System User,OU=users,OU=espocrm, DC=test,DC=lan\".", "ldapPassword": "Le mot de passe pour accéder au serveur LDAP.", "ldapAuth": "Accédez aux informations d'identification du serveur LDAP.", "ldapUserNameAttribute": "Attribut permettant d'identifier l'utilisateur. \nPar exemple, « userPrincipalName » ou « sAMAccountName » pour Active Directory, « uid » pour OpenLDAP.", "ldapOptReferrals": "si les références doivent être suivies vers le client LDAP.", "ldapCreateEspoUser": "Cette option permet à EspoCRM de créer un utilisateur à partir du LDAP.", "ldapUserFirstNameAttribute": "Attribut LDAP qui permet de déterminer le prénom de l'utilisateur. Par exemple. \"givenname\".", "ldapUserLastNameAttribute": "Attribut LDAP utilisé pour déterminer le nom de famille de l'utilisateur. Par exemple. \"sn\".", "ldapUserTitleAttribute": "Attribut LDAP utilisé pour déterminer le titre de l'utilisateur. Par exemple. \"title\".", "ldapUserEmailAddressAttribute": "Attribut LDAP utilisé pour déterminer l'adresse e-mail de l'utilisateur. Par exemple. \"mail\".", "ldapUserPhoneNumberAttribute": "Attribut LDAP utilisé pour déterminer le numéro de téléphone de l'utilisateur. Par exple. \"numéro de téléphone\".", "ldapAccountDomainName": "Le domaine utilisé pour l'autorisation sur le serveur LDAP.", "ldapAccountDomainNameShort": "Le domaine court utilisé pour l'autorisation sur le serveur LDAP.", "b2cMode": "<PERSON><PERSON> <PERSON><PERSON>, EspoCRM est adapté au B2B. Vous pouvez le passer en B2C.", "aclAllowDeleteCreated": "Les utilisateurs pourront supprimer les enregistrements qu'ils ont créés même s'ils ne disposent pas de droits en suppression.", "textFilterUseContainsForVarchar": "Si cette case n'est pas cochée, l'opérateur \"commence par\" est utilisé. Vous pouvez utiliser le caractère générique '%'.", "streamEmailNotificationsEntityList": "Notifications par courriel des mises à jour de flux des éléments suivis. Uniquement pour les Fonctionnalités spécifiées.", "authTokenPreventConcurrent": "Les utilisateurs ne pourront pas se connecter simultanément sur plusieurs appareils.", "cleanupDeletedRecords": "Les enregistrements supprimés seront supprimés de la base de données après un certain temps.", "ldapPortalUserLdapAuth": "Autoriser les utilisateurs du portail à utiliser l'authentification LDAP au lieu de l'authentification EspoCRM.", "ldapPortalUserPortals": "Portails par défaut pour l'utilisateur de portail créé", "ldapPortalUserRoles": "Rôles par défaut pour l'utilisateur de portail créé", "jobRunInParallel": "Les travaux seront exécutés dans des processus parallèles.", "jobPoolConcurrencyNumber": "Nombre maximal de processus exécutés simultanément.", "jobMaxPortion": "Nombre maximal de travaux traités par exécution.", "daemonInterval": "L'intervalle entre les processus cron s'exécute en secondes.", "daemonMaxProcessNumber": "Nombre maximal de processus cron exécutés simultanément.", "daemonProcessTimeout": "Temps d'exécution maximal (en secondes) alloué pour un seul processus cron.", "cronDisabled": "<PERSON><PERSON> ne s'executera pas.", "maintenanceMode": "Seuls les administrateurs auront accès au système.", "ldapAccountCanonicalForm": "Le type de votre compte forme canonique. Il y a 4 options: \n\n- 'Dn' - le formulaire au format 'CN = testeur, OU = espocr, DC = test, DC = lan'. \n\n- 'Nom d'utilisateur' - le formulaire 'testeur '. \n\n-' Barre oblique inverse '- le formulaire' ENTREPRISE \\ testeur '. \n\n-' Principal '- le formulaire' <EMAIL> '.", "massEmailVerp": "Méthode de l'adresse de retour variable (VERP). Pour une meilleure gestion des messages renvoyés. Assurez-vous que votre fournisseur SMTP le prend en charge.", "displayListViewRecordCount": "Le nombre total d'éléments sera affiché dans les affichages en Liste.", "currencyList": "Devises disponibles dans le système.", "activitiesEntityList": "Éléments disponibles dans le volet Activités.", "historyEntityList": "Éléments disponibles dans le volet Historique.", "calendarEntityList": "Éléments disponibles dans le Calendrier.", "addressStateList": "Suggestions de Régions pour les champs d'adresse.", "addressCityList": "Suggestions de Villes pour les champs d'adresse. ", "addressCountryList": "Suggestions de Pays pour les champs d'adresse. ", "exportDisabled": "Les utilisateurs ne pourront pas faire d'export. L'export sera réservé aux administrateurs.", "globalSearchEntityList": "Éléments trouvables grâce à la Recherche globale.", "siteUrl": "Lien vers cette installation d'EspoCRM. Vous devrez le modifier si vous souhaitez migrer vers un autre domaine.", "useCache": "Gardez cette option activée, sauf pour des motifs de développement.", "useWebSocket": "WebSocket permet une communication interactive bidirectionnelle entre un serveur et un navigateur. Nécessite la configuration du processus (démon) WebSocket sur votre serveur. Consultez la documentation pour plus d'informations.", "passwordRecoveryForInternalUsersDisabled": "Seuls les utilisateurs Portail pourront récupérer leur mot de passe.", "passwordRecoveryNoExposure": "Il ne sera pas possible de déterminer si une adresse courriel spécifique est enregistrée dans le système.", "emailAddressLookupEntityTypeList": "Pour la saisie semi-automatique de l’adresse e-mail.", "emailNotificationsDelay": "Un message peut être modifié dans le délai spécifié avant l'envoi de la notification.", "busyRangesEntityList": "Ce qui sera pris en compte lors de l'affichage des plages horaires occupées dans le planificateur et la chronologie.", "recordsPerPageSelect": "Nombre d'enregistrements initialement affichés lors de la sélection des enregistrements", "workingTimeCalendar": "Un calendrier des jours ouvrés qui sera appliqué par défaut à tous les utilisateurs.", "oidcFallback": "Autoriser la connexion par login/mot de passe.", "oidcCreateUser": "Créez un nouvel utilisateur dans EspoCRM lorsqu’aucun utilisateur correspondant n’est trouvé.", "oidcSync": "Synchronisez les données utilisateur (à chaque connexion).", "oidcSyncTeams": "Synchronisez les équipes d'utilisateurs (à chaque connexion).", "oidcUsernameClaim": "Une revendication à utiliser pour un nom d'utilisateur (pour la correspondance et la création d'utilisateurs).", "oidcTeams": "Les équipes Espo sont mises en correspondance avec les groupes/équipes/rôles du fournisseur d'identité. Les équipes dont la valeur de mappage est vide seront toujours attribuées à un utilisateur (lors de la création ou de la synchronisation).", "oidcLogoutUrl": "Une URL vers laquelle le navigateur sera redirigé après s'être déconnecté d'EspoCRM. Destiné à effacer les informations de session dans le navigateur et à se déconnecter du côté du fournisseur. Habituellement, l'URL contient un paramètre redirect-URL, pour revenir à EspoCRM.\n\nEspaces réservés disponibles :\n* `{siteUrl}`\n* `{clientId}`", "recordsPerPageKanban": "Nombre d'enregistrements initialement affichés dans les colonnes du Kanban.", "jobForceUtc": "Utilisez le fuseau horaire UTC pour les tâches planifiées. Sinon, le fuseau horaire défini dans les paramètres sera utilisé.", "emailAddressSelectEntityTypeList": "Types d'entités disponibles lors de la recherche d'une adresse e-mail à partir d'une fenêtre modale.", "authIpAddressCheckExcludedUsers": "Utilisateurs qui pourront se connecter, que leur adresse IP figure ou non dans la liste blanche.", "authIpAddressWhitelist": "Une liste d'adresses IP ou de plages d'adresses IP en notation CIDR.\n\nLes portails ne sont pas concernés par cette restriction.", "emailAddressIsOptedOutByDefault": "Lors de la création d'un nouvel enregistrement, l'adresse électronique sera marquée comme opt-out.", "oidcGroupClaim": "Une revendication à utiliser pour la cartographie d'équipe.", "quickSearchFullTextAppendWildcard": "Ajouter un joker (*) à une requête de recherche autocomplète lorsque la recherche en texte intégral est activée. Réduit les performances de la recherche.", "outboundEmailFromAddress": "Les e-mails système seront envoyés depuis cette adresse. Un [compte e-mail du groupe](#InboundEmail) avec la même adresse doit être configuré pour envoyer des e-mails."}, "labels": {"System": "Système", "Locale": "Région", "In-app Notifications": "Notifications internes", "Email Notifications": "Notifications par email", "Currency Settings": "Paramètres de devises", "Currency Rates": "Taux des devises", "Mass Email": "Emails groupés", "Activities": "Activités", "Admin Notifications": "Notifications de l'administrateur", "Search": "<PERSON><PERSON><PERSON>", "Misc": "Divers", "Passwords": "Mots de passe", "2-Factor Authentication": "Authentification à 2 facteurs", "Group Tab": "Onglet de Groupe", "Attachments": "Pièces jointes", "IdP Group": "Groupe IdP", "Divider": "Séparateur", "General": "Général", "Navbar": "Barre de navigation", "Dashboard": "Tableau de bord", "Phone Numbers": "Numéros de téléphone", "Access": "Accès", "Strength": "Force", "Recovery": "Récupération", "Scheduled Send": "Envoi programmé"}, "options": {"streamEmailNotificationsTypeList": {"Post": "<PERSON>", "Status": "Mises à jour de statut", "EmailReceived": "<PERSON><PERSON><PERSON><PERSON>"}, "personNameFormat": {"firstLast": "Prénom Nom", "lastFirst": "Nom Prénom", "firstMiddleLast": "Prénom Second Prénom Nom", "lastFirstMiddle": "Nom Prénom Second Prénom"}, "currencyFormat": {"3": "10 €"}, "auth2FAMethodList": {"Email": "eMail"}}}