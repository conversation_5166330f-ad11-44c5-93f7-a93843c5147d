{"labels": {"Fields": "<PERSON><PERSON>", "Relationships": "Relations", "Schedule": "Emploi du temps", "Formula": "Formule", "Layouts": "Dispositions d'écran", "Parameters": "Paramètres"}, "fields": {"name": "Nom", "labelSingular": "Libellé au singulier", "labelPlural": "Libellé au pluriel", "stream": "Flux", "label": "Libellé", "linkType": "Type de lien", "entityForeign": "Entité Etrangère", "linkForeign": "Lien externe", "link": "<PERSON><PERSON>", "labelForeign": "Libellé externe", "relationName": "Nom de table intermédiaire", "linkMultipleField": "Lier un champ multiple", "linkMultipleFieldForeign": "Lien externe vers un champ multiple", "disabled": "Désactivé", "textFilterFields": "Champs de filtre textuel", "audited": "<PERSON><PERSON><PERSON><PERSON>", "statusField": "Champ d'État", "color": "<PERSON><PERSON><PERSON>", "kanbanViewMode": "<PERSON><PERSON>", "kanbanStatusIgnoreList": "Groupes ignorés dans la vue Kanban", "iconClass": "Icône", "fullTextSearch": "Recherche du texte complet", "countDisabled": "<PERSON><PERSON><PERSON><PERSON> le compteur de lignes", "parentEntityTypeList": "Types de Fonctionnalités parentes", "foreignLinkEntityTypeList": "Liens externes", "entity": "Entité", "optimisticConcurrencyControl": "Contrôle optimiste de la concurrence", "beforeSaveApiScript": "API avant le script d'enregistrement", "updateDuplicateCheck": "Vérification des doublons lors de la mise à jour", "duplicateCheckFieldList": "Champs de contrôle en double", "layout": "Thème", "author": "<PERSON><PERSON><PERSON>", "selectFilter": "Sélectionner un Filtre", "primaryFilters": "Filtres Primaires", "stars": "Etoiles", "preserveAuditLog": "Préserver le journal d'audit", "assignedUsers": "Plusieurs utilisateurs assignés", "collaborators": "Collaborateurs", "aclContactLink": "Lien de contact ACL", "aclAccountLink": "Lien du compte ACL", "sortBy": "Champ d'ordre par défaut", "sortDirection": "Direction de l'ordre par défaut"}, "options": {"type": {"": "Aucun", "Person": "<PERSON><PERSON>", "CategoryTree": "Arborescence des catégories", "Event": "<PERSON><PERSON><PERSON><PERSON>", "Company": "Structure (entreprise, association, ...)"}, "linkType": {"manyToMany": "Plusieurs-à-Plusieurs", "oneToMany": "Un-à-Plusieurs", "manyToOne": "Plusieurs-à-Un", "parentToChildren": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenToParent": "<PERSON><PERSON>-<PERSON><PERSON>", "oneToOneRight": "Un-à-<PERSON> Droit<PERSON>", "oneToOneLeft": "Un-à-Un Gauche"}, "sortDirection": {"asc": "Croissant", "desc": "Décroissant"}, "module": {"Custom": "<PERSON><PERSON><PERSON><PERSON>"}}, "messages": {"entityCreated": "La Fonctionnalités a bien été créée", "linkAlreadyExists": "Conflit : lien déjà existant.", "linkConflict": "Conflit de nom : un lien ou un champ portant ce nom existe déjà.", "confirmRemove": "Êtes-vous sûr de vouloir supprimer ce type de Fonctionnalité du système ?", "beforeSaveCustomScript": "Un script appelé à chaque fois avant la sauvegarde d'une entité. À utiliser pour définir les champs calculés.", "beforeSaveApiScript": "Un script appelé sur les requêtes API de création et de mise à jour avant qu'une entité ne soit sauvegardée. À utiliser pour la validation personnalisée et la vérification des doublons.", "nameIsAlreadyUsed": "Le nom '{name}' est déjà utilisé.", "nameIsNotAllowed": "Le nom '{name}' n'est pas autorisé.", "nameIsTooLong": "Le nom est trop long", "confirmRemoveLink": "Êtes-vous sûr de vouloir supprimer la relation *{link}* ?", "urlHashCopiedToClipboard": "Un fragment d'URL pour le filtre *{name}* est copié dans le presse-papiers. Vous pouvez l'ajouter à la barre de navigation."}, "tooltips": {"statusField": "Les mises à jour de ce champ sont inscrites dans le Flux.", "textFilterFields": "Champs utilisés lors de recherches textuelles.", "disabled": "Vérifiez que vous n'ayez pas besoin de cette Fonctionnalité dans votre système.", "linkAudited": "La création d'un enregistrement lié et la liaison avec l'enregistrement existant seront consignées dans le flux.", "linkMultipleField": "Le champ de Liens multiples est très utile à la création de relations. Mais ne l'utilisez pas si un trop grand nombre d'éléments sont reliés.", "entityType": "Base Plus - ajoute les volets Activités, Historique et Tâches.\n\nÉvènement - est disponible dans les volets Calendrier and Activités.", "fullTextSearch": "Une reconstruction est requise.", "countDisabled": "Le nombre total ne sera pas affiché en vue Liste. Cela peut faire baisser les temps de chargement quand la base de données est grande.", "optimisticConcurrencyControl": "Empêche les conflits d’écriture.", "duplicateCheckFieldList": "Les champs à vérifier lors de la recherche de doublons.", "updateDuplicateCheck": "Vérifier la présence de doublons lors de la mise à jour d'un enregistrement.", "linkSelectFilter": "Un filtre primaire à appliquer par défaut lors de la sélection d'un enregistrement.", "stars": "La possibilité d'attribuer une étoile à un enregistrement. Les étoiles peuvent être utilisées par les utilisateurs pour mettre des enregistrements en favoris.", "aclContactLink": "Le lien avec le contact à utiliser lors de l'application du contrôle d'accès pour les utilisateurs du portail.", "aclAccountLink": "Le lien avec le compte à utiliser pour appliquer le contrôle d'accès aux utilisateurs du portail.", "collaborators": "La possibilité de partager des enregistrements avec des utilisateurs spécifiques.", "assignedUsers": "La possibilité d'affecter plusieurs utilisateurs à un enregistrement.\n\nNotez qu'après l'activation du paramètre, les utilisateurs assignés existants ne seront pas transférés dans le nouveau champ *Utilisateurs assignés*.", "preserveAuditLog": "Désactive le nettoyage du journal d’audit. Ce paramètre s’applique uniquement si le Flux est désactivé, car quand il est activé, les enregistrements d’audit ne sont pas supprimés.", "stream": "Si l'entité dispose du flux.", "linkParamReadOnly": "Un lien en lecture seule ne peut pas être modifié via les requêtes API link et unlink. Il est impossible de lier ou délier des enregistrements via le panneau de relations. Cependant, il reste possible de modifier ces liens via les champs link et link-multiple."}}