{"fields": {"name": "Nom", "accountRole": "Titre", "account": "<PERSON><PERSON><PERSON>", "accounts": "<PERSON><PERSON><PERSON>", "phoneNumber": "Téléphone", "accountType": "Type de compte", "doNotCall": "Ne pas appeler", "address": "<PERSON><PERSON><PERSON>", "opportunityRole": "Rôle d'opportunité", "campaign": "Campagne", "targetLists": "Listes de cibles", "targetList": "Liste de cibles", "portalUser": "Utilisateur Portail", "acceptanceStatus": "Statut d'acceptation", "acceptanceStatusMeetings": "Statut d'acceptation (Rendez-vous)", "acceptanceStatusCalls": "Statut d'acceptation (Appels)", "title": "<PERSON><PERSON><PERSON>", "hasPortalUser": "Dispose d'un utilisateur de Portail", "originalEmail": "Email original"}, "links": {"opportunities": "Opportunités", "cases": "Tickets", "targetLists": "Listes de cibles", "campaignLogRecords": "Rapporter une campagne", "campaign": "Campagne", "account": "<PERSON><PERSON><PERSON> (principal)", "accounts": "<PERSON><PERSON><PERSON>", "casesPrimary": "Tick<PERSON> (principal)", "portalUser": "Utilisateur Portail", "tasksPrimary": "Tâches (développées)", "opportunitiesPrimary": "Opportunités (Parentes)"}, "labels": {"Create Contact": "<PERSON><PERSON><PERSON> un <PERSON>"}, "options": {"opportunityRole": {"Decision Maker": "Décisionnaire", "Evaluator": "<PERSON><PERSON><PERSON><PERSON>", "Influencer": "Influenceur"}}, "presetFilters": {"portalUsers": "Utilisateurs Portail", "notPortalUsers": "N'est pas un utilisateur Portail"}}