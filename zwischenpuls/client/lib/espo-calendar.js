/*! espocrm 2025-06-24 */
define("modules/crm/views/calendar/calendar",["exports","view","moment","fullcalendar","helpers/record-modal"],function(t,e,u,s,o){Object.defineProperty(t,"__esModule",{value:!0});t.default=void 0;e=a(e);u=a(u);s=((t,e)=>{if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var a=n(e);if(a&&a.has(t))return a.get(t);var i,s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(i in t)if("default"!==i&&{}.hasOwnProperty.call(t,i)){var d=o?Object.getOwnPropertyDescriptor(t,i):null;d&&(d.get||d.set)?Object.defineProperty(s,i,d):s[i]=t[i]}return s.default=t,a&&a.set(t,s),s})(s);o=a(o);function n(t){var e,a;return"function"!=typeof WeakMap?null:(e=new WeakMap,a=new WeakMap,(n=function(t){return t?a:e})(t))}function a(t){return t&&t.__esModule?t:{default:t}}class i extends e.default{template="crm:calendar/calendar";eventAttributes=[];colors={};allDayScopeList=["Task"];scopeList=["Meeting","Call","Task"];header=!0;modeList=[];fullCalendarModeList=["month","agendaWeek","agendaDay","basicWeek","basicDay","listWeek"];defaultMode="agendaWeek";slotDuration=30;scrollToNowSlots=6;scrollHour=6;titleFormat={month:"MMMM YYYY",week:"MMMM YYYY",day:"dddd, MMMM D, YYYY"};rangeSeparator=" – ";fetching=!1;modeViewMap={month:"dayGridMonth",agendaWeek:"timeGridWeek",agendaDay:"timeGridDay",basicWeek:"dayGridWeek",basicDay:"dayGridDay",listWeek:"listWeek"};extendedProps=["scope","recordId","dateStart","dateEnd","dateStartDate","dateEndDate","status","originalColor","duration","allDayCopy"];calendar;events={'click button[data-action="prev"]':function(){this.actionPrevious()},'click button[data-action="next"]':function(){this.actionNext()},'click button[data-action="today"]':function(){this.actionToday()},'click [data-action="mode"]':function(t){var e=$(t.currentTarget).data("mode");this.selectMode(e)},'click [data-action="refresh"]':function(){this.actionRefresh()},'click [data-action="toggleScopeFilter"]':function(t){var e=$(t.currentTarget),a=e.data("name"),e=e.find(".filter-check-icon");e.hasClass("hidden")?e.removeClass("hidden"):e.addClass("hidden");t.stopPropagation(t);this.toggleScopeFilter(a)}};data(){return{mode:this.mode,header:this.header,isCustomViewAvailable:this.isCustomViewAvailable,isCustomView:this.isCustomView,todayLabel:this.translate("Today","labels","Calendar"),todayLabelShort:this.translate("Today","labels","Calendar").slice(0,2)}}setup(){this.wait(Espo.loader.requirePromise("lib!@fullcalendar/moment"));this.wait(Espo.loader.requirePromise("lib!@fullcalendar/moment-timezone"));this.suppressLoadingAlert=this.options.suppressLoadingAlert;this.date=this.options.date||null;this.mode=this.options.mode||this.defaultMode;this.header=("header"in this.options?this.options:this).header;this.scrollToNowSlots=(void 0!==this.options.scrollToNowSlots?this.options:this).scrollToNowSlots;this.setupMode();this.$container=this.options.$container;this.colors=Espo.Utils.clone(this.getMetadata().get("clientDefs.Calendar.colors")||this.colors);this.modeList=this.getMetadata().get("clientDefs.Calendar.modeList")||this.modeList;this.scopeList=this.getConfig().get("calendarEntityList")||Espo.Utils.clone(this.scopeList);this.allDayScopeList=this.getMetadata().get("clientDefs.Calendar.allDayScopeList")||this.allDayScopeList;this.slotDuration=this.options.slotDuration||this.getPreferences().get("calendarSlotDuration")||this.getMetadata().get("clientDefs.Calendar.slotDuration")||this.slotDuration;this.setupScrollHour();this.colors={...this.colors,...this.getHelper().themeManager.getParam("calendarColors")};this.isCustomViewAvailable="no"!==this.getAcl().getPermissionLevel("userCalendar");this.options.userId&&(this.isCustomViewAvailable=!1);let e=[];this.scopeList.forEach(t=>{this.getAcl().check(t)&&e.push(t)});this.scopeList=e;this.header?this.enabledScopeList=this.getStoredEnabledScopeList()||Espo.Utils.clone(this.scopeList):this.enabledScopeList=this.options.enabledScopeList||Espo.Utils.clone(this.scopeList);"[object Array]"!==Object.prototype.toString.call(this.enabledScopeList)&&(this.enabledScopeList=[]);this.enabledScopeList.forEach(t=>{var e=this.getMetadata().get(["clientDefs",t,"color"]);e&&(this.colors[t]=e)});this.header&&this.createView("modeButtons","crm:views/calendar/mode-buttons",{selector:".mode-buttons",isCustomViewAvailable:this.isCustomViewAvailable,modeList:this.modeList,scopeList:this.scopeList,mode:this.mode})}setupScrollHour(){if(void 0!==this.options.scrollHour)this.scrollHour=this.options.scrollHour;else{var t=this.getPreferences().get("calendarScrollHour");null!==t?this.scrollHour=t:this.slotDuration<30&&(this.scrollHour=8)}}setupMode(){this.viewMode=this.mode;this.isCustomView=!1;this.teamIdList=this.options.teamIdList||null;this.teamIdList&&!this.teamIdList.length&&(this.teamIdList=null);if(~this.mode.indexOf("view-")){this.viewId=this.mode.slice(5);this.isCustomView=!0;var t=this.getPreferences().get("calendarViewDataList")||[];t.forEach(t=>{if(t.id===this.viewId){this.viewMode=t.mode;this.teamIdList=t.teamIdList;this.viewName=t.name}})}}isAgendaMode(){return 0===this.mode.indexOf("agenda")}selectMode(t){if(this.fullCalendarModeList.includes(t)||0===t.indexOf("view-")){var e=this.mode;if(0===t.indexOf("view-")||0!==t.indexOf("view-")&&0===e.indexOf("view-"))return this.trigger("change:mode",t,!0),void 0;this.mode=t;this.setupMode();this.isCustomView?this.$el.find('button[data-action="editCustomView"]').removeClass("hidden"):this.$el.find('button[data-action="editCustomView"]').addClass("hidden");this.$el.find('[data-action="mode"]').removeClass("active");this.$el.find('[data-mode="'+t+'"]').addClass("active");this.calendar.changeView(this.modeViewMap[this.viewMode]);var a=0!==e.indexOf("agenda")&&0===t.indexOf("agenda"),e=0===e.indexOf("agenda")&&0!==t.indexOf("agenda");(a&&!this.fetching||e&&!this.fetching)&&this.calendar.refetchEvents();this.updateDate();if(this.hasView("modeButtons")){this.getModeButtonsView().mode=t;this.getModeButtonsView().reRender()}}this.trigger("change:mode",t)}getModeButtonsView(){return this.getView("modeButtons")}toggleScopeFilter(t){var e=this.enabledScopeList.indexOf(t);~e?this.enabledScopeList.splice(e,1):this.enabledScopeList.push(t);this.storeEnabledScopeList(this.enabledScopeList);this.calendar.refetchEvents()}getStoredEnabledScopeList(){return this.getStorage().get("state","calendarEnabledScopeList")||null}storeEnabledScopeList(t){this.getStorage().set("state","calendarEnabledScopeList",t)}updateDate(){if(this.header){this.isToday()?this.$el.find('button[data-action="today"]').addClass("active"):this.$el.find('button[data-action="today"]').removeClass("active");var t=this.getTitle();this.$el.find(".date-title h4 span").text(t)}}isToday(){var t=this.calendar.view,e=(0,u.default)().unix(),a=(0,u.default)(t.activeStart).unix(),t=(0,u.default)(t.activeEnd).unix();return a<=e&&e<t}getTitle(){var t=this.calendar.view,e={timeGridWeek:"week",timeGridDay:"day",dayGridWeek:"week",dayGridDay:"day",dayGridMonth:"month"}[t.type]||t.type;let a;var i=this.titleFormat[e];if("week"===e){var e=this.dateToMoment(t.currentStart).format(i),s=this.dateToMoment(t.currentEnd).subtract(1,"minute").format(i);a=e!==s?e+this.rangeSeparator+s:e}else a=this.dateToMoment(t.currentStart).format(i);this.options.userId&&this.options.userName&&(a+=" ("+this.options.userName+")");a=this.getHelper().escapeString(a);return a}convertToFcEvent(e){let a={title:e.name||"",scope:e.scope,id:e.scope+"-"+e.id,recordId:e.id,dateStart:e.dateStart,dateEnd:e.dateEnd,dateStartDate:e.dateStartDate,dateEndDate:e.dateEndDate,status:e.status,originalColor:e.color,display:"block"};if(e.isWorkingRange){a.display="inverse-background";a.groupId="nonWorking";a.color=this.colors.bg}if(this.teamIdList){a.userIdList=e.userIdList||[];a.userNameMap=e.userNameMap||{};a.userIdList=a.userIdList.sort((t,e)=>(a.userNameMap[t]||"").localeCompare(a.userNameMap[e]||""))}this.eventAttributes.forEach(t=>{a[t]=e[t]});let t,i;e.dateStart&&(t=e.dateStartDate?this.dateToMoment(e.dateStartDate):this.getDateTime().toMoment(e.dateStart));e.dateEnd&&(i=e.dateEndDate?this.dateToMoment(e.dateEndDate):this.getDateTime().toMoment(e.dateEnd));i&&t&&(a.duration=i.unix()-t.unix());t&&(a.start=t.toISOString(!0));i&&(a.end=i.toISOString(!0));a.allDay=!1;if(!e.isWorkingRange){this.handleAllDay(a);this.fillColor(a);this.handleStatus(a)}e.isWorkingRange&&!this.isAgendaMode()&&(a.allDay=!0);return a}dateToMoment(t){return u.default.tz(t,this.getDateTime().getTimeZone())}getEventTypeCompletedStatusList(t){return this.getMetadata().get(["scopes",t,"completedStatusList"])||[]}getEventTypeCanceledStatusList(t){return this.getMetadata().get(["scopes",t,"canceledStatusList"])||[]}fillColor(t){let e=this.colors[t.scope];t.originalColor&&(e=t.originalColor);e=e||this.getColorFromScopeName(t.scope);e&&(this.getEventTypeCompletedStatusList(t.scope).includes(t.status)||this.getEventTypeCanceledStatusList(t.scope).includes(t.status))&&(e=this.shadeColor(e,.4));t.color=e}handleStatus(t){this.getEventTypeCanceledStatusList(t.scope).includes(t.status)?t.className=["event-canceled"]:t.className=[]}shadeColor(t,e){if("transparent"===t)return t;this.getThemeManager().getParam("isDark")&&(e*=-1);var a=t.substring(7),i=parseInt(t.slice(1,7),16),s=e<0?0:255,o=e<0?-1*e:e,d=i>>16,n=i>>8&255,i=255&i;return"#"+(16777216+65536*(Math.round((s-d)*o)+d)+256*(Math.round((s-n)*o)+n)+(Math.round((s-i)*o)+i)).toString(16).slice(1)+a}handleAllDay(t,e){let a=t.start?this.dateToMoment(t.start):null;var i=t.end?this.dateToMoment(t.end):null;if(this.allDayScopeList.includes(t.scope)){t.allDay=t.allDayCopy=!0;if(!e&&i){a=i.clone();t.dateEndDate||0!==i.hours()||0!==i.minutes()||a.add(-1,"days")}a.isSame(i)&&i.add(1,"days")}else if(t.dateStartDate&&t.dateEndDate){t.allDay=!0;t.allDayCopy=t.allDay;e||i.add(1,"days")}else{if(a&&i)if(a.format("YYYY-DD")!==i.format("YYYY-DD")&&86400<=i.unix()-a.unix()){t.allDay=!0;0===i.hours()&&0===i.minutes()||i.add(1,"days")}else t.allDay=!1;else{t.allDay=!0;i&&(a=i)}t.allDayCopy=t.allDay}a&&(t.start=a.toDate());i&&(t.end=i.toDate())}convertToFcEvents(t){this.now=u.default.tz(this.getDateTime().getTimeZone());let a=[];t.forEach(t=>{var e=this.convertToFcEvent(t);a.push(e)});return a}convertDateTime(t){var e=this.getDateTime().internalDateTimeFormat,a=this.getDateTime().timeZone,a=a?u.default.tz(t,null,a).utc():u.default.utc(t,null);return a.format(e)+":00"}getCalculatedHeight(){return this.$container&&this.$container.length?this.$container.height():this.getHelper().calculateContentContainerHeight(this.$el.find(".calendar"))}adjustSize(){if(!this.isRemoved()){var t=this.getCalculatedHeight();this.calendar.setOption("contentHeight",t);this.calendar.updateSize()}}afterRender(){this.options.containerSelector&&(this.$container=$(this.options.containerSelector));this.$calendar=this.$el.find("div.calendar");var t="00:"+this.slotDuration+":00",e=this.getDateTime().timeFormat;let a=e,i=(~e.indexOf("a")?a="h:mma":~e.indexOf("A")&&(a="h:mmA"),{scrollTime:this.scrollHour+":00",headerToolbar:!1,slotLabelFormat:a,eventTimeFormat:e,initialView:this.modeViewMap[this.viewMode],defaultRangeSeparator:this.rangeSeparator,weekNumbers:!0,weekNumberCalculation:"ISO",editable:!0,selectable:!0,selectMirror:!0,height:this.options.height||void 0,firstDay:this.getDateTime().weekStart,slotEventOverlap:!0,slotDuration:t,slotLabelInterval:"01:00",snapDuration:60*this.slotDuration*1e3,timeZone:this.getDateTime().timeZone||void 0,longPressDelay:300,eventColor:this.colors[""],nowIndicator:!0,allDayText:"",weekText:"",views:{week:{dayHeaderFormat:"ddd DD"},day:{dayHeaderFormat:"ddd DD"},month:{dayHeaderFormat:"ddd"}},windowResize:()=>{this.adjustSize()},select:t=>{var e=t.startStr,a=t.endStr,i=t.allDay;let s=null,o=null;var d=this.convertDateTime(e),n=this.convertDateTime(a);if(i){o=(0,u.default)(e).format("YYYY-MM-DD");s=(0,u.default)(a).clone().add(-1,"days").format("YYYY-MM-DD")}this.createEvent({dateStart:d,dateEnd:n,allDay:i,dateStartDate:o,dateEndDate:s});this.calendar.unselect()},eventClick:async t=>{var e=t.event,a=e.extendedProps.scope,e=e.extendedProps.recordId,i=new o.default;let s;s=await i.showDetail(this,{entityType:a,id:e,removeDisabled:!1,afterSave:(t,e)=>{e.bypassClose||s.close();this.updateModel(t)},afterDestroy:t=>{this.removeModel(t)}})},datesSet:()=>{this.date=this.dateToMoment(this.calendar.getDate()).format("YYYY-MM-DD");this.trigger("view",this.date,this.mode)},events:(t,e)=>{var a=this.getDateTime().internalDateTimeFormat,i=u.default.tz(t.startStr,t.timeZone),s=u.default.tz(t.endStr,t.timeZone),i=i.utc().format(a),s=s.utc().format(a);this.fetchEvents(i,s,e)},eventDrop:i=>{let s=i.event;var t=i.delta,o=s.extendedProps.scope;if(!s.allDay&&s.extendedProps.allDayCopy)i.revert();else if(s.allDay&&!s.extendedProps.allDayCopy)i.revert();else{var d=s.start,n=s.end,r=s.extendedProps.dateStart,l=s.extendedProps.dateEnd,h=s.extendedProps.dateStartDate,c=s.extendedProps.dateEndDate;let e={};if(r){r=this.getDateTime().toMoment(r).add(t).format(this.getDateTime().internalDateTimeFormat);e.dateStart=this.convertDateTime(r)}if(l){r=this.getDateTime().toMoment(l).add(t).format(this.getDateTime().internalDateTimeFormat);e.dateEnd=this.convertDateTime(r)}if(h){l=this.dateToMoment(h).add(t);e.dateStartDate=l.format(this.getDateTime().internalDateFormat)}if(c){r=this.dateToMoment(c).add(t);e.dateEndDate=r.format(this.getDateTime().internalDateFormat)}let a=this.obtainPropsFromEvent(s);n||this.allDayScopeList.includes(o)||(a.end=u.default.tz(d.toISOString(),null,this.getDateTime().timeZone).clone().add(s.extendedProps.duration,"s").toDate());a.allDay=!1;a.dateStart=e.dateStart;a.dateEnd=e.dateEnd;a.dateStartDate=e.dateStartDate;a.dateEndDate=e.dateEndDate;this.handleAllDay(a,!0);this.fillColor(a);Espo.Ui.notify(this.translate("saving","messages"));this.getModelFactory().create(o,t=>{t.id=a.recordId;t.save(e,{patch:!0}).then(()=>{Espo.Ui.notify(!1);this.applyPropsToEvent(s,a)}).catch(()=>{i.revert()})})}},eventResize:e=>{let a=e.event,i={dateEnd:this.convertDateTime(a.endStr)},s=(0,u.default)(a.end).unix()-(0,u.default)(a.start).unix();Espo.Ui.notify(this.translate("saving","messages"));this.getModelFactory().create(a.extendedProps.scope,t=>{t.id=a.extendedProps.recordId;t.save(i,{patch:!0}).then(()=>{Espo.Ui.notify(!1);a.setExtendedProp("dateEnd",i.dateEnd);a.setExtendedProp("duration",s)}).catch(()=>{e.revert()})})},eventAllow:(t,e)=>!(e.allDay&&!t.allDay||!e.allDay&&t.allDay)});this.teamIdList&&(i.eventContent=t=>{let i=t.event,s=$("<div>");s.append($("<div>").append($("<div>").addClass("fc-event-main-frame").append(t.timeText?$("<div>").addClass("fc-event-time").text(t.timeText):void 0).append($("<div>").addClass("fc-event-title").text(i.title))));var e=i.extendedProps.userIdList||[];e.forEach(t=>{var e=i.extendedProps.userNameMap[t]||"";let a=this.getHelper().getAvatarHtml(t,"small",13);a&&(a+=" ");e=$("<div>").addClass("user").css({overflow:"hidden"}).append(a).append($("<span>").text(e));s.append(e)});return{html:s.get(0).innerHTML}});this.options.height?i.aspectRatio=1.62:i.contentHeight=this.getCalculatedHeight();this.date?i.initialDate=this.date:this.$el.find('button[data-action="today"]').addClass("active");setTimeout(()=>{this.calendar=new s.Calendar(this.$calendar.get(0),i);this.calendar.render();this.handleScrollToNow();this.updateDate();this.$container&&this.$container.length&&this.adjustSize()},150)}handleScrollToNow(){if(("agendaWeek"===this.mode||"agendaDay"===this.mode)&&this.isToday()){var t=this.getDateTime().getNowMoment().hours()-Math.floor(this.slotDuration*this.scrollToNowSlots/60);t<0||this.calendar.scrollToTime(t+":00")}}createEvent(t){t=t||{};if(!t.dateStart&&this.date!==this.getDateTime().getToday()&&("day"===this.mode||"agendaDay"===this.mode)){t.allDay=!0;t.dateStartDate=this.date;t.dateEndDate=this.date}var e={};if(this.options.userId){e.assignedUserId=this.options.userId;e.assignedUserName=this.options.userName||this.options.userId}Espo.Ui.notifyWait();this.createView("quickEdit","crm:views/calendar/modals/edit",{attributes:e,enabledScopeList:this.enabledScopeList,scopeList:this.scopeList,allDay:t.allDay,dateStartDate:t.dateStartDate,dateEndDate:t.dateEndDate,dateStart:t.dateStart,dateEnd:t.dateEnd},t=>{t.render();Espo.Ui.notify(!1);let e=!1;this.listenTo(t,"after:save",t=>{if(e)this.updateModel(t);else{this.addModel(t);e=!0}})})}fetchEvents(t,e,a){let i=`Activities?from=${t}&to=`+e;this.options.userId&&(i+="&userId="+this.options.userId);i+="&scopeList="+encodeURIComponent(this.enabledScopeList.join(","));this.teamIdList&&this.teamIdList.length&&(i+="&teamIdList="+encodeURIComponent(this.teamIdList.join(",")));var s="agendaWeek"===this.mode||"agendaDay"===this.mode;i+="&agenda="+encodeURIComponent(s);this.suppressLoadingAlert||Espo.Ui.notifyWait();Espo.Ajax.getRequest(i).then(t=>{var e=this.convertToFcEvents(t);a(e);Espo.Ui.notify(!1)});this.fetching=!0;this.suppressLoadingAlert=!1;setTimeout(()=>this.fetching=!1,50)}addModel(t){var e=t.getClonedAttributes(),e=(e.scope=t.entityType,this.convertToFcEvent(e));this.calendar.addEvent(e,!0)}updateModel(t){var e=t.entityType+"-"+t.id,e=this.calendar.getEventById(e);if(e){var a=t.getClonedAttributes(),a=(a.scope=t.entityType,this.convertToFcEvent(a));this.applyPropsToEvent(e,a)}}obtainPropsFromEvent(t){var e,a={};for(e in t.extendedProps)a[e]=t.extendedProps[e];a.allDay=t.allDay;a.start=t.start;a.end=t.end;a.title=t.title;a.id=t.id;a.color=t.color;return a}applyPropsToEvent(t,e){if("start"in e){!e.allDay&&e.end&&e.end.getTime()===e.start.getTime()&&(e.end=(0,u.default)(e.end).add(1,"hour").toDate());t.setDates(e.start,e.end,{allDay:e.allDay})}for(var a in e){var i=e[a];"start"!==a&&"end"!==a&&"allDay"!==a&&("className"===a?t.setProp("classNames",i):this.extendedProps.includes(a)?t.setExtendedProp(a,i):t.setProp(a,i))}}removeModel(t){var e=this.calendar.getEventById(t.entityType+"-"+t.id);e&&e.remove()}actionRefresh(t){t&&t.suppressLoadingAlert&&(this.suppressLoadingAlert=!0);this.calendar.refetchEvents()}actionPrevious(){this.calendar.prev();this.handleScrollToNow();this.updateDate()}actionNext(){this.calendar.next();this.handleScrollToNow();this.updateDate()}getColorFromScopeName(i){var t=this.getMetadata().get("clientDefs.Calendar.additionalColorList")||[];if(t.length){var s=this.getMetadata().get("clientDefs.Calendar.colors")||{},o=this.getConfig().get("calendarEntityList")||[];let e=0,a=0;for(let t=0;t<o.length;t++)if(!(o[t]in s)){if(o[t]===i){e=a;break}a++}e%=t.length;this.colors[i]=t[e];return this.colors[i]}}actionToday(){if(this.isToday())this.actionRefresh();else{this.calendar.today();this.handleScrollToNow();this.updateDate()}}}t.default=i});
//# sourceMappingURL=espo-calendar.js.map