/*! espocrm 2025-06-24 */
define("crm:views/dashlets/abstract/chart",["views/dashlets/abstract/base","lib!flotr2"],function(t,e){return t.extend({templateContent:'<div class="chart-container"></div><div class="legend-container"></div>',decimalMark:".",thousandSeparator:",",defaultColorList:["#6FA8D6","#4E6CAD","#EDC555","#ED8F42","#DE6666","#7CC4A4","#8A7CC2","#D4729B"],successColor:"#85b75f",gridColor:"#ddd",tickColor:"#e8eced",textColor:"#333",hoverColor:"#FF3F19",legendColumnWidth:110,legendColumnNumber:6,labelFormatter:function(t){return'<span style="color:'+this.textColor+'">'+t+"</span>"},init:function(){t.prototype.init.call(this);this.fontSizeFactor=this.getThemeManager().getFontSizeFactor();this.flotr=e;this.successColor=this.getThemeManager().getParam("chartSuccessColor")||this.successColor;this.colorList=this.getThemeManager().getParam("chartColorList")||this.defaultColorList;this.tickColor=this.getThemeManager().getParam("chartTickColor")||this.tickColor;this.gridColor=this.getThemeManager().getParam("chartGridColor")||this.gridColor;this.textColor=this.getThemeManager().getParam("textColor")||this.textColor;this.hoverColor=this.getThemeManager().getParam("hoverColor")||this.hoverColor;this.getPreferences().has("decimalMark")?this.decimalMark=this.getPreferences().get("decimalMark"):this.getConfig().has("decimalMark")&&(this.decimalMark=this.getConfig().get("decimalMark"));this.getPreferences().has("thousandSeparator")?this.thousandSeparator=this.getPreferences().get("thousandSeparator"):this.getConfig().has("thousandSeparator")&&(this.thousandSeparator=this.getConfig().get("thousandSeparator"));this.on("resize",()=>{this.isRendered()&&setTimeout(()=>{this.adjustContainer();this.isNoData()?this.showNoData():this.draw()},50)});$(window).on("resize.chart"+this.id,()=>{this.adjustContainer();this.isNoData()?this.showNoData():this.draw()});this.once("remove",()=>{$(window).off("resize.chart"+this.id)})},formatNumber:function(e,t,a){if(null===e)return"";var i=this.getConfig().get("currencyDecimalPlaces"),r="";if(a)if(1e6<=e){r="M";e/=1e6}else if(1e3<=e){r="k";e/=1e3}if(t)e=0===i?Math.round(e):i?Math.round(e*Math.pow(10,i))/Math.pow(10,i):Math.round(e*Math.pow(10,2))/Math.pow(10,2);else{let t=4;a&&(t=2);e=Math.round(e*Math.pow(10,t))/Math.pow(10,t)}var s=e.toString().split(".");s[0]=s[0].replace(/\B(?=(\d{3})+(?!\d))/g,this.thousandSeparator);if(t)if(0===i)delete s[1];else if(i){var o=0;1<s.length?o=s[1].length:s[1]="";if(i&&o<i){var n=i-o;for(let t=0;t<n;t++)s[1]+="0"}}return s.join(this.decimalMark)+r},getLegendColumnNumber:function(){var t=this.$el.closest(".panel-body").width(),t=Math.floor(t/(this.legendColumnWidth*this.fontSizeFactor));return t||this.legendColumnNumber},getLegendHeight:function(){var t=Math.ceil(this.chartData.length/this.getLegendColumnNumber());let e=0;var a=(this.getThemeManager().getParam("dashletChartLegendRowHeight")||19)*this.fontSizeFactor,i=(this.getThemeManager().getParam("dashletChartLegendPaddingTopHeight")||7)*this.fontSizeFactor;0<t&&(e=a*t+i);return e},adjustContainer:function(){var t=this.getLegendHeight(),t=`calc(100% - ${t.toString()}px)`;this.$container.css("height",t)},adjustLegend:function(){var t=this.getLegendColumnNumber();if(t){var e=(this.getThemeManager().getParam("dashletChartLegendBoxWidth")||21)*this.fontSizeFactor,a=this.$legendContainer.width(),a=Math.floor((a-e*t)/t),t=this.$legendContainer.find("> table tr:first-child > td").length/2,t=(a+e)*t;this.$legendContainer.find("> table").css("table-layout","fixed").attr("width",t);this.$legendContainer.find("td.flotr-legend-label").attr("width",a);this.$legendContainer.find("td.flotr-legend-color-box").attr("width",e);this.$legendContainer.find("td.flotr-legend-label > span").each((t,e)=>{e.setAttribute("title",e.textContent)})}},afterRender:function(){this.$el.closest(".panel-body").css({"overflow-y":"visible","overflow-x":"visible"});this.$legendContainer=this.$el.find(".legend-container");this.$container=this.$el.find(".chart-container");this.fetch(function(t){this.chartData=this.prepareData(t);this.adjustContainer();this.isNoData()?this.showNoData():setTimeout(()=>{this.$container.length&&this.$container.is(":visible")&&this.draw()},1)})},isNoData:function(){return!1},url:function(){},prepareData:function(t){return t},fetch:function(e){Espo.Ajax.getRequest(this.url()).then(t=>{e.call(this,t)})},getDateFilter:function(){return this.getOption("dateFilter")||"currentYear"},showNoData:function(){this.$container.empty();var t=$("<span>").html(this.translate("No Data")).addClass("text-muted"),e=$("<div>").css("text-align","center").css("font-size","calc(var(--font-size-base) * 1.2)").css("display","table").css("width","100%").css("height","100%").css("user-select","none");t.css("display","table-cell").css("vertical-align","middle").css("padding-bottom","calc(var(--font-size-base) * 1.5)");e.append(t);this.$container.append(e)}})});define("crm:views/dashlets/sales-pipeline",["crm:views/dashlets/abstract/chart","lib!espo-funnel-chart"],function(t){return t.extend({name:"SalesPipeline",setupDefaultOptions:function(){this.defaultOptions.dateFrom=this.defaultOptions.dateFrom||moment().format("YYYY")+"-01-01";this.defaultOptions.dateTo=this.defaultOptions.dateTo||moment().format("YYYY")+"-12-31"},url:function(){var t="Opportunity/action/reportSalesPipeline?dateFilter="+this.getDateFilter();"between"===this.getDateFilter()&&(t+="&dateFrom="+this.getOption("dateFrom")+"&dateTo="+this.getOption("dateTo"));this.getOption("useLastStage")&&(t+="&useLastStage=true");this.getOption("teamId")&&(t+="&teamId="+this.getOption("teamId"));return t},isNoData:function(){return this.isEmpty},prepareData:function(t){let e=[];this.isEmpty=!0;t.dataList.forEach(t=>{t.value&&(this.isEmpty=!1);e.push({stageTranslated:this.getLanguage().translateOption(t.stage,"stage","Opportunity"),value:t.value,stage:t.stage})});return e},setup:function(){this.currency=this.getConfig().get("defaultCurrency");this.currencySymbol=this.getMetadata().get(["app","currency","symbolMap",this.currency])||"";this.chartData=[]},draw:function(){let a=Espo.Utils.clone(this.colorList);this.chartData.forEach((t,e)=>{e+1>a.length&&a.push("#164");this.chartData.length===e+1&&"Closed Won"===t.stage&&(a[e]=this.successColor);this.chartData[e].color=a[e]});this.$container.empty();new EspoFunnel.Funnel(this.$container.get(0),{colors:a,outlineColor:this.hoverColor,callbacks:{tooltipHtml:t=>{var e=this.chartData[t].value;return this.chartData[t].stageTranslated+"<br>"+this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(e,!0)+"</span>"}},tooltipClassName:"flotr-mouse-value",tooltipStyleString:"opacity:0.7;background-color:#000;color:#fff;position:absolute;padding:2px 8px;-moz-border-radius:4px;border-radius:4px;white-space:nowrap;"},this.chartData);this.drawLegend();this.adjustLegend()},drawLegend:function(){let r=this.getLegendColumnNumber();var t=this.$el.find(".legend-container");let s='<table style="font-size: smaller; color:'+this.textColor+'">';this.chartData.forEach((t,e)=>{if(e%r==0){0<e&&(s+="</tr>");s+="<tr>"}var a=this.getHelper().escapeString(t.stageTranslated),i='<div style="border: var(--1px) solid transparent; padding: var(--1px)"><div style="width: var(--13px); height: var(--9px); border: var(--1px) solid '+t.color+'"><div style="width: var(--14px); height: var(--10px); background-color:'+t.color+';"></div></div></div>';s+='<td class="flotr-legend-color-box">'+i+"</td>";s+='<td class="flotr-legend-label"><span title="'+a+'">'+a+"</span></td>"});s+="</tr>";s+="</table>";t.html(s)}})});define("crm:views/dashlets/sales-by-month",["crm:views/dashlets/abstract/chart"],function(t){return t.extend({name:"SalesByMonth",columnWidth:50,setupDefaultOptions:function(){this.defaultOptions.dateFrom=this.defaultOptions.dateFrom||moment().format("YYYY")+"-01-01";this.defaultOptions.dateTo=this.defaultOptions.dateTo||moment().format("YYYY")+"-12-31"},url:function(){var t="Opportunity/action/reportSalesByMonth?dateFilter="+this.getDateFilter();"between"===this.getDateFilter()&&(t+="&dateFrom="+this.getOption("dateFrom")+"&dateTo="+this.getOption("dateTo"));return t},getLegendHeight:function(){return 0},isNoData:function(){return this.isEmpty},prepareData:function(t){var e=this.monthList=t.keyList,a=t.dataMap||{},i=[];e.forEach(t=>{i.push(a[t])});this.chartData=[];this.isEmpty=!0;var r=0,s=(i.length&&(r=i.reduce((t,e)=>t+e)/i.length),[]),o=0;i.forEach((t,e)=>{t&&(this.isEmpty=!1);t&&o<t&&(o=t);s.push({data:[[e,t]],color:r<=t?this.successColor:this.colorBad})});this.max=o;return s},setup:function(){this.currency=this.getConfig().get("defaultCurrency");this.currencySymbol=this.getMetadata().get(["app","currency","symbolMap",this.currency])||"";this.colorBad=this.successColor},getTickNumber:function(){var t=this.$container.width();return Math.floor(t/this.columnWidth*this.fontSizeFactor)},draw:function(){var a=this.getTickNumber();this.flotr.draw(this.$container.get(0),this.chartData,{shadowSize:!1,bars:{show:!0,horizontal:!1,shadowSize:0,lineWidth:+this.fontSizeFactor,fillOpacity:1,barWidth:.5},grid:{horizontalLines:!0,verticalLines:!1,outline:"sw",color:this.gridColor,tickColor:this.tickColor},yaxis:{min:0,showLabels:!0,color:this.textColor,max:this.max+.08*this.max,tickFormatter:t=>{t=parseFloat(t);return t&&t%1==0?this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(Math.floor(t),!1,!0).toString()+"</span>":""}},xaxis:{min:0,color:this.textColor,noTicks:a,tickFormatter:t=>{if(t%1==0){var e=parseInt(t);if(e in this.monthList)return 5<this.monthList.length-a&&e===this.monthList.length-1?"":moment(this.monthList[e]+"-01").format("MMM YYYY")}return""}},mouse:{track:!0,relative:!0,lineColor:this.hoverColor,position:"s",autoPositionVertical:!0,trackFormatter:t=>{var e=parseInt(t.x);let a="";e in this.monthList&&(a+=moment(this.monthList[e]+"-01").format("MMM YYYY")+"<br>");return a+this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(t.y,!0)+"</span>"}}})}})});define("crm:views/dashlets/opportunities-by-stage",["crm:views/dashlets/abstract/chart"],function(t){return t.extend({name:"OpportunitiesByStage",setupDefaultOptions:function(){this.defaultOptions.dateFrom=this.defaultOptions.dateFrom||moment().format("YYYY")+"-01-01";this.defaultOptions.dateTo=this.defaultOptions.dateTo||moment().format("YYYY")+"-12-31"},url:function(){var t="Opportunity/action/reportByStage?dateFilter="+this.getDateFilter();"between"===this.getDateFilter()&&(t+="&dateFrom="+this.getOption("dateFrom")+"&dateTo="+this.getOption("dateTo"));return t},prepareData:function(t){let a=[];for(var e in t){var i=t[e];a.push({stage:e,value:i})}this.stageList=[];this.isEmpty=!0;var r=[],s=0;a.forEach(t=>{t.value&&(this.isEmpty=!1);var e={data:[[t.value,a.length-s]],label:this.getLanguage().translateOption(t.stage,"stage","Opportunity")};r.push(e);this.stageList.push(this.getLanguage().translateOption(t.stage,"stage","Opportunity"));s++});let o=0;a.length&&a.forEach(t=>{t.value&&t.value>o&&(o=t.value)});this.max=o;return r},setup:function(){this.currency=this.getConfig().get("defaultCurrency");this.currencySymbol=this.getMetadata().get(["app","currency","symbolMap",this.currency])||""},isNoData:function(){return this.isEmpty},draw:function(){this.flotr.draw(this.$container.get(0),this.chartData,{colors:this.colorList,shadowSize:!1,bars:{show:!0,horizontal:!0,shadowSize:0,lineWidth:+this.fontSizeFactor,fillOpacity:1,barWidth:.5},grid:{horizontalLines:!1,outline:"sw",color:this.gridColor,tickColor:this.tickColor},yaxis:{min:0,showLabels:!1,color:this.textColor},xaxis:{min:0,color:this.textColor,max:this.max+.08*this.max,tickFormatter:t=>{t=parseFloat(t);return!t||t%1!=0||t>this.max+.05*this.max?"":this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(Math.floor(t),!1,!0).toString()+"</span>"}},mouse:{track:!0,relative:!0,position:"w",autoPositionHorizontal:!0,lineColor:this.hoverColor,trackFormatter:t=>{var e=this.getHelper().escapeString(t.series.label||this.translate("None"));return e+"<br>"+this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(t.x,!0)+"</span>"}},legend:{show:!0,noColumns:this.getLegendColumnNumber(),container:this.$el.find(".legend-container"),labelBoxMargin:0,labelFormatter:this.labelFormatter.bind(this),labelBoxBorderColor:"transparent",backgroundOpacity:0}});this.adjustLegend()}})});define("crm:views/dashlets/opportunities-by-lead-source",["crm:views/dashlets/abstract/chart"],function(t){return t.extend({name:"OpportunitiesByLeadSource",url:function(){let t="Opportunity/action/reportByLeadSource?dateFilter="+this.getDateFilter();"between"===this.getDateFilter()&&(t+="&dateFrom="+this.getOption("dateFrom")+"&dateTo="+this.getOption("dateTo"));return t},prepareData:function(t){var e,a=[];for(e in t){var i=t[e];a.push({label:this.getLanguage().translateOption(e,"source","Lead"),data:[[0,i]]})}return a},isNoData:function(){return!this.chartData.length},setupDefaultOptions:function(){this.defaultOptions.dateFrom=this.defaultOptions.dateFrom||moment().format("YYYY")+"-01-01";this.defaultOptions.dateTo=this.defaultOptions.dateTo||moment().format("YYYY")+"-12-31"},setup:function(){this.currency=this.getConfig().get("defaultCurrency");this.currencySymbol=this.getMetadata().get(["app","currency","symbolMap",this.currency])||""},draw:function(){this.flotr.draw(this.$container.get(0),this.chartData,{colors:this.colorList,shadowSize:!1,pie:{show:!0,explode:0,lineWidth:+this.fontSizeFactor,fillOpacity:1,sizeRatio:.8,labelFormatter:(t,e)=>{var a=Math.round(100*e/t);return a<5?"":'<span class="small numeric-text" style="font-size: 0.8em;color:'+this.textColor+'">'+a.toString()+"%</span>"}},grid:{horizontalLines:!1,verticalLines:!1,outline:"",tickColor:this.tickColor},yaxis:{showLabels:!1,color:this.textColor},xaxis:{showLabels:!1,color:this.textColor},mouse:{track:!0,relative:!0,lineColor:this.hoverColor,trackFormatter:t=>{var e=this.currencySymbol+'<span class="numeric-text">'+this.formatNumber(t.y,!0)+"</span>",a=t.fraction||0,a='<span class="numeric-text">'+(100*a).toFixed(2).toString()+"</span>",i=this.getHelper().escapeString(t.series.label||this.translate("None"));return i+"<br>"+e+" / "+a+"%"}},legend:{show:!0,noColumns:this.getLegendColumnNumber(),container:this.$el.find(".legend-container"),labelBoxMargin:0,labelFormatter:this.labelFormatter.bind(this),labelBoxBorderColor:"transparent",backgroundOpacity:0}});this.adjustLegend()}})});
//# sourceMappingURL=espo-chart.js.map