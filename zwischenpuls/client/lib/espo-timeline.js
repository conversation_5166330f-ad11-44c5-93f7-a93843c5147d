/*! espocrm 2025-06-24 */
define("modules/crm/views/scheduler/scheduler",["exports","view","vis-data","vis-timeline","moment","jquery"],function(t,e,a,s,n,r){Object.defineProperty(t,"__esModule",{value:!0});t.default=void 0;e=i(e);n=i(n);r=i(r);function i(t){return t&&t.__esModule?t:{default:t}}class o extends e.default{templateContent=`
        <div class="timeline"></div>
        <link href="{{basePath}}client/modules/crm/css/vis.css" rel="stylesheet">
    `;rangeMarginThreshold=43200;leftMargin=86400;rightMargin=172800;rangeMultiplierLeft=3;rangeMultiplierRight=3;setup(){this.startField=this.options.startField||"dateStart";this.endField=this.options.endField||"dateEnd";this.assignedUserField=this.options.assignedUserField||"assignedUser";this.startDateField=this.startField+"Date";this.endDateField=this.endField+"Date";this.colors=Espo.Utils.clone(this.getMetadata().get("clientDefs.Calendar.colors")||{});this.colors={...this.colors,...this.getHelper().themeManager.getParam("calendarColors")};let t="users";!this.model.hasLink("users")&&this.model.hasLink("assignedUsers")&&(t="assignedUsers");this.eventAssignedUserIsAttendeeDisabled=this.getConfig().get("eventAssignedUserIsAttendeeDisabled")||!1;this.usersField=this.options.usersField||t;this.userIdList=[];this.listenTo(this.model,"change",t=>{var e=t.hasChanged("isAllDay")||t.hasChanged(this.startField)||t.hasChanged(this.endField)||t.hasChanged(this.endDateField)||t.hasChanged(this.usersField+"Ids")||!this.eventAssignedUserIsAttendeeDisabled&&t.hasChanged(this.assignedUserField+"Id");if(e)if(t.hasChanged(this.assignedUserField+"Id")||t.hasChanged(this.usersField+"Ids")){if(!this.isRemoved()){this.trigger("has-data");this.reRender()}}else{this.initDates(!0);if(this.start&&this.end&&this.userIdList.length){this.trigger("has-data");if(this.timeline){this.updateEvent();this.timeline.setWindow(this.start.toDate(),this.end.toDate())}this.noDataShown&&this.reRender()}else if(this.timeline){this.showNoData();this.trigger("no-data")}}});this.once("remove",()=>{this.destroyTimeline()})}destroyTimeline(){if(this.timeline){this.timeline.destroy();this.timeline=null}}showNoData(){this.noDataShown=!0;this.destroyTimeline();this.$timeline.empty();this.$timeline.append((0,r.default)("<div>").addClass("revert-margin").text(this.translate("No Data")))}afterRender(){let i=this.$timeline=this.$el.find(".timeline");this.noDataShown=!1;this.$timeline.empty();this.initGroupsDataSet();this.initDates();if(i.get(0)){i.get(0).innerHTML="";if(this.start&&this.end&&this.userIdList.length){this.destroyTimeline();this.lastHeight&&i.css("min-height",this.lastHeight+"px");this.fetch(this.start,this.end,t=>{var e=new a.DataSet(t);this.timeline=new s.Timeline(i.get(0),e,this.groupsDataSet,{dataAttributes:"all",start:this.start.toDate(),end:this.end.toDate(),rollingMode:{follow:!1},moment:t=>{var e=(0,n.default)(t);return t&&t.noTimeZone?e:e.tz(this.getDateTime().getTimeZone())},xss:{filterOptions:{onTag:(t,e)=>e}},format:this.getFormatObject(),zoomable:!1,moveable:!0,orientation:"top",groupEditable:!1,editable:{add:!1,updateTime:!1,updateGroup:!1,remove:!1},showCurrentTime:!0,locales:{myLocale:{current:this.translate("current","labels","Calendar"),time:this.translate("time","labels","Calendar")}},locale:"myLocale",margin:{item:{vertical:12},axis:6}});i.css("min-height","");this.timeline.on("rangechanged",t=>{t.skipClick=!0;this.blockClick=!0;setTimeout(function(){this.blockClick=!1}.bind(this),100);this.start=(0,n.default)(t.start);this.end=(0,n.default)(t.end);this.updateRange()});setTimeout(()=>{this.lastHeight=i.height()},500)})}else{this.showNoData();this.trigger("no-data")}}}updateEvent(){var t=Espo.Utils.cloneDeep(this.busyEventList),t=this.convertEventList(t),t=(this.addEvent(t),new a.DataSet(t));this.timeline.setItems(t)}updateRange(){(this.start.unix()<this.fetchedStart.unix()+this.rangeMarginThreshold||this.end.unix()>this.fetchedEnd.unix()-this.rangeMarginThreshold)&&this.runFetch()}initDates(e){this.start=null;this.end=null;let i=this.model.get(this.startField),s=this.model.get(this.endField);if(this.model.get("isAllDay")){i=this.model.get(this.startDateField);s=this.model.get(this.endDateField)}if(i&&s){if(this.model.get("isAllDay")){this.eventStart=n.default.tz(i,this.getDateTime().getTimeZone());this.eventEnd=n.default.tz(s,this.getDateTime().getTimeZone());this.eventEnd.add(1,"day")}else{this.eventStart=n.default.utc(i).tz(this.getDateTime().getTimeZone());this.eventEnd=n.default.utc(s).tz(this.getDateTime().getTimeZone())}let t=this.eventEnd.diff(this.eventStart,"hours");this.start=this.eventStart.clone();this.end=this.eventEnd.clone();t<0&&(this.end=this.start.clone());t<1&&(t=1);this.start.add(-t*this.rangeMultiplierLeft,"hours");this.end.add(t*this.rangeMultiplierRight,"hours");this.start.startOf("hour");this.end.endOf("hour");if(!e){this.fetchedStart=null;this.fetchedEnd=null}}}runFetch(){this.fetch(this.start,this.end,t=>{var e=new a.DataSet(t);this.timeline.setItems(e)})}fetch(a,n,r){a=a.clone().add(-1*this.leftMargin,"seconds");n=n.clone().add(this.rightMargin,"seconds");var t=a.utc().format(this.getDateTime().internalDateTimeFormat),e=n.utc().format(this.getDateTime().internalDateTimeFormat);let i="Timeline/busyRanges?from="+t+"&to="+e+"&userIdList="+encodeURIComponent(this.userIdList.join(","))+"&entityType="+this.model.entityType;this.model.id&&(i+="&entityId="+this.model.id);Espo.Ajax.getRequest(i).then(t=>{this.fetchedStart=a.clone();this.fetchedEnd=n.clone();let i=[];for(let e in t){var s=t[e].filter(t=>!t.isBusyRange).concat(t[e].filter(t=>t.isBusyRange));s.forEach(t=>{t.userId=e;i.push(t)})}this.busyEventList=Espo.Utils.cloneDeep(i);var e=this.convertEventList(i);this.addEvent(e);r(e)})}addEvent(e){this.getCurrentItemList().forEach(t=>{e.push(t)})}getCurrentItemList(){let i=[],s={start:this.eventStart.clone(),end:this.eventEnd.clone(),type:"background",style:"z-index: 4; opacity: 0.6;",className:"event-range"};var t=this.getColorFromScopeName(this.model.entityType);if(t){s.style+="; border-color: "+t;t=this.hexToRgb(t);s.style+="; background-color: rgba("+t.r+", "+t.g+", "+t.b+", 0.01)"}this.userIdList.forEach(t=>{var e=Espo.Utils.clone(s);e.group=t;e.id="event-"+t;i.push(e)});return i}convertEventList(t){let i=[];t.forEach(t=>{var e=this.convertEvent(t);e&&i.push(e)});return i}convertEvent(t){let e;if(t.isBusyRange)e={className:"busy",group:t.userId,"date-start":t.dateStart,"date-end":t.dateEnd,type:"background"};else if(t.isWorkingRange)e={className:"working",group:t.userId,"date-start":t.dateStart,"date-end":t.dateEnd,type:"background"};else if(t.isNonWorkingRange){e={className:"non-working",group:t.userId,"date-start":t.dateStart,"date-end":t.dateEnd,type:"background"};var i=this.colors.bg;e.style="background-color:"+i+";";e.style+="border-color:"+i+";"}t.dateStart&&(t.dateStartDate?e.start=n.default.tz(t.dateStartDate,this.getDateTime().getTimeZone()):e.start=this.getDateTime().toMoment(t.dateStart));t.dateEnd&&(t.dateEndDate?e.end=n.default.tz(t.dateEndDate,this.getDateTime().getTimeZone()):e.end=this.getDateTime().toMoment(t.dateEnd));if(t.isBusyRange||t.isNonWorkingRange)return e}initGroupsDataSet(){let i=[];var t=Espo.Utils.clone(this.model.get(this.usersField+"Ids")||[]),e=this.model.get(this.assignedUserField+"Id");let s=this.model.get(this.usersField+"Names")||{};if(!this.eventAssignedUserIsAttendeeDisabled&&e){~t.indexOf(e)||t.unshift(e);s[e]=this.model.get(this.assignedUserField+"Name")}this.userIdList=t;t.forEach((t,e)=>{i.push({id:t,content:this.getGroupContent(t,s[t]||t),order:e})});this.groupsDataSet=new a.DataSet(i)}getGroupContent(t,e){if("single"===this.calendarType)return(0,r.default)("<span>").text(e).get(0).outerHTML;let i=this.getAvatarHtml(t);i&&(i+=" ");return(0,r.default)("<span>").append((0,r.default)(i),(0,r.default)("<span>").attr("data-id",t).addClass("group-title").text(e)).get(0).innerHTML}getAvatarHtml(t){if(this.getConfig().get("avatarsDisabled"))return"";let e;var i=this.getCache();e=i?i.get("app","timestamp"):Date.now();return(0,r.default)("<img>").addClass("avatar avatar-link").attr("width","16").attr("src",this.getBasePath()+"?entryPoint=avatar&size=small&id="+t+"&t="+e).get(0).outerHTML}getFormatObject(){return{minorLabels:{millisecond:"SSS",second:"s",minute:this.getDateTime().getTimeFormat(),hour:this.getDateTime().getTimeFormat(),weekday:"ddd D",day:"D",month:"MMM",year:"YYYY"},majorLabels:{millisecond:this.getDateTime().getTimeFormat()+" ss",second:this.getDateTime().getReadableDateFormat()+" HH:mm",minute:"ddd D MMMM",hour:"ddd D MMMM",weekday:"MMMM YYYY",day:"MMMM YYYY",month:"YYYY",year:""}}}getColorFromScopeName(t){return this.getMetadata().get(["clientDefs",t,"color"])||this.getMetadata().get(["clientDefs","Calendar","colors",t])}hexToRgb(t){var e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null}}t.default=o});define("modules/crm/views/calendar/timeline",["exports","view","vis-data","vis-timeline","moment","jquery","helpers/record-modal","crm:views/calendar/modals/shared-options"],function(t,e,s,a,n,r,o,i){Object.defineProperty(t,"__esModule",{value:!0});t.default=void 0;e=d(e);n=d(n);r=d(r);o=d(o);i=d(i);function d(t){return t&&t.__esModule?t:{default:t}}class l extends e.default{template="crm:calendar/timeline";eventAttributes=[];colors={};scopeList=[];header=!0;modeList=[];defaultMode="timeline";maxRange=120;rangeMarginThreshold=43200;leftMargin=86400;rightMargin=172800;calendarType="single";calendarTypeList=["single","shared"];zoomPercentage=1;timeline;events={'click button[data-action="today"]':function(){this.actionToday()},'click [data-action="mode"]':function(t){var e=(0,r.default)(t.currentTarget).data("mode");this.selectMode(e)},'click [data-action="refresh"]':function(){this.actionRefresh()},'click [data-action="toggleScopeFilter"]':function(t){var e=(0,r.default)(t.currentTarget),i=e.data("name"),e=e.find(".filter-check-icon");e.hasClass("hidden")?e.removeClass("hidden"):e.addClass("hidden");t.stopPropagation(t);this.toggleScopeFilter(i)},'click [data-action="toggleCalendarType"]':function(t){var e=(0,r.default)(t.currentTarget),i=e.data("name"),s=(e.parent().parent().find(".calendar-type-check-icon").addClass("hidden"),e.find(".calendar-type-check-icon"));s.hasClass("hidden")&&s.removeClass("hidden");e.closest(".calendar-type-button-group").find(".calendar-type-label").text(this.getCalendarTypeLabel(i));s=this.$el.find('> .button-container button[data-action="showSharedCalendarOptions"]');"shared"===i?s.removeClass("hidden"):s.addClass("hidden");this.selectCalendarType(i)},'click button[data-action="showSharedCalendarOptions"]':function(){this.actionShowSharedCalendarOptions()}};data(){var t=this.getCalendarTypeDataList();return{mode:this.mode,header:this.header,calendarType:this.calendarType,calendarTypeDataList:t,calendarTypeSelectEnabled:1<t.length,calendarTypeLabel:this.getCalendarTypeLabel(this.calendarType),isCustomViewAvailable:this.isCustomViewAvailable}}setup(){this.date=this.options.date||this.getDateTime().getToday();this.mode=this.options.mode||this.defaultMode;this.header=("header"in this.options?this.options:this).header;this.$container=this.options.$container;this.colors=Espo.Utils.clone(this.getMetadata().get("clientDefs.Calendar.colors")||this.colors||{});this.modeList=this.getMetadata().get("clientDefs.Calendar.modeList")||this.modeList||[];this.scopeList=this.getConfig().get("calendarEntityList")||Espo.Utils.clone(this.scopeList)||[];this.allDayScopeList=this.getMetadata().get("clientDefs.Calendar.allDayScopeList")||this.allDayScopeList||[];this.colors={...this.colors,...this.getHelper().themeManager.getParam("calendarColors")};this.isCustomViewAvailable="no"!==this.getAcl().getPermissionLevel("userCalendar");this.options.userId&&(this.isCustomViewAvailable=!1);let e=[];this.scopeList.forEach(t=>{this.getAcl().check(t)&&e.push(t)});this.scopeList=e;this.header?this.enabledScopeList=this.getStoredEnabledScopeList()||Espo.Utils.clone(this.scopeList):this.enabledScopeList=this.options.enabledScopeList||Espo.Utils.clone(this.scopeList);"[object Array]"!==Object.prototype.toString.call(this.enabledScopeList)&&(this.enabledScopeList=[]);this.enabledScopeList.forEach(t=>{var e=this.getMetadata().get(["clientDefs",t,"color"]);e&&(this.colors[t]=e)});this.options.calendarType?this.calendarType=this.options.calendarType:this.options.userId?this.calendarType="single":this.calendarType=this.getStorage().get("calendar","timelineType")||"shared";"no"===this.getAcl().getPermissionLevel("userCalendar")&&"shared"===this.calendarType&&(this.calendarType="single");~this.calendarTypeList.indexOf(this.calendarType)||(this.calendarType="single");this.header&&this.createView("modeButtons","crm:views/calendar/mode-buttons",{selector:".mode-buttons",isCustomViewAvailable:this.isCustomViewAvailable,modeList:this.modeList,scopeList:this.scopeList,mode:this.mode})}getModeButtonsView(){return this.getView("modeButtons")}selectMode(t){this.trigger("change:mode",t)}getCalendarTypeDataList(){var t=[],e={type:"single",disabled:"single"!==this.calendarType,label:this.getCalendarTypeLabel("single")};t.push(e);this.options.userId||"no"!==this.getAcl().getPermissionLevel("userCalendar")&&t.push({type:"shared",label:this.getCalendarTypeLabel("shared"),disabled:"shared"!==this.calendarType});return t}getCalendarTypeLabel(t){let e;if("single"===t){e=this.options.userId?this.options.userName||this.options.userId:this.getUser().get("name");e=this.getHelper().escapeString(e);return e}if("shared"===t)return this.translate("Shared","labels","Calendar")}selectCalendarType(t){this.calendarType=t;this.initUserList();this.initGroupsDataSet();this.timeline.setGroups(this.groupsDataSet);this.runFetch();this.getStorage().set("calendar","timelineType",t)}toggleScopeFilter(t){var e=this.enabledScopeList.indexOf(t);~e?this.enabledScopeList.splice(e,1):this.enabledScopeList.push(t);this.storeEnabledScopeList(this.enabledScopeList);this.runFetch()}getStoredEnabledScopeList(){return this.getStorage().get("state","calendarEnabledScopeList")||null}storeEnabledScopeList(t){this.getStorage().set("state","calendarEnabledScopeList",t)}getTitle(){let t="";this.options.userId&&this.options.userName&&(t+=" ("+this.options.userName+")");t=this.getHelper().escapeString(t);return t}convertEvent(e){var t=e.userId||this.userList[0].id||this.getUser().id;let i;i=e.isBusyRange?{className:"busy",group:t,"date-start":e.dateStart,"date-end":e.dateEnd,type:"background"}:e.isWorkingRange?{className:"working",group:t,"date-start":e.dateStart,"date-end":e.dateEnd,type:"background"}:e.isNonWorkingRange?{className:"non-working",group:t,"date-start":e.dateStart,"date-end":e.dateEnd,type:"background"}:{content:this.getHelper().escapeString(e.name),title:this.getHelper().escapeString(e.name),id:t+"-"+e.scope+"-"+e.id,group:t,"record-id":e.id,scope:e.scope,status:e.status,"date-start":e.dateStart,"date-end":e.dateEnd,type:"range",className:"clickable",color:e.color};this.eventAttributes.forEach(t=>{i[t]=e[t]});e.dateStart&&(e.dateStartDate?i.start=n.default.tz(e.dateStartDate,this.getDateTime().getTimeZone()):i.start=this.getDateTime().toMoment(e.dateStart));e.dateEnd&&(e.dateEndDate?i.end=n.default.tz(e.dateEndDate,this.getDateTime().getTimeZone()):i.end=this.getDateTime().toMoment(e.dateEnd));e.dateStartDate&&!~this.allDayScopeList.indexOf(e.scope)&&(i.end=i.end.clone().add(1,"days"));if(!e.isBusyRange){if(~this.allDayScopeList.indexOf(e.scope)){i.type="box";i.end&&(e.dateEndDate?i.start=i.end.clone().add(1,"days"):i.start=i.end.clone())}else if(!i.end||!i.start)return;this.fillColor(i);e.isNonWorkingRange||this.handleStatus(i)}return i}getEventTypeCompletedStatusList(t){return this.getMetadata().get(["scopes",t,"completedStatusList"])||[]}getEventTypeCanceledStatusList(t){return this.getMetadata().get(["scopes",t,"canceledStatusList"])||[]}fillColor(t){let e=t.scope,i=("non-working"===t.className&&(e="bg"),this.colors[e]);t.color&&(i=t.color);i=i||this.getColorFromScopeName(t.scope);t.status&&(this.getEventTypeCompletedStatusList(t.scope).includes(t.status)||this.getEventTypeCanceledStatusList(t.scope).includes(t.status))&&(i=this.shadeColor(i,.4));t.style=t.style||"";t.style+="background-color:"+i+";";t.style+="border-color:"+i+";"}handleStatus(t){this.getEventTypeCanceledStatusList(t.scope).includes(t.status)&&(t.className+=" event-canceled")}shadeColor(t,e){if("transparent"===t)return t;this.getThemeManager().getParam("isDark")&&(e*=-1);var i=t.substring(7),s=parseInt(t.slice(1,7),16),a=e<0?0:255,n=e<0?-1*e:e,r=s>>16,o=s>>8&255,s=255&s;return"#"+(16777216+65536*(Math.round((a-r)*n)+r)+256*(Math.round((a-o)*n)+o)+(Math.round((a-s)*n)+s)).toString(16).slice(1)+i}convertEventList(t){let i=[];t.forEach(t=>{var e=this.convertEvent(t);e&&i.push(e)});return i}afterRender(){this.options.containerSelector&&(this.$container=(0,r.default)(this.options.containerSelector));let i=this.$timeline=this.$el.find("div.timeline");this.initUserList();this.initDates();this.initGroupsDataSet();this.fetchEvents(this.start,this.end,t=>{var e=new s.DataSet(t);this.timeline=new a.Timeline(i.get(0),e,this.groupsDataSet,{dataAttributes:"all",start:this.start.toDate(),end:this.end.toDate(),rollingMode:{follow:!1},xss:{filterOptions:{onTag:(t,e)=>e}},moment:t=>{var e=(0,n.default)(t);return t&&t.noTimeZone?e:e.tz(this.getDateTime().getTimeZone())},format:this.getFormatObject(),zoomMax:864e5*this.maxRange,zoomMin:9e5,orientation:"top",groupEditable:!1,editable:{add:!1,updateTime:!1,updateGroup:!1,remove:!1},locales:{myLocale:{current:this.translate("current","labels","Calendar"),time:this.translate("time","labels","Calendar")}},locale:"myLocale",margin:{item:{vertical:12},axis:6}});this.timeline.on("click",t=>{if(!this.blockClick)if(t.item){var e=this.$el.find('.timeline .vis-item[data-id="'+t.item+'"]'),i=e.attr("data-record-id"),e=e.attr("data-scope");i&&e&&this.viewEvent(e,i)}else if("background"===t.what&&t.group&&t.time){e=(0,n.default)(t.time).utc().format(this.getDateTime().internalDateTimeFormat);this.createEvent(e,t.group)}});this.timeline.on("rangechanged",t=>{t.skipClick=!0;this.blockClick=!0;setTimeout(()=>{this.blockClick=!1},100);this.start=(0,n.default)(t.start);this.end=(0,n.default)(t.end);this.triggerView();(this.start.unix()<this.fetchedStart.unix()+this.rangeMarginThreshold||this.end.unix()>this.fetchedEnd.unix()-this.rangeMarginThreshold)&&this.runFetch()});this.once("remove",()=>{this.timeline.destroy()})})}createEvent(t,i){if(!t){var s=(this.timeline.getWindow().end-this.timeline.getWindow().start)/2+this.timeline.getWindow().start;t=(0,n.default)(s).utc().format(this.getDateTime().internalDateTimeFormat);this.date===this.getDateTime().getToday()&&(t=(0,n.default)().utc().format(this.getDateTime().internalDateTimeFormat))}s={dateStart:t};if(i){let e;this.userList.forEach(t=>{t.id===i&&(e=t.name)});s.assignedUserId=i;s.assignedUserName=e||i}Espo.Ui.notifyWait();this.createView("quickEdit","crm:views/calendar/modals/edit",{attributes:s,enabledScopeList:this.enabledScopeList,scopeList:this.scopeList},t=>{t.render();t.notify(!1);this.listenTo(t,"after:save",()=>{this.runFetch()})})}async viewEvent(t,e){var i=new o.default;let s;s=await i.showDetail(this,{entityType:t,id:e,removeDisabled:!1,afterSave:(t,e)=>{e.bypassClose||s.close();this.runFetch()},afterDestroy:()=>{this.runFetch()}})}runFetch(){this.fetchEvents(this.start,this.end,t=>{var e=new s.DataSet(t);this.timeline.setItems(e);this.triggerView()})}getFormatObject(){return{minorLabels:{millisecond:"SSS",second:"s",minute:this.getDateTime().getTimeFormat(),hour:this.getDateTime().getTimeFormat(),weekday:"ddd D",day:"D",month:"MMM",year:"YYYY"},majorLabels:{millisecond:this.getDateTime().getTimeFormat()+" ss",second:this.getDateTime().getReadableDateFormat()+" HH:mm",minute:"ddd D MMMM",hour:"ddd D MMMM",weekday:"MMMM YYYY",day:"MMMM YYYY",month:"YYYY",year:""}}}triggerView(){var t=this.start.clone().add(Math.round((this.end.unix()-this.start.unix())/2),"seconds"),t=t.format(this.getDateTime().internalDateFormat);this.date=t;this.trigger("view",t,this.mode)}initUserList(){if(this.options.userList){this.userList=Espo.Utils.clone(this.options.userList);this.userList.length||this.userList.push({id:this.getUser().id,name:this.getUser().get("name")})}else{this.userList=[];"single"===this.calendarType?this.options.userId?this.userList.push({id:this.options.userId,name:this.options.userName||this.options.userId}):this.userList.push({id:this.getUser().id,name:this.getUser().get("name")}):"shared"===this.calendarType&&this.getSharedCalenderUserList().forEach(t=>{this.userList.push({id:t.id,name:t.name})})}}storeUserList(){this.getPreferences().save({sharedCalendarUserList:Espo.Utils.clone(this.userList)},{patch:!0})}getSharedCalenderUserList(){var t=Espo.Utils.clone(this.getPreferences().get("sharedCalendarUserList"));if(t&&t.length){let e=!1;t.forEach(t=>{"object"==typeof t&&t.id&&t.name||(e=!0)});if(!e)return t}return[{id:this.getUser().id,name:this.getUser().get("name")}]}initDates(){this.date?this.start=n.default.tz(this.date,this.getDateTime().getTimeZone()):this.start=n.default.tz(this.getDateTime().getTimeZone());this.end=this.start.clone();this.end.add(1,"day");this.fetchedStart=null;this.fetchedEnd=null}initGroupsDataSet(){let i=[];this.userList.forEach((t,e)=>{i.push({id:t.id,content:this.getGroupContent(t.id,t.name),order:e})});this.groupsDataSet=new s.DataSet(i)}getGroupContent(t,e){if("single"===this.calendarType)return(0,r.default)("<span>").text(e).get(0).outerHTML;let i=this.getAvatarHtml(t);i&&(i+=" ");return i+(0,r.default)("<span>").attr("data-id",t).addClass("group-title").text(e).get(0).outerHTML}getAvatarHtml(t){if(this.getConfig().get("avatarsDisabled"))return"";let e;var i=this.getCache();e=i?i.get("app","timestamp"):Date.now();return(0,r.default)("<img>").addClass("avatar avatar-link").attr("width","16").attr("src",this.getBasePath()+"?entryPoint=avatar&size=small&id="+t+"&t="+e).get(0).outerHTML}fetchEvents(a,n,r){this.options.noFetchLoadingMessage||Espo.Ui.notifyWait();a=a.clone().add(-1*this.leftMargin,"seconds");n=n.clone().add(this.rightMargin,"seconds");var t=a.utc().format(this.getDateTime().internalDateTimeFormat),e=n.utc().format(this.getDateTime().internalDateTimeFormat);let i="Timeline?from="+t+"&to="+e;t=this.userList.map(t=>t.id);1===t.length?i+="&userId="+t[0]:i+="&userIdList="+encodeURIComponent(t.join(","));i+="&scopeList="+encodeURIComponent(this.enabledScopeList.join(","));Espo.Ajax.getRequest(i).then(t=>{this.fetchedStart=a.clone();this.fetchedEnd=n.clone();let i=[];for(let e in t){var s=t[e];s.forEach(t=>{t.userId=e;i.push(t)})}var e=this.convertEventList(i);r(e);Espo.Ui.notify(!1)})}async actionShowSharedCalendarOptions(){var t=new i.default({users:this.userList,onApply:t=>{this.userList=t.users;this.storeUserList();this.initGroupsDataSet();this.timeline.setGroups(this.groupsDataSet);this.runFetch()}});await this.assignView("modal",t);await t.render()}actionRefresh(){this.runFetch();let t=this.element.querySelector('button[data-action="refresh"] > span');if(t){t.classList.add("animation-spin-fast");setTimeout(()=>t.classList.remove("animation-spin-fast"),500)}}getColorFromScopeName(s){var t=this.getMetadata().get("clientDefs.Calendar.additionalColorList")||[];if(t.length){var a=this.getMetadata().get("clientDefs.Calendar.colors")||{},n=this.getConfig().get("calendarEntityList")||[];let e=0,i=0;for(let t=0;t<n.length;t++)if(!(n[t]in a)){if(n[t]===s){e=i;break}i++}e%=t.length;this.colors[s]=t[e];return this.colors[s]}}actionPrevious(){var t=this.timeline.getWindow().start;this.timeline.moveTo(t);this.triggerView()}actionNext(){var t=this.timeline.getWindow().end;this.timeline.moveTo(t);this.triggerView()}actionToday(){this.timeline.moveTo((0,n.default)().toDate());this.triggerView()}actionZoomOut(){this.timeline.zoomOut(this.zoomPercentage);this.triggerView()}actionZoomIn(){this.timeline.zoomIn(this.zoomPercentage)}}t.default=l});
//# sourceMappingURL=espo-timeline.js.map