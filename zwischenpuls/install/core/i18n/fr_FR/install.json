{"labels": {"Main page title": "Bienvenue sur EspoCRM", "Start page title": "Accord de Licence", "Step1 page title": "Accord de Licence", "License Agreement": "Accord de Licence", "I accept the agreement": "J'accepte l'accord de Licence", "Step2 page title": "Configuration de la base de données", "Step3 page title": "Configuration de l'Administrateur", "Step4 page title": "Paramètres du système", "Step5 page title": "Paramètres SMTP pour envoyer les emails", "Errors page title": "<PERSON><PERSON><PERSON>", "Finish page title": "L'installation est terminée", "Congratulation! Welcome to EspoCRM": "Félicitation! EspoCRM a été installé avec succès.", "share": "Si vous appréciez EspoCRM, partagez le avec vos amis. Faites leur connaître le produit", "Installation Guide": "Instructions d'installation", "Locale": "Région", "Outbound Email Configuration": "Configuration de l'envoi d'email", "Start": "Lancement", "Back": "Retour", "Next": "Suivant", "Go to EspoCRM": "Aller vers EspoCRM", "Re-check": "Revérifier", "Test settings": "Tester la connexion", "Database Settings Description": "Entrez vos informations de connexion à la base MYSQL (nom d'hôte, nom d'utilisateur et mot de passe). <PERSON><PERSON> pouvez spécifier le port serveur pour le nom d'hôte comme localhost:3306.", "Install": "Installation", "Configuration Instructions": "Instructions de configuration", "phpVersion": "Version PHP", "dbHostName": "Nom d'hôte", "dbName": "Nom de la base de donnée", "dbUserName": "Nom d'utilisateur de la base de données", "SetupConfirmation page title": "Configuration requise", "PHP Configuration": "Paramètres PHP", "MySQL Configuration": "Paramètres de base de données", "Permission Requirements": "Les permissions", "Success": "Su<PERSON>ès", "Fail": "<PERSON><PERSON><PERSON>", "is recommended": "est recommandé", "extension is missing": "l'extension est manquante", "headerTitle": "Installation EspoCRM", "Crontab setup instructions": "Sans exécuter les e-mails entrants des travaux planifiés, les notifications et les rappels ne fonctionneront pas. Ici vous pouvez lire {SETUP_INSTRUCTIONS}.", "Setup instructions": "instructions d'installation", "requiredMysqlVersion": "Version MySQL", "requiredMariadbVersion": "Version MariaDB", "requiredPostgresqlVersion": "Version PostgreSQL"}, "fields": {"Choose your language": "Choisissez votre langue", "Database Name": "Nom de la base de données", "Host Name": "Nom d'hôte", "Database User Name": "Nom d'utilisateur de la base de données", "Database User Password": "Mot de passe pour l'utilisateur de la base de données", "Database driver": "Pilote de base de données", "User Name": "Nom d'utilisateur", "Password": "Mot de passe", "smtpPassword": "Mot de passe", "Confirm Password": "Confirmer le mot de passe", "From Address": "<PERSON><PERSON><PERSON> <PERSON> l’émetteur", "From Name": "Nom de l'expéditeur", "Is Shared": "Est partagé", "Date Format": "Format de date", "Time Format": "Format de l'heure", "Time Zone": "<PERSON><PERSON> ho<PERSON>", "First Day of Week": "Premier j<PERSON> de la semaine", "Thousand Separator": "Séparateur de milliers", "Decimal Mark": "Marqueur décimal", "Default Currency": "Monnaie courante", "Currency List": "Liste des monnaies", "Language": "<PERSON><PERSON>", "smtpServer": "Ser<PERSON><PERSON>", "smtpAuth": "Autorisation", "smtpSecurity": "Sécurité", "smtpUsername": "Nom d'utilisateur", "Platform": "Plateforme"}, "messages": {"1049": "Base de donnée inconnue", "2005": "Hôte du server MYSQL est inconnu", "Some errors occurred!": "Des erreurs se sont produites !", "The PHP extension was not found...": "L'extension PHP <b>{extName}</b> est introuvable...", "All Settings correct": "Les Paramètres sont corrects", "Failed to connect to database": "Impossible de connecter la base de donnée", "PHP version": "Version PHP", "You must agree to the license agreement": "<PERSON><PERSON> devez agréer l'accord de licence", "Passwords do not match": "Les mots de passes sont différents", "Enable mod_rewrite in Apache server": "Activer mod_rewrite sur le serveur Apache", "checkWritable error": "Erreur de vérification d'écriture", "applySett error": "<PERSON><PERSON><PERSON> apply<PERSON>", "buildDatabase error": "Erreur dans la construction de la base de donnée", "createUser error": "Erreur dans la création de l'utilisateur", "checkAjaxPermission error": "Erreur checkAjaxPermission", "Cannot create user": "Impossible de créer l'utilisateur", "Permission denied": "Permission refusée", "Permission denied to": "Permission refusée", "Can not save settings": "Impossible d'enregistrer les paramètres", "Cannot save preferences": "Impossible d'enregistrer les préférences", "extension": "l'extension {0} est manquante", "option": "la valeur recommandée est {0}", "requiredMariadbVersion": "Votre version de MariaDB n’est pas supportée par EspoCRM, ve<PERSON><PERSON>z mettre à jour au moins vers MariaDB {minVersion}", "Ajax failed": "une erreur inattendue est apparue", "Bad init Permission": "Autorisation refusée pour le répertoire \\ \"{*} \". Veuillez définir 77 \"pour \" {*} \\ \"ou simplement exécuter cette commande dans le terminal <pre> <b> {C} </ b> </ pre> L'opération n'est pas autorisée? Essayez celui-ci: {CSU}", "permissionInstruction": "<br> Exécutez cette commande dans le terminal: <pre> <b> \\ \"{C} \" </ b> </ pre>", "operationNotPermitted": "L'opération n'est pas autorisée? Essayez celui-ci: <br> <br> {CSU}"}, "systemRequirements": {"requiredPhpVersion": "Version PHP", "requiredMysqlVersion": "Version MySQL", "host": "Nom d'hôte", "dbname": "Nom de la base de données", "user": "Nom d'utilisateur", "writable": "Enregistrable", "readable": "Lisible", "requiredMariadbVersion": "Version de MariaDB"}, "options": {"modRewriteTitle": {"apache": "<h3>Erreur API : l'API EspoCRM n'est pas disponible.</h3><br><PERSON><PERSON>z uniquement les étapes nécessaires. Après chaque étape, vérifiez si le problème est résolu.", "nginx": "<h3>Erreur API : EspoCRM API est indisponible.</h3>", "microsoft-iis": "<h3>Erreur API : l'API EspoCRM n'est pas disponible.</h3><br> Problème possible : \"URL Rewrite\" désactivée. Veuillez vérifier et activer le module \"URL Rewrite\" sur le serveur IIS.", "default": "\n<h3>Erreur API : l'API EspoCRM n'est pas disponible.</h3><br> Problèmes possibles : module Rewrite  désactivé. Veuillez vérifier et activer le module Rewrite sur votre serveur (par exemple mod_rewrite dans Apache) et la prise en charge .htaccess."}, "modRewriteInstruction": {"apache": {"linux": "<br><br><h4>1. <PERSON><PERSON> « mod_rewrite ».</h4><PERSON>ur activer <i>mod_rewrite</i>, exécutez les commandes suivantes dans un terminal:<br><br><pre>{APACHE1}</pre><hr><h4>2. Si l'étape précédente ne vous a pas aidé, essayez d'activer la prise en charge du . htaccess.</h4> Ajoutez/modifiez les paramètres de configuration du serveur <code>{APACHE2_PATH1}</code> ou <code>{APACHE2_PATH2}</code> (ou <code>{APACHE2_PATH3}</code>):<br><br><pre>{APACHE2}</pre>\n Ensuite, exécutez cette commande dans un terminal:<br><br><pre>{APACHE3}</pre><hr><h4>3. Si l'étape précédente n'a pas aidé, essayez d'ajouter le chemin RewriteBase.</h4>Ouvrez un fichier <code>{API_PATH}. htaccess</code> et remplacez la ligne suivante:<br><br><pre>{APACHE4}</pre>A<br><br><pre>{APACHE5}</pre><hr>Pour plus d'informations, veuillez consulter la ligne directrice <a href=« {APACHE_LINK} » target=« _blank »>Configuration du serveur Apache pour EspoCRM</a>.<br><br>", "windows": "<br><br> <h4>1. <PERSON><PERSON><PERSON> httpd.conf.</h4>En général, il se trouve dans un dossier appelé « conf », « config » ou quelque chose de ce genre.<br><br><h4>2. <PERSON><PERSON> httpd.conf.</h4> <PERSON><PERSON> le fichier httpd.conf, décommentez la ligne <code>{WINDOWS_APACHE1}</code> (supprimez le signe dièse '#' devant la ligne). 3. <br><br><h4>3. vérifiez les autres paramètres.</h4>Vérifiez également si la ligne <code>ClearModuleList</code> est décommentée et assurez-vous que la ligne <code>AddModule mod_rewrite.c</code> n'est pas commentée."}, "nginx": {"linux": "<br>Ajouter ce code à votre fichier de configuration du serveur Nginx <code>{NGINX_PATH}</code> dans la section « server »:<br><br><pre>{NGINX}</pre> <br>Pour plus d'informations, veuillez consulter le guide <a href=« {NGINX_LINK} » target=« _blank »>configuration du serveur Nginx pour EspoCRM</a>.<br><br>"}}}}