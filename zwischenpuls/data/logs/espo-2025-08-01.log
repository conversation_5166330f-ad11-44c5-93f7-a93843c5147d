[2025-08-01 18:41:52] WARNING: E_WARNING: Undefined property: stdClass::$listPrice
[2025-08-01 18:41:52] WARNING: E_WARNING: Undefined property: stdClass::$listPrice
[2025-08-01 23:00:48] ERROR: (0) Process 688d47209ad834bb3, element 688d4720a4436cece: Call to undefined method Espo\Core\Select\SelectBuilder::leftJoin() :: /var/www/html/custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php(87)
[2025-08-01 23:14:36] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Starting execution
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Parameters received
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Email addresses processed
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Searching for SalesOrders
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: findSalesOrders called
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Executing SalesOrder query
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Initial SalesOrders found
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Filtering by brand
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] INFO: GenerateAndSendPdf: Filtered SalesOrders by brand
[2025-08-01 23:16:18] INFO: GenerateAndSendPdf: SalesOrders found
[2025-08-01 23:16:18] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Starting execution
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Parameters received
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Email addresses processed
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Searching for SalesOrders
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: findSalesOrders called
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Executing SalesOrder query
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Initial SalesOrders found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Filtering by brand
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Filtered SalesOrders by brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrders found
[2025-08-01 23:20:09] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: Starting execution
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: PARAMETERS - Brand: [BWS], Template: 687ee1fcabfae7e40, Emails: <EMAIL>, WithDate: true
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: Email addresses processed
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: Searching for SalesOrders
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: findSalesOrders called
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: Executing SalesOrder query
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: INITIAL SALESORDERS FOUND: 8 - ID:678638f17d8e62336-NUM:SO-00030, ID:67863a71e9fdb6ace-NUM:SO-00031, ID:67864568aa78ebd23-NUM:SO-00032, ID:6798d4babf9a20fca-NUM:SO-00034, ID:67a5be4356f11252a-NUM:SO-00035, ID:67a5c40dbd140df1d-NUM:SO-00036, ID:67a5c7f6b135d1fcc-NUM:SO-00037, ID:688d0a7056c17e50d-NUM:SO-00038
[2025-08-01 23:26:18] INFO: GenerateAndSendPdf: Filtering by brand
[2025-08-01 23:26:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:26:18] ERROR: (0) Process 688d4d1a3fbba541a, element 688d4d1a4e3131f76: Object of class Espo\Modules\Sales\Entities\ProductBrand could not be converted to string :: /var/www/html/custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php(189)
[2025-08-01 23:26:18] INFO: BPM: triggerError
