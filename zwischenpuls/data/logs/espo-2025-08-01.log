[2025-08-01 18:41:52] WARNING: E_WARNING: Undefined property: stdClass::$listPrice
[2025-08-01 18:41:52] WARNING: E_WARNING: Undefined property: stdClass::$listPrice
[2025-08-01 23:00:48] ERROR: (0) Process 688d47209ad834bb3, element 688d4720a4436cece: Call to undefined method Espo\Core\Select\SelectBuilder::leftJoin() :: /var/www/html/custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php(87)
[2025-08-01 23:14:36] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Starting execution
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Parameters received
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Email addresses processed
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Searching for SalesOrders
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: findSalesOrders called
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Executing SalesOrder query
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Initial SalesOrders found
[2025-08-01 23:16:17] INFO: GenerateAndSendPdf: Filtering by brand
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:17] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:16:18] INFO: GenerateAndSendPdf: Filtered SalesOrders by brand
[2025-08-01 23:16:18] INFO: GenerateAndSendPdf: SalesOrders found
[2025-08-01 23:16:18] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Starting execution
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Parameters received
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Email addresses processed
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Searching for SalesOrders
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: findSalesOrders called
[2025-08-01 23:20:08] INFO: GenerateAndSendPdf: Executing SalesOrder query
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Initial SalesOrders found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Filtering by brand
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] NOTICE: E_USER_DEPRECATED: Accessing related records with Entity::get is deprecated. Use `$entityManager->getRelation(...)->find()`.
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking item product brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: No brand match found
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Checking SalesOrder for brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrder filtered out - no brand items
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: Filtered SalesOrders by brand
[2025-08-01 23:20:09] INFO: GenerateAndSendPdf: SalesOrders found
[2025-08-01 23:20:09] WARNING: GenerateAndSendPdf: No SalesOrders found matching criteria
