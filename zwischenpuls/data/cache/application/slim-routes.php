<?php return array (
  0 => 
  array (
    'GET' => 
    array (
      '/api/v1/integradial/get-lead' => 'route0',
      '/api/v1/integradial/get-form' => 'route1',
      '/api/v1/integradial/create-lead' => 'route2',
      '/api/v1/integradial/get-account-data' => 'route3',
      '/api/v1/integradial/create-call-activity' => 'route4',
      '/api/v1/integradial/webapps' => 'route8',
      '/api/v1/integradial/search-account' => 'route9',
      '/api/v1/ProductAttribute/options' => 'route18',
      '/api/v1/PriceRuleCondition/list' => 'route39',
      '/api/v1/Activities/upcoming' => 'route47',
      '/api/v1/Activities' => 'route48',
      '/api/v1/Timeline' => 'route49',
      '/api/v1/Timeline/busyRanges' => 'route50',
      '/api/v1/' => 'route59',
      '/api/v1/App/user' => 'route60',
      '/api/v1/App/about' => 'route62',
      '/api/v1/Metadata' => 'route63',
      '/api/v1/I18n' => 'route64',
      '/api/v1/Settings' => 'route65',
      '/api/v1/Stream' => 'route68',
      '/api/v1/GlobalStream' => 'route69',
      '/api/v1/GlobalSearch' => 'route70',
      '/api/v1/Admin/jobs' => 'route82',
      '/api/v1/CurrencyRate' => 'route88',
      '/api/v1/Email/inbox/notReadCounts' => 'route123',
      '/api/v1/Email/insertFieldData' => 'route124',
      '/api/v1/EmailAddress/search' => 'route126',
      '/api/v1/Oidc/authorizationData' => 'route136',
    ),
    'POST' => 
    array (
      '/api/v1/integradial/create-insertion' => 'route5',
      '/api/v1/ProductPrice/getSalesPrice' => 'route40',
      '/api/v1/ProductPrice/getPurchasePrice' => 'route41',
      '/api/v1/App/destroyAuthToken' => 'route61',
      '/api/v1/Admin/rebuild' => 'route80',
      '/api/v1/Admin/clearCache' => 'route81',
      '/api/v1/Action' => 'route90',
      '/api/v1/MassAction' => 'route91',
      '/api/v1/Export' => 'route94',
      '/api/v1/Import' => 'route97',
      '/api/v1/Import/file' => 'route98',
      '/api/v1/Attachment/fromImageUrl' => 'route107',
      '/api/v1/Email/importEml' => 'route114',
      '/api/v1/Email/sendTest' => 'route115',
      '/api/v1/Email/inbox/read' => 'route116',
      '/api/v1/Email/inbox/important' => 'route118',
      '/api/v1/Email/inbox/inTrash' => 'route120',
      '/api/v1/UserSecurity/apiKey/generate' => 'route129',
      '/api/v1/UserSecurity/password/recovery' => 'route131',
      '/api/v1/UserSecurity/password/generate' => 'route132',
      '/api/v1/User/passwordChangeRequest' => 'route133',
      '/api/v1/User/changePasswordByRequest' => 'route134',
      '/api/v1/Oidc/backchannelLogout' => 'route137',
    ),
    'PATCH' => 
    array (
      '/api/v1/Settings' => 'route66',
    ),
    'PUT' => 
    array (
      '/api/v1/Settings' => 'route67',
      '/api/v1/CurrencyRate' => 'route89',
      '/api/v1/Kanban/order' => 'route103',
      '/api/v1/UserSecurity/password' => 'route130',
    ),
    'DELETE' => 
    array (
      '/api/v1/Email/inbox/read' => 'route117',
      '/api/v1/Email/inbox/important' => 'route119',
      '/api/v1/Email/inbox/inTrash' => 'route121',
    ),
  ),
  1 => 
  array (
    'GET' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/integradial/test/([^/]+)|/api/v1/Product/([^/]+)/warehousesQuantity()|/api/v1/InventoryNumber/([^/]+)/warehousesQuantity()()|/api/v1/InventoryNumber/([^/]+)/history()()()|/api/v1/Warehouse/([^/]+)/products()()()()|/api/v1/Warehouse/([^/]+)/inventoryNumbers()()()()()|/api/v1/Product/([^/]+)/variantInventoryNumbers()()()()()()|/api/v1/Product/([^/]+)/orderItems()()()()()()()|/api/v1/Activities/([^/]+)/([^/]+)/composeEmailAddressList()()()()()()()|/api/v1/Activities/([^/]+)/([^/]+)/([^/]+)()()()()()()()|/api/v1/Activities/([^/]+)/([^/]+)/([^/]+)/list/([^/]+)()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route6',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route10',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route11',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route12',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          6 => 
          array (
            0 => 'route13',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route14',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route19',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route20',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route44',
            1 => 
            array (
              'parentType' => 'parentType',
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route45',
            1 => 
            array (
              'parentType' => 'parentType',
              'id' => 'id',
              'type' => 'type',
            ),
          ),
          12 => 
          array (
            0 => 'route46',
            1 => 
            array (
              'parentType' => 'parentType',
              'id' => 'id',
              'type' => 'type',
              'targetType' => 'targetType',
            ),
          ),
        ),
      ),
      1 => 
      array (
        'regex' => '~^(?|/api/v1/Meeting/([^/]+)/attendees|/api/v1/Call/([^/]+)/attendees()|/api/v1/TargetList/([^/]+)/optedOut()()|/api/v1/([^/]+)/action/([^/]+)()()|/api/v1/([^/]+)/layout/([^/]+)()()()|/api/v1/Admin/fieldManager/([^/]+)/([^/]+)()()()()|/api/v1/MassAction/([^/]+)/status()()()()()()|/api/v1/Export/([^/]+)/status()()()()()()()|/api/v1/Kanban/([^/]+)()()()()()()()()|/api/v1/Attachment/file/([^/]+)()()()()()()()()()|/api/v1/Note/([^/]+)/reactors/([^/]+)()()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route51',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route52',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route54',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route76',
            1 => 
            array (
              'controller' => 'controller',
              'action' => 'action',
            ),
          ),
          6 => 
          array (
            0 => 'route77',
            1 => 
            array (
              'controller' => 'controller',
              'name' => 'name',
            ),
          ),
          7 => 
          array (
            0 => 'route83',
            1 => 
            array (
              'scope' => 'scope',
              'name' => 'name',
            ),
          ),
          8 => 
          array (
            0 => 'route92',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route95',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route104',
            1 => 
            array (
              'entityType' => 'entityType',
            ),
          ),
          11 => 
          array (
            0 => 'route105',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          12 => 
          array (
            0 => 'route111',
            1 => 
            array (
              'id' => 'id',
              'type' => 'type',
            ),
          ),
        ),
      ),
      2 => 
      array (
        'regex' => '~^(?|/api/v1/User/([^/]+)/stream/own|/api/v1/User/([^/]+)/acl()|/api/v1/([^/]+)/([^/]+)()|/api/v1/([^/]+)()()()|/api/v1/([^/]+)/([^/]+)/followers()()()|/api/v1/([^/]+)/([^/]+)/stream()()()()|/api/v1/([^/]+)/([^/]+)/posts()()()()()|/api/v1/([^/]+)/([^/]+)/updateStream()()()()()()|/api/v1/([^/]+)/([^/]+)/streamAttachments()()()()()()()|/api/v1/([^/]+)/([^/]+)/usersAccess()()()()()()()()|/api/v1/([^/]+)/([^/]+)/([^/]+)()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route127',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route128',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route140',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route141',
            1 => 
            array (
              'controller' => 'controller',
            ),
          ),
          6 => 
          array (
            0 => 'route146',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route149',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route150',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route151',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route154',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route159',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          12 => 
          array (
            0 => 'route160',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
              'link' => 'link',
            ),
          ),
        ),
      ),
    ),
    'POST' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/Product/([^/]+)/generateVariants|/api/v1/ReceiptOrder/([^/]+)/importSerialNumbers()|/api/v1/ProductAttribute/([^/]+)/move/([^/]+)()|/api/v1/ProductAttributeOption/([^/]+)/move/([^/]+)()()|/api/v1/DeliveryOrder/([^/]+)/lock()()()()|/api/v1/DeliveryOrder/([^/]+)/unlock()()()()()|/api/v1/Invoice/([^/]+)/lock()()()()()()|/api/v1/Invoice/([^/]+)/unlock()()()()()()()|/api/v1/Quote/([^/]+)/lock()()()()()()()()|/api/v1/Quote/([^/]+)/unlock()()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route7',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route15',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route16',
            1 => 
            array (
              'id' => 'id',
              'type' => 'type',
            ),
          ),
          5 => 
          array (
            0 => 'route17',
            1 => 
            array (
              'id' => 'id',
              'type' => 'type',
            ),
          ),
          6 => 
          array (
            0 => 'route21',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route22',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route23',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route24',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route25',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route26',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
        ),
      ),
      1 => 
      array (
        'regex' => '~^(?|/api/v1/ReceiptOrder/([^/]+)/lock|/api/v1/ReceiptOrder/([^/]+)/unlock()|/api/v1/ReturnOrder/([^/]+)/lock()()|/api/v1/ReturnOrder/([^/]+)/unlock()()()|/api/v1/SalesOrder/([^/]+)/lock()()()()|/api/v1/SalesOrder/([^/]+)/unlock()()()()()|/api/v1/TransferOrder/([^/]+)/lock()()()()()()|/api/v1/TransferOrder/([^/]+)/unlock()()()()()()()|/api/v1/PurchaseOrder/([^/]+)/lock()()()()()()()()|/api/v1/PurchaseOrder/([^/]+)/unlock()()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route27',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route28',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route29',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route30',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          6 => 
          array (
            0 => 'route31',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route32',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route33',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route34',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route35',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route36',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
        ),
      ),
      2 => 
      array (
        'regex' => '~^(?|/api/v1/InventoryAdjustment/([^/]+)/lock|/api/v1/InventoryAdjustment/([^/]+)/unlock()|/api/v1/Invoice/([^/]+)/exportEInvoice()()|/api/v1/Invoice/([^/]+)/validateEInvoice()()()|/api/v1/Campaign/([^/]+)/generateMailMerge()()()()|/api/v1/Campaign/unsubscribe/([^/]+)()()()()()|/api/v1/Campaign/unsubscribe/([^/]+)/([^/]+)()()()()()|/api/v1/LeadCapture/form/([^/]+)()()()()()()()|/api/v1/LeadCapture/([^/]+)()()()()()()()()|/api/v1/([^/]+)/action/([^/]+)()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route37',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route38',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route42',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route43',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          6 => 
          array (
            0 => 'route53',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route55',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route57',
            1 => 
            array (
              'emailAddress' => 'emailAddress',
              'hash' => 'hash',
            ),
          ),
          9 => 
          array (
            0 => 'route71',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route72',
            1 => 
            array (
              'apiKey' => 'apiKey',
            ),
          ),
          11 => 
          array (
            0 => 'route74',
            1 => 
            array (
              'controller' => 'controller',
              'action' => 'action',
            ),
          ),
        ),
      ),
      3 => 
      array (
        'regex' => '~^(?|/api/v1/Admin/fieldManager/([^/]+)|/api/v1/MassAction/([^/]+)/subscribe()|/api/v1/Export/([^/]+)/subscribe()()|/api/v1/Import/([^/]+)/revert()()()|/api/v1/Import/([^/]+)/removeDuplicates()()()()|/api/v1/Import/([^/]+)/unmarkDuplicates()()()()()|/api/v1/Import/([^/]+)/exportErrors()()()()()()|/api/v1/Attachment/chunk/([^/]+)()()()()()()()|/api/v1/Attachment/copy/([^/]+)()()()()()()()()|/api/v1/Note/([^/]+)/myReactions/([^/]+)()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route84',
            1 => 
            array (
              'scope' => 'scope',
            ),
          ),
          3 => 
          array (
            0 => 'route93',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route96',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          5 => 
          array (
            0 => 'route99',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          6 => 
          array (
            0 => 'route100',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route101',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route102',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route106',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route108',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route109',
            1 => 
            array (
              'id' => 'id',
              'type' => 'type',
            ),
          ),
        ),
      ),
      4 => 
      array (
        'regex' => '~^(?|/api/v1/EmailTemplate/([^/]+)/prepare|/api/v1/Email/([^/]+)/attachments/copy()|/api/v1/Email/inbox/folders/([^/]+)()()|/api/v1/Email/([^/]+)/users()()()|/api/v1/OAuth/([^/]+)/connection()()()()|/api/v1/([^/]+)()()()()()|/api/v1/([^/]+)/([^/]+)/followers()()()()()|/api/v1/([^/]+)/([^/]+)/pin()()()()()()|/api/v1/([^/]+)/([^/]+)/([^/]+)()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route112',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route113',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          4 => 
          array (
            0 => 'route122',
            1 => 
            array (
              'folderId' => 'folderId',
            ),
          ),
          5 => 
          array (
            0 => 'route125',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          6 => 
          array (
            0 => 'route138',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route142',
            1 => 
            array (
              'controller' => 'controller',
            ),
          ),
          8 => 
          array (
            0 => 'route147',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route155',
            1 => 
            array (
              'Note' => 'Note',
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route161',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
              'link' => 'link',
            ),
          ),
        ),
      ),
    ),
    'DELETE' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/Campaign/unsubscribe/([^/]+)|/api/v1/Campaign/unsubscribe/([^/]+)/([^/]+)|/api/v1/Admin/fieldManager/([^/]+)/([^/]+)()|/api/v1/Note/([^/]+)/myReactions/([^/]+)()()|/api/v1/OAuth/([^/]+)/connection()()()()|/api/v1/([^/]+)/([^/]+)()()()()|/api/v1/([^/]+)/([^/]+)/followers()()()()()|/api/v1/([^/]+)/([^/]+)/subscription()()()()()()|/api/v1/([^/]+)/([^/]+)/pin()()()()()()()|/api/v1/([^/]+)/([^/]+)/starSubscription()()()()()()()()|/api/v1/([^/]+)/([^/]+)/([^/]+)()()()()()()()())$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route56',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          3 => 
          array (
            0 => 'route58',
            1 => 
            array (
              'emailAddress' => 'emailAddress',
              'hash' => 'hash',
            ),
          ),
          4 => 
          array (
            0 => 'route87',
            1 => 
            array (
              'scope' => 'scope',
              'name' => 'name',
            ),
          ),
          5 => 
          array (
            0 => 'route110',
            1 => 
            array (
              'id' => 'id',
              'type' => 'type',
            ),
          ),
          6 => 
          array (
            0 => 'route139',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          7 => 
          array (
            0 => 'route145',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route148',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route153',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route156',
            1 => 
            array (
              'Note' => 'Note',
              'id' => 'id',
            ),
          ),
          11 => 
          array (
            0 => 'route158',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
          12 => 
          array (
            0 => 'route162',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
              'link' => 'link',
            ),
          ),
        ),
      ),
    ),
    'OPTIONS' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/LeadCapture/([^/]+))$~',
        'routeMap' => 
        array (
          2 => 
          array (
            0 => 'route73',
            1 => 
            array (
              'apiKey' => 'apiKey',
            ),
          ),
        ),
      ),
    ),
    'PUT' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/([^/]+)/action/([^/]+)|/api/v1/([^/]+)/layout/([^/]+)()|/api/v1/([^/]+)/layout/([^/]+)/([^/]+)()|/api/v1/Admin/fieldManager/([^/]+)/([^/]+)()()()|/api/v1/Team/([^/]+)/userPosition()()()()()|/api/v1/([^/]+)/([^/]+)()()()()()|/api/v1/([^/]+)/([^/]+)/subscription()()()()()()|/api/v1/([^/]+)/([^/]+)/starSubscription()()()()()()())$~',
        'routeMap' => 
        array (
          3 => 
          array (
            0 => 'route75',
            1 => 
            array (
              'controller' => 'controller',
              'action' => 'action',
            ),
          ),
          4 => 
          array (
            0 => 'route78',
            1 => 
            array (
              'controller' => 'controller',
              'name' => 'name',
            ),
          ),
          5 => 
          array (
            0 => 'route79',
            1 => 
            array (
              'controller' => 'controller',
              'name' => 'name',
              'setId' => 'setId',
            ),
          ),
          6 => 
          array (
            0 => 'route85',
            1 => 
            array (
              'scope' => 'scope',
              'name' => 'name',
            ),
          ),
          7 => 
          array (
            0 => 'route135',
            1 => 
            array (
              'id' => 'id',
            ),
          ),
          8 => 
          array (
            0 => 'route143',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          9 => 
          array (
            0 => 'route152',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
          10 => 
          array (
            0 => 'route157',
            1 => 
            array (
              'entityType' => 'entityType',
              'id' => 'id',
            ),
          ),
        ),
      ),
    ),
    'PATCH' => 
    array (
      0 => 
      array (
        'regex' => '~^(?|/api/v1/Admin/fieldManager/([^/]+)/([^/]+)|/api/v1/([^/]+)/([^/]+)())$~',
        'routeMap' => 
        array (
          3 => 
          array (
            0 => 'route86',
            1 => 
            array (
              'scope' => 'scope',
              'name' => 'name',
            ),
          ),
          4 => 
          array (
            0 => 'route144',
            1 => 
            array (
              'controller' => 'controller',
              'id' => 'id',
            ),
          ),
        ),
      ),
    ),
  ),
);