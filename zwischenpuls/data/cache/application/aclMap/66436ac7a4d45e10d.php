<?php
return (object) [
  'table' => (object) [
    'ActionHistoryRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AddressCountry' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AppLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AppSecret' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ArrayValue' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Attachment' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AuthLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AuthToken' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'AuthenticationProvider' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Autofollow' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Currency' => (object) [
      'read' => 'yes',
      'stream' => 'no',
      'edit' => 'yes',
      'delete' => 'no',
      'create' => 'no'
    ],
    'Dashboard' => false,
    'DashboardTemplate' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Email' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailAccount' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailAccountScope' => true,
    'EmailAddress' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailFilter' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailFolder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailTemplate' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailTemplateCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Export' => false,
    'Extension' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'no',
      'delete' => 'all',
      'create' => 'no'
    ],
    'ExternalAccount' => true,
    'Formula' => false,
    'GlobalStream' => true,
    'GroupEmailFolder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Import' => true,
    'ImportEml' => true,
    'ImportError' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InboundEmail' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Integration' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Job' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'no',
      'delete' => 'all',
      'create' => 'no'
    ],
    'LastViewed' => false,
    'LayoutRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'LayoutSet' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'LeadCapture' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'LeadCaptureLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'MassAction' => false,
    'Note' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Notification' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'OAuthAccount' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'OAuthProvider' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PasswordChangeRequest' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PhoneNumber' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Portal' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PortalRole' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PortalUser' => false,
    'Preferences' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Role' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ScheduledJob' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ScheduledJobLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Stream' => true,
    'StreamSubscription' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Team' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Template' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'UniqueId' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'User' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'UserData' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'UserReaction' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Webhook' => true,
    'WebhookEventQueueItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'WebhookQueueItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'WorkingTimeCalendar' => true,
    'WorkingTimeRange' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Account' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Activities' => true,
    'Calendar' => true,
    'Call' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Campaign' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'CampaignLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'CampaignTrackingUrl' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Case' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Contact' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Document' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'DocumentFolder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'EmailQueueItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'KnowledgeBaseArticle' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'KnowledgeBaseCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Lead' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'MassEmail' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Meeting' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Opportunity' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Reminder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Target' => false,
    'TargetList' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'TargetListCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Task' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'BpmnFlowNode' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'BpmnFlowchart' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'BpmnProcess' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'BpmnSignalListener' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'BpmnUserTask' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'no'
    ],
    'Report' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReportCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReportFilter' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReportPanel' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Workflow' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'WorkflowCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'WorkflowLogRecord' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'WorkflowRoundRobin' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'DeliveryOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'DeliveryOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InventoryAdjustment' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InventoryAdjustmentItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InventoryNumber' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InventoryTransaction' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Invoice' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'InvoiceItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'OpportunityItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PriceBook' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PriceRule' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PriceRuleCondition' => (object) [
      'read' => 'all',
      'stream' => 'no',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Product' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ProductAttribute' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ProductAttributeOption' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ProductBrand' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ProductCategory' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ProductPrice' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PurchaseOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'PurchaseOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Quote' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'QuoteItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReceiptOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReceiptOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReturnOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ReturnOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'SalesOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'SalesOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'ShippingProvider' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Supplier' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'SupplierProductPrice' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Tax' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'TransferOrder' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'TransferOrderItem' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Warehouse' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'CInsertion' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ],
    'Place' => (object) [
      'read' => 'all',
      'stream' => 'all',
      'edit' => 'all',
      'delete' => 'all',
      'create' => 'yes'
    ]
  ],
  'fieldTable' => (object) [
    'ActionHistoryRecord' => (object) [],
    'AddressCountry' => (object) [],
    'AppLogRecord' => (object) [],
    'AppSecret' => (object) [],
    'ArrayValue' => (object) [],
    'Attachment' => (object) [],
    'AuthLogRecord' => (object) [],
    'AuthToken' => (object) [],
    'AuthenticationProvider' => (object) [],
    'Autofollow' => (object) [],
    'Currency' => (object) [],
    'DashboardTemplate' => (object) [],
    'Email' => (object) [],
    'EmailAccount' => (object) [],
    'EmailAddress' => (object) [],
    'EmailFilter' => (object) [],
    'EmailFolder' => (object) [],
    'EmailTemplate' => (object) [],
    'EmailTemplateCategory' => (object) [],
    'Extension' => (object) [],
    'ExternalAccount' => (object) [],
    'GroupEmailFolder' => (object) [],
    'Import' => (object) [],
    'ImportError' => (object) [],
    'InboundEmail' => (object) [],
    'Integration' => (object) [],
    'Job' => (object) [],
    'LayoutRecord' => (object) [],
    'LayoutSet' => (object) [],
    'LeadCapture' => (object) [],
    'LeadCaptureLogRecord' => (object) [],
    'Note' => (object) [],
    'Notification' => (object) [],
    'OAuthAccount' => (object) [],
    'OAuthProvider' => (object) [],
    'PasswordChangeRequest' => (object) [],
    'PhoneNumber' => (object) [],
    'Portal' => (object) [],
    'PortalRole' => (object) [],
    'Preferences' => (object) [],
    'Role' => (object) [],
    'ScheduledJob' => (object) [],
    'ScheduledJobLogRecord' => (object) [],
    'StreamSubscription' => (object) [],
    'Team' => (object) [],
    'Template' => (object) [],
    'UniqueId' => (object) [],
    'User' => (object) [],
    'UserData' => (object) [],
    'UserReaction' => (object) [],
    'Webhook' => (object) [],
    'WebhookEventQueueItem' => (object) [],
    'WebhookQueueItem' => (object) [],
    'WorkingTimeCalendar' => (object) [],
    'WorkingTimeRange' => (object) [],
    'Account' => (object) [],
    'Call' => (object) [],
    'Campaign' => (object) [],
    'CampaignLogRecord' => (object) [],
    'CampaignTrackingUrl' => (object) [],
    'Case' => (object) [],
    'Contact' => (object) [],
    'Document' => (object) [],
    'DocumentFolder' => (object) [],
    'EmailQueueItem' => (object) [],
    'KnowledgeBaseArticle' => (object) [],
    'KnowledgeBaseCategory' => (object) [],
    'Lead' => (object) [],
    'MassEmail' => (object) [],
    'Meeting' => (object) [],
    'Opportunity' => (object) [],
    'Reminder' => (object) [],
    'TargetList' => (object) [],
    'TargetListCategory' => (object) [],
    'Task' => (object) [],
    'BpmnFlowNode' => (object) [],
    'BpmnFlowchart' => (object) [],
    'BpmnProcess' => (object) [],
    'BpmnSignalListener' => (object) [],
    'BpmnUserTask' => (object) [],
    'Report' => (object) [],
    'ReportCategory' => (object) [],
    'ReportFilter' => (object) [],
    'ReportPanel' => (object) [],
    'Workflow' => (object) [],
    'WorkflowCategory' => (object) [],
    'WorkflowLogRecord' => (object) [],
    'WorkflowRoundRobin' => (object) [],
    'DeliveryOrder' => (object) [],
    'DeliveryOrderItem' => (object) [],
    'InventoryAdjustment' => (object) [],
    'InventoryAdjustmentItem' => (object) [],
    'InventoryNumber' => (object) [],
    'InventoryTransaction' => (object) [],
    'Invoice' => (object) [],
    'InvoiceItem' => (object) [],
    'OpportunityItem' => (object) [],
    'PriceBook' => (object) [],
    'PriceRule' => (object) [],
    'PriceRuleCondition' => (object) [],
    'Product' => (object) [],
    'ProductAttribute' => (object) [],
    'ProductAttributeOption' => (object) [],
    'ProductBrand' => (object) [],
    'ProductCategory' => (object) [],
    'ProductPrice' => (object) [],
    'PurchaseOrder' => (object) [],
    'PurchaseOrderItem' => (object) [],
    'Quote' => (object) [],
    'QuoteItem' => (object) [],
    'ReceiptOrder' => (object) [],
    'ReceiptOrderItem' => (object) [],
    'ReturnOrder' => (object) [],
    'ReturnOrderItem' => (object) [],
    'SalesOrder' => (object) [],
    'SalesOrderItem' => (object) [],
    'ShippingProvider' => (object) [],
    'Supplier' => (object) [],
    'SupplierProductPrice' => (object) [],
    'Tax' => (object) [],
    'TransferOrder' => (object) [],
    'TransferOrderItem' => (object) [],
    'Warehouse' => (object) [],
    'CInsertion' => (object) [],
    'Place' => (object) []
  ],
  'assignmentPermission' => 'all',
  'messagePermission' => 'all',
  'mentionPermission' => 'yes',
  'userCalendarPermission' => 'all',
  'auditPermission' => 'yes',
  'exportPermission' => 'yes',
  'massUpdatePermission' => 'yes',
  'userPermission' => 'all',
  'portalPermission' => 'yes',
  'groupEmailAccountPermission' => 'all',
  'followerManagementPermission' => 'all',
  'dataPrivacyPermission' => 'yes',
  'fieldTableQuickAccess' => (object) [
    'ActionHistoryRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AddressCountry' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AppLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AppSecret' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ArrayValue' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Attachment' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AuthLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AuthToken' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'AuthenticationProvider' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Autofollow' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Currency' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'DashboardTemplate' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Email' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailAccount' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailAddress' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailFilter' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailFolder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailTemplate' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailTemplateCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Extension' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ExternalAccount' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'GroupEmailFolder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Import' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ImportError' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InboundEmail' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Integration' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Job' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'LayoutRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'LayoutSet' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'LeadCapture' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'LeadCaptureLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Note' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Notification' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'OAuthAccount' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'OAuthProvider' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PasswordChangeRequest' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PhoneNumber' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Portal' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PortalRole' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Preferences' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Role' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ScheduledJob' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ScheduledJobLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'StreamSubscription' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Team' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Template' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'UniqueId' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'User' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'UserData' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'UserReaction' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Webhook' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WebhookEventQueueItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WebhookQueueItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WorkingTimeCalendar' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WorkingTimeRange' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Account' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Call' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Campaign' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'CampaignLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'CampaignTrackingUrl' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Case' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Contact' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Document' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'DocumentFolder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'EmailQueueItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'KnowledgeBaseArticle' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'KnowledgeBaseCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Lead' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'MassEmail' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Meeting' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Opportunity' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Reminder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'TargetList' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'TargetListCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Task' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'BpmnFlowNode' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'BpmnFlowchart' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'BpmnProcess' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'BpmnSignalListener' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'BpmnUserTask' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Report' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReportCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReportFilter' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReportPanel' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Workflow' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WorkflowCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WorkflowLogRecord' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'WorkflowRoundRobin' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'DeliveryOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'DeliveryOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InventoryAdjustment' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InventoryAdjustmentItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InventoryNumber' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InventoryTransaction' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Invoice' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'InvoiceItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'OpportunityItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PriceBook' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PriceRule' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PriceRuleCondition' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Product' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ProductAttribute' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ProductAttributeOption' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ProductBrand' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ProductCategory' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ProductPrice' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PurchaseOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'PurchaseOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Quote' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'QuoteItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReceiptOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReceiptOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReturnOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ReturnOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'SalesOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'SalesOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'ShippingProvider' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Supplier' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'SupplierProductPrice' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Tax' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'TransferOrder' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'TransferOrderItem' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Warehouse' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'CInsertion' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ],
    'Place' => (object) [
      'attributes' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ],
      'fields' => (object) [
        'read' => (object) [
          'yes' => [],
          'no' => []
        ],
        'edit' => (object) [
          'yes' => [],
          'no' => []
        ]
      ]
    ]
  ]
];
