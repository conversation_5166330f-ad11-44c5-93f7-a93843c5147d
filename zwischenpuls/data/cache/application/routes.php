<?php
return [
  0 => [
    'route' => '/integradial/get-lead',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\GetLead',
    'adjustedRoute' => '/integradial/get-lead'
  ],
  1 => [
    'route' => '/integradial/get-form',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\GetForm',
    'adjustedRoute' => '/integradial/get-form'
  ],
  2 => [
    'route' => '/integradial/create-lead',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\CreateLead',
    'noAuth' => true,
    'adjustedRoute' => '/integradial/create-lead'
  ],
  3 => [
    'route' => '/integradial/get-account-data',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\GetAccountData',
    'noAuth' => true,
    'adjustedRoute' => '/integradial/get-account-data'
  ],
  4 => [
    'route' => '/integradial/create-call-activity',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\CreateCallActivity',
    'noAuth' => true,
    'adjustedRoute' => '/integradial/create-call-activity'
  ],
  5 => [
    'route' => '/integradial/create-insertion',
    'method' => 'post',
    'actionClassName' => 'Espo\\Custom\\Actions\\CreateInsertion',
    'noAuth' => true,
    'adjustedRoute' => '/integradial/create-insertion'
  ],
  6 => [
    'route' => '/integradial/test/:id',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\Test',
    'noAuth' => true,
    'adjustedRoute' => '/integradial/test/{id}'
  ],
  7 => [
    'route' => 'Product/:id/generateVariants',
    'method' => 'post',
    'actionClassName' => 'Espo\\Custom\\Tools\\Product\\Api\\PostGenerateVariants',
    'adjustedRoute' => '/Product/{id}/generateVariants'
  ],
  8 => [
    'route' => '/integradial/webapps',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\WebRoot',
    'adjustedRoute' => '/integradial/webapps'
  ],
  9 => [
    'route' => '/integradial/search-account',
    'method' => 'get',
    'actionClassName' => 'Espo\\Custom\\Actions\\SearchExistingAccountByPhone',
    'adjustedRoute' => '/integradial/search-account'
  ],
  10 => [
    'route' => 'Product/:id/warehousesQuantity',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Product\\Api\\GetWarehousesQuantity',
    'adjustedRoute' => '/Product/{id}/warehousesQuantity'
  ],
  11 => [
    'route' => 'InventoryNumber/:id/warehousesQuantity',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\InventoryNumber\\Api\\GetWarehousesQuantity',
    'adjustedRoute' => '/InventoryNumber/{id}/warehousesQuantity'
  ],
  12 => [
    'route' => 'InventoryNumber/:id/history',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\InventoryNumber\\Api\\GetHistory',
    'adjustedRoute' => '/InventoryNumber/{id}/history'
  ],
  13 => [
    'route' => 'Warehouse/:id/products',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Warehouse\\Api\\GetProducts',
    'adjustedRoute' => '/Warehouse/{id}/products'
  ],
  14 => [
    'route' => 'Warehouse/:id/inventoryNumbers',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Warehouse\\Api\\GetInventoryNumbers',
    'adjustedRoute' => '/Warehouse/{id}/inventoryNumbers'
  ],
  15 => [
    'route' => 'ReceiptOrder/:id/importSerialNumbers',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\ReceiptOrder\\Api\\PostImportSerialNumbers',
    'adjustedRoute' => '/ReceiptOrder/{id}/importSerialNumbers'
  ],
  16 => [
    'route' => 'ProductAttribute/:id/move/:type',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\ProductAttribute\\Api\\PostMove',
    'adjustedRoute' => '/ProductAttribute/{id}/move/{type}'
  ],
  17 => [
    'route' => 'ProductAttributeOption/:id/move/:type',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\ProductAttribute\\Api\\PostMoveOption',
    'adjustedRoute' => '/ProductAttributeOption/{id}/move/{type}'
  ],
  18 => [
    'route' => 'ProductAttribute/options',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\ProductAttribute\\Api\\GetOptions',
    'adjustedRoute' => '/ProductAttribute/options'
  ],
  19 => [
    'route' => 'Product/:id/variantInventoryNumbers',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Product\\Api\\GetVariantInventoryNumbers',
    'adjustedRoute' => '/Product/{id}/variantInventoryNumbers'
  ],
  20 => [
    'route' => 'Product/:id/orderItems',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Product\\Api\\GetOrderItems',
    'adjustedRoute' => '/Product/{id}/orderItems'
  ],
  21 => [
    'route' => 'DeliveryOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'DeliveryOrder'
    ],
    'adjustedRoute' => '/DeliveryOrder/{id}/lock'
  ],
  22 => [
    'route' => 'DeliveryOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'DeliveryOrder'
    ],
    'adjustedRoute' => '/DeliveryOrder/{id}/unlock'
  ],
  23 => [
    'route' => 'Invoice/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'Invoice'
    ],
    'adjustedRoute' => '/Invoice/{id}/lock'
  ],
  24 => [
    'route' => 'Invoice/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'Invoice'
    ],
    'adjustedRoute' => '/Invoice/{id}/unlock'
  ],
  25 => [
    'route' => 'Quote/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'Quote'
    ],
    'adjustedRoute' => '/Quote/{id}/lock'
  ],
  26 => [
    'route' => 'Quote/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'Quote'
    ],
    'adjustedRoute' => '/Quote/{id}/unlock'
  ],
  27 => [
    'route' => 'ReceiptOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'ReceiptOrder'
    ],
    'adjustedRoute' => '/ReceiptOrder/{id}/lock'
  ],
  28 => [
    'route' => 'ReceiptOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'ReceiptOrder'
    ],
    'adjustedRoute' => '/ReceiptOrder/{id}/unlock'
  ],
  29 => [
    'route' => 'ReturnOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'ReturnOrder'
    ],
    'adjustedRoute' => '/ReturnOrder/{id}/lock'
  ],
  30 => [
    'route' => 'ReturnOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'ReturnOrder'
    ],
    'adjustedRoute' => '/ReturnOrder/{id}/unlock'
  ],
  31 => [
    'route' => 'SalesOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'SalesOrder'
    ],
    'adjustedRoute' => '/SalesOrder/{id}/lock'
  ],
  32 => [
    'route' => 'SalesOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'SalesOrder'
    ],
    'adjustedRoute' => '/SalesOrder/{id}/unlock'
  ],
  33 => [
    'route' => 'TransferOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'TransferOrder'
    ],
    'adjustedRoute' => '/TransferOrder/{id}/lock'
  ],
  34 => [
    'route' => 'TransferOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'TransferOrder'
    ],
    'adjustedRoute' => '/TransferOrder/{id}/unlock'
  ],
  35 => [
    'route' => 'PurchaseOrder/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'PurchaseOrder'
    ],
    'adjustedRoute' => '/PurchaseOrder/{id}/lock'
  ],
  36 => [
    'route' => 'PurchaseOrder/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'PurchaseOrder'
    ],
    'adjustedRoute' => '/PurchaseOrder/{id}/unlock'
  ],
  37 => [
    'route' => 'InventoryAdjustment/:id/lock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostLock',
    'params' => [
      'entityType' => 'InventoryAdjustment'
    ],
    'adjustedRoute' => '/InventoryAdjustment/{id}/lock'
  ],
  38 => [
    'route' => 'InventoryAdjustment/:id/unlock',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Quote\\Api\\PostUnlock',
    'params' => [
      'entityType' => 'InventoryAdjustment'
    ],
    'adjustedRoute' => '/InventoryAdjustment/{id}/unlock'
  ],
  39 => [
    'route' => 'PriceRuleCondition/list',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Price\\Api\\GetPriceRuleConditionList',
    'adjustedRoute' => '/PriceRuleCondition/list'
  ],
  40 => [
    'route' => 'ProductPrice/getSalesPrice',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Price\\Api\\PostGetSalesPrice',
    'adjustedRoute' => '/ProductPrice/getSalesPrice'
  ],
  41 => [
    'route' => 'ProductPrice/getPurchasePrice',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Price\\Api\\PostGetPurchasePrice',
    'adjustedRoute' => '/ProductPrice/getPurchasePrice'
  ],
  42 => [
    'route' => 'Invoice/:id/exportEInvoice',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Invoice\\Api\\PostExportEInvoice',
    'adjustedRoute' => '/Invoice/{id}/exportEInvoice'
  ],
  43 => [
    'route' => 'Invoice/:id/validateEInvoice',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Sales\\Tools\\Invoice\\Api\\PostValidateEInvoice',
    'adjustedRoute' => '/Invoice/{id}/validateEInvoice'
  ],
  44 => [
    'route' => '/Activities/:parentType/:id/composeEmailAddressList',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Activities\\Api\\GetComposeAddressList',
    'adjustedRoute' => '/Activities/{parentType}/{id}/composeEmailAddressList'
  ],
  45 => [
    'route' => '/Activities/:parentType/:id/:type',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Activities\\Api\\Get',
    'adjustedRoute' => '/Activities/{parentType}/{id}/{type}'
  ],
  46 => [
    'route' => '/Activities/:parentType/:id/:type/list/:targetType',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Activities\\Api\\GetListTyped',
    'adjustedRoute' => '/Activities/{parentType}/{id}/{type}/list/{targetType}'
  ],
  47 => [
    'route' => '/Activities/upcoming',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Activities\\Api\\GetUpcoming',
    'adjustedRoute' => '/Activities/upcoming'
  ],
  48 => [
    'route' => '/Activities',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Calendar\\Api\\GetCalendar',
    'adjustedRoute' => '/Activities'
  ],
  49 => [
    'route' => '/Timeline',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Calendar\\Api\\GetTimeline',
    'adjustedRoute' => '/Timeline'
  ],
  50 => [
    'route' => '/Timeline/busyRanges',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Calendar\\Api\\GetBusyRanges',
    'adjustedRoute' => '/Timeline/busyRanges'
  ],
  51 => [
    'route' => '/Meeting/:id/attendees',
    'method' => 'get',
    'params' => [
      'controller' => 'Meeting',
      'action' => 'attendees'
    ],
    'adjustedRoute' => '/Meeting/{id}/attendees'
  ],
  52 => [
    'route' => '/Call/:id/attendees',
    'method' => 'get',
    'params' => [
      'controller' => 'Call',
      'action' => 'attendees'
    ],
    'adjustedRoute' => '/Call/{id}/attendees'
  ],
  53 => [
    'route' => '/Campaign/:id/generateMailMerge',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\Campaign\\Api\\PostGenerateMailMerge',
    'adjustedRoute' => '/Campaign/{id}/generateMailMerge'
  ],
  54 => [
    'route' => '/TargetList/:id/optedOut',
    'method' => 'get',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\TargetList\\Api\\GetOptedOut',
    'adjustedRoute' => '/TargetList/{id}/optedOut'
  ],
  55 => [
    'route' => '/Campaign/unsubscribe/:id',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\MassEmail\\Api\\PostUnsubscribe',
    'noAuth' => true,
    'adjustedRoute' => '/Campaign/unsubscribe/{id}'
  ],
  56 => [
    'route' => '/Campaign/unsubscribe/:id',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\MassEmail\\Api\\DeleteUnsubscribe',
    'noAuth' => true,
    'adjustedRoute' => '/Campaign/unsubscribe/{id}'
  ],
  57 => [
    'route' => '/Campaign/unsubscribe/:emailAddress/:hash',
    'method' => 'post',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\MassEmail\\Api\\PostUnsubscribe',
    'noAuth' => true,
    'adjustedRoute' => '/Campaign/unsubscribe/{emailAddress}/{hash}'
  ],
  58 => [
    'route' => '/Campaign/unsubscribe/:emailAddress/:hash',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Modules\\Crm\\Tools\\MassEmail\\Api\\DeleteUnsubscribe',
    'noAuth' => true,
    'adjustedRoute' => '/Campaign/unsubscribe/{emailAddress}/{hash}'
  ],
  59 => [
    'route' => '/',
    'method' => 'get',
    'params' => [
      'controller' => 'ApiIndex',
      'action' => 'index'
    ],
    'adjustedRoute' => '/'
  ],
  60 => [
    'route' => '/App/user',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\App\\Api\\GetUser',
    'adjustedRoute' => '/App/user'
  ],
  61 => [
    'route' => '/App/destroyAuthToken',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\App\\Api\\PostDestroyAuthToken',
    'adjustedRoute' => '/App/destroyAuthToken'
  ],
  62 => [
    'route' => '/App/about',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\App\\Api\\GetAbout',
    'adjustedRoute' => '/App/about'
  ],
  63 => [
    'route' => '/Metadata',
    'method' => 'get',
    'params' => [
      'controller' => 'Metadata'
    ],
    'adjustedRoute' => '/Metadata'
  ],
  64 => [
    'route' => '/I18n',
    'method' => 'get',
    'params' => [
      'controller' => 'I18n'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/I18n'
  ],
  65 => [
    'route' => '/Settings',
    'method' => 'get',
    'params' => [
      'controller' => 'Settings'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/Settings'
  ],
  66 => [
    'route' => '/Settings',
    'method' => 'patch',
    'params' => [
      'controller' => 'Settings'
    ],
    'adjustedRoute' => '/Settings'
  ],
  67 => [
    'route' => '/Settings',
    'method' => 'put',
    'params' => [
      'controller' => 'Settings'
    ],
    'adjustedRoute' => '/Settings'
  ],
  68 => [
    'route' => '/Stream',
    'method' => 'get',
    'params' => [
      'controller' => 'Stream',
      'action' => 'list',
      'scope' => 'User'
    ],
    'adjustedRoute' => '/Stream'
  ],
  69 => [
    'route' => '/GlobalStream',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\GetGlobal',
    'adjustedRoute' => '/GlobalStream'
  ],
  70 => [
    'route' => '/GlobalSearch',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\GlobalSearch\\Api\\Get',
    'adjustedRoute' => '/GlobalSearch'
  ],
  71 => [
    'route' => '/LeadCapture/form/:id',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\LeadCapture\\Api\\PostForm',
    'noAuth' => true,
    'adjustedRoute' => '/LeadCapture/form/{id}'
  ],
  72 => [
    'route' => '/LeadCapture/:apiKey',
    'method' => 'post',
    'params' => [
      'controller' => 'LeadCapture',
      'action' => 'leadCapture',
      'apiKey' => ':apiKey'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/LeadCapture/{apiKey}'
  ],
  73 => [
    'route' => '/LeadCapture/:apiKey',
    'method' => 'options',
    'params' => [
      'controller' => 'LeadCapture',
      'action' => 'leadCapture',
      'apiKey' => ':apiKey'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/LeadCapture/{apiKey}'
  ],
  74 => [
    'route' => '/:controller/action/:action',
    'method' => 'post',
    'params' => [
      'controller' => ':controller',
      'action' => ':action'
    ],
    'adjustedRoute' => '/{controller}/action/{action}'
  ],
  75 => [
    'route' => '/:controller/action/:action',
    'method' => 'put',
    'params' => [
      'controller' => ':controller',
      'action' => ':action'
    ],
    'adjustedRoute' => '/{controller}/action/{action}'
  ],
  76 => [
    'route' => '/:controller/action/:action',
    'method' => 'get',
    'params' => [
      'controller' => ':controller',
      'action' => ':action'
    ],
    'adjustedRoute' => '/{controller}/action/{action}'
  ],
  77 => [
    'route' => '/:controller/layout/:name',
    'method' => 'get',
    'params' => [
      'controller' => 'Layout',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/layout/{name}'
  ],
  78 => [
    'route' => '/:controller/layout/:name',
    'method' => 'put',
    'params' => [
      'controller' => 'Layout',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/layout/{name}'
  ],
  79 => [
    'route' => '/:controller/layout/:name/:setId',
    'method' => 'put',
    'params' => [
      'controller' => 'Layout',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/layout/{name}/{setId}'
  ],
  80 => [
    'route' => '/Admin/rebuild',
    'method' => 'post',
    'params' => [
      'controller' => 'Admin',
      'action' => 'rebuild'
    ],
    'adjustedRoute' => '/Admin/rebuild'
  ],
  81 => [
    'route' => '/Admin/clearCache',
    'method' => 'post',
    'params' => [
      'controller' => 'Admin',
      'action' => 'clearCache'
    ],
    'adjustedRoute' => '/Admin/clearCache'
  ],
  82 => [
    'route' => '/Admin/jobs',
    'method' => 'get',
    'params' => [
      'controller' => 'Admin',
      'action' => 'jobs'
    ],
    'adjustedRoute' => '/Admin/jobs'
  ],
  83 => [
    'route' => '/Admin/fieldManager/:scope/:name',
    'method' => 'get',
    'params' => [
      'controller' => 'FieldManager',
      'action' => 'read',
      'scope' => ':scope',
      'name' => ':name'
    ],
    'adjustedRoute' => '/Admin/fieldManager/{scope}/{name}'
  ],
  84 => [
    'route' => '/Admin/fieldManager/:scope',
    'method' => 'post',
    'params' => [
      'controller' => 'FieldManager',
      'action' => 'create',
      'scope' => ':scope'
    ],
    'adjustedRoute' => '/Admin/fieldManager/{scope}'
  ],
  85 => [
    'route' => '/Admin/fieldManager/:scope/:name',
    'method' => 'put',
    'params' => [
      'controller' => 'FieldManager',
      'action' => 'update',
      'scope' => ':scope',
      'name' => ':name'
    ],
    'adjustedRoute' => '/Admin/fieldManager/{scope}/{name}'
  ],
  86 => [
    'route' => '/Admin/fieldManager/:scope/:name',
    'method' => 'patch',
    'params' => [
      'controller' => 'FieldManager',
      'action' => 'update',
      'scope' => ':scope',
      'name' => ':name'
    ],
    'adjustedRoute' => '/Admin/fieldManager/{scope}/{name}'
  ],
  87 => [
    'route' => '/Admin/fieldManager/:scope/:name',
    'method' => 'delete',
    'params' => [
      'controller' => 'FieldManager',
      'action' => 'delete',
      'scope' => ':scope',
      'name' => ':name'
    ],
    'adjustedRoute' => '/Admin/fieldManager/{scope}/{name}'
  ],
  88 => [
    'route' => '/CurrencyRate',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Currency\\Api\\Get',
    'adjustedRoute' => '/CurrencyRate'
  ],
  89 => [
    'route' => '/CurrencyRate',
    'method' => 'put',
    'actionClassName' => 'Espo\\Tools\\Currency\\Api\\PutUpdate',
    'adjustedRoute' => '/CurrencyRate'
  ],
  90 => [
    'route' => '/Action',
    'method' => 'post',
    'actionClassName' => 'Espo\\Core\\Action\\Api\\PostProcess',
    'adjustedRoute' => '/Action'
  ],
  91 => [
    'route' => '/MassAction',
    'method' => 'post',
    'actionClassName' => 'Espo\\Core\\MassAction\\Api\\PostProcess',
    'adjustedRoute' => '/MassAction'
  ],
  92 => [
    'route' => '/MassAction/:id/status',
    'method' => 'get',
    'actionClassName' => 'Espo\\Core\\MassAction\\Api\\GetStatus',
    'adjustedRoute' => '/MassAction/{id}/status'
  ],
  93 => [
    'route' => '/MassAction/:id/subscribe',
    'method' => 'post',
    'actionClassName' => 'Espo\\Core\\MassAction\\Api\\PostSubscribe',
    'adjustedRoute' => '/MassAction/{id}/subscribe'
  ],
  94 => [
    'route' => '/Export',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Export\\Api\\PostProcess',
    'adjustedRoute' => '/Export'
  ],
  95 => [
    'route' => '/Export/:id/status',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Export\\Api\\GetStatus',
    'adjustedRoute' => '/Export/{id}/status'
  ],
  96 => [
    'route' => '/Export/:id/subscribe',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Export\\Api\\PostSubscribe',
    'adjustedRoute' => '/Export/{id}/subscribe'
  ],
  97 => [
    'route' => '/Import',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\Post',
    'adjustedRoute' => '/Import'
  ],
  98 => [
    'route' => '/Import/file',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\PostFile',
    'adjustedRoute' => '/Import/file'
  ],
  99 => [
    'route' => '/Import/:id/revert',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\PostRevert',
    'adjustedRoute' => '/Import/{id}/revert'
  ],
  100 => [
    'route' => '/Import/:id/removeDuplicates',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\PostRemoveDuplicates',
    'adjustedRoute' => '/Import/{id}/removeDuplicates'
  ],
  101 => [
    'route' => '/Import/:id/unmarkDuplicates',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\PostUnmarkDuplicates',
    'adjustedRoute' => '/Import/{id}/unmarkDuplicates'
  ],
  102 => [
    'route' => '/Import/:id/exportErrors',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Import\\Api\\PostExportErrors',
    'adjustedRoute' => '/Import/{id}/exportErrors'
  ],
  103 => [
    'route' => '/Kanban/order',
    'method' => 'put',
    'actionClassName' => 'Espo\\Tools\\Kanban\\Api\\PutOrder',
    'adjustedRoute' => '/Kanban/order'
  ],
  104 => [
    'route' => '/Kanban/:entityType',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Kanban\\Api\\GetData',
    'adjustedRoute' => '/Kanban/{entityType}'
  ],
  105 => [
    'route' => '/Attachment/file/:id',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Attachment\\Api\\GetFile',
    'adjustedRoute' => '/Attachment/file/{id}'
  ],
  106 => [
    'route' => '/Attachment/chunk/:id',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Attachment\\Api\\PostChunk',
    'adjustedRoute' => '/Attachment/chunk/{id}'
  ],
  107 => [
    'route' => '/Attachment/fromImageUrl',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Attachment\\Api\\PostFromImageUrl',
    'adjustedRoute' => '/Attachment/fromImageUrl'
  ],
  108 => [
    'route' => '/Attachment/copy/:id',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Attachment\\Api\\PostCopy',
    'adjustedRoute' => '/Attachment/copy/{id}'
  ],
  109 => [
    'route' => '/Note/:id/myReactions/:type',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\PostMyReactions',
    'adjustedRoute' => '/Note/{id}/myReactions/{type}'
  ],
  110 => [
    'route' => '/Note/:id/myReactions/:type',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\DeleteMyReactions',
    'adjustedRoute' => '/Note/{id}/myReactions/{type}'
  ],
  111 => [
    'route' => '/Note/:id/reactors/:type',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\GetNoteReactors',
    'adjustedRoute' => '/Note/{id}/reactors/{type}'
  ],
  112 => [
    'route' => '/EmailTemplate/:id/prepare',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\EmailTemplate\\Api\\PostPrepare',
    'adjustedRoute' => '/EmailTemplate/{id}/prepare'
  ],
  113 => [
    'route' => '/Email/:id/attachments/copy',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostAttachmentsCopy',
    'adjustedRoute' => '/Email/{id}/attachments/copy'
  ],
  114 => [
    'route' => '/Email/importEml',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostImportEml',
    'adjustedRoute' => '/Email/importEml'
  ],
  115 => [
    'route' => '/Email/sendTest',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostSendTest',
    'adjustedRoute' => '/Email/sendTest'
  ],
  116 => [
    'route' => '/Email/inbox/read',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostInboxRead',
    'adjustedRoute' => '/Email/inbox/read'
  ],
  117 => [
    'route' => '/Email/inbox/read',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\DeleteInboxRead',
    'adjustedRoute' => '/Email/inbox/read'
  ],
  118 => [
    'route' => '/Email/inbox/important',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostInboxImportant',
    'adjustedRoute' => '/Email/inbox/important'
  ],
  119 => [
    'route' => '/Email/inbox/important',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\DeleteInboxImportant',
    'adjustedRoute' => '/Email/inbox/important'
  ],
  120 => [
    'route' => '/Email/inbox/inTrash',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostInboxInTrash',
    'adjustedRoute' => '/Email/inbox/inTrash'
  ],
  121 => [
    'route' => '/Email/inbox/inTrash',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\DeleteInboxInTrash',
    'adjustedRoute' => '/Email/inbox/inTrash'
  ],
  122 => [
    'route' => '/Email/inbox/folders/:folderId',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostFolder',
    'adjustedRoute' => '/Email/inbox/folders/{folderId}'
  ],
  123 => [
    'route' => '/Email/inbox/notReadCounts',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\GetNotReadCounts',
    'adjustedRoute' => '/Email/inbox/notReadCounts'
  ],
  124 => [
    'route' => '/Email/insertFieldData',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\GetInsertFieldData',
    'adjustedRoute' => '/Email/insertFieldData'
  ],
  125 => [
    'route' => '/Email/:id/users',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Email\\Api\\PostUsers',
    'adjustedRoute' => '/Email/{id}/users'
  ],
  126 => [
    'route' => '/EmailAddress/search',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\EmailAddress\\Api\\GetSearch',
    'adjustedRoute' => '/EmailAddress/search'
  ],
  127 => [
    'route' => '/User/:id/stream/own',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\GetOwn',
    'adjustedRoute' => '/User/{id}/stream/own'
  ],
  128 => [
    'route' => '/User/:id/acl',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\GetUserAcl',
    'adjustedRoute' => '/User/{id}/acl'
  ],
  129 => [
    'route' => '/UserSecurity/apiKey/generate',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PostApiKeyGenerate',
    'adjustedRoute' => '/UserSecurity/apiKey/generate'
  ],
  130 => [
    'route' => '/UserSecurity/password',
    'method' => 'put',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PutPassword',
    'adjustedRoute' => '/UserSecurity/password'
  ],
  131 => [
    'route' => '/UserSecurity/password/recovery',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PostPasswordRecovery',
    'adjustedRoute' => '/UserSecurity/password/recovery'
  ],
  132 => [
    'route' => '/UserSecurity/password/generate',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PostPasswordGenerate',
    'adjustedRoute' => '/UserSecurity/password/generate'
  ],
  133 => [
    'route' => '/User/passwordChangeRequest',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PostPasswordChangeRequest',
    'noAuth' => true,
    'adjustedRoute' => '/User/passwordChangeRequest'
  ],
  134 => [
    'route' => '/User/changePasswordByRequest',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\UserSecurity\\Api\\PostChangePasswordByRequest',
    'noAuth' => true,
    'adjustedRoute' => '/User/changePasswordByRequest'
  ],
  135 => [
    'route' => '/Team/:id/userPosition',
    'method' => 'put',
    'actionClassName' => 'Espo\\Tools\\User\\Api\\PutTeamUserPosition',
    'adjustedRoute' => '/Team/{id}/userPosition'
  ],
  136 => [
    'route' => '/Oidc/authorizationData',
    'method' => 'get',
    'params' => [
      'controller' => 'Oidc',
      'action' => 'authorizationData'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/Oidc/authorizationData'
  ],
  137 => [
    'route' => '/Oidc/backchannelLogout',
    'method' => 'post',
    'params' => [
      'controller' => 'Oidc',
      'action' => 'backchannelLogout'
    ],
    'noAuth' => true,
    'adjustedRoute' => '/Oidc/backchannelLogout'
  ],
  138 => [
    'method' => 'post',
    'route' => '/OAuth/:id/connection',
    'actionClassName' => 'Espo\\Tools\\OAuth\\Api\\PostConnection',
    'adjustedRoute' => '/OAuth/{id}/connection'
  ],
  139 => [
    'method' => 'delete',
    'route' => '/OAuth/:id/connection',
    'actionClassName' => 'Espo\\Tools\\OAuth\\Api\\DeleteConnection',
    'adjustedRoute' => '/OAuth/{id}/connection'
  ],
  140 => [
    'route' => '/:controller/:id',
    'method' => 'get',
    'params' => [
      'controller' => ':controller',
      'action' => 'read',
      'id' => ':id'
    ],
    'adjustedRoute' => '/{controller}/{id}'
  ],
  141 => [
    'route' => '/:controller',
    'method' => 'get',
    'params' => [
      'controller' => ':controller',
      'action' => 'index'
    ],
    'adjustedRoute' => '/{controller}'
  ],
  142 => [
    'route' => '/:controller',
    'method' => 'post',
    'params' => [
      'controller' => ':controller',
      'action' => 'create'
    ],
    'adjustedRoute' => '/{controller}'
  ],
  143 => [
    'route' => '/:controller/:id',
    'method' => 'put',
    'params' => [
      'controller' => ':controller',
      'action' => 'update',
      'id' => ':id'
    ],
    'adjustedRoute' => '/{controller}/{id}'
  ],
  144 => [
    'route' => '/:controller/:id',
    'method' => 'patch',
    'params' => [
      'controller' => ':controller',
      'action' => 'update',
      'id' => ':id'
    ],
    'adjustedRoute' => '/{controller}/{id}'
  ],
  145 => [
    'route' => '/:controller/:id',
    'method' => 'delete',
    'params' => [
      'controller' => ':controller',
      'action' => 'delete',
      'id' => ':id'
    ],
    'adjustedRoute' => '/{controller}/{id}'
  ],
  146 => [
    'route' => '/:entityType/:id/followers',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\GetFollowers',
    'adjustedRoute' => '/{entityType}/{id}/followers'
  ],
  147 => [
    'route' => '/:entityType/:id/followers',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\PostFollowers',
    'adjustedRoute' => '/{entityType}/{id}/followers'
  ],
  148 => [
    'route' => '/:entityType/:id/followers',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\DeleteFollowers',
    'adjustedRoute' => '/{entityType}/{id}/followers'
  ],
  149 => [
    'route' => '/:controller/:id/stream',
    'method' => 'get',
    'params' => [
      'controller' => 'Stream',
      'action' => 'list',
      'id' => ':id',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/{id}/stream'
  ],
  150 => [
    'route' => '/:controller/:id/posts',
    'method' => 'get',
    'params' => [
      'controller' => 'Stream',
      'action' => 'listPosts',
      'id' => ':id',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/{id}/posts'
  ],
  151 => [
    'route' => '/:controller/:id/updateStream',
    'method' => 'get',
    'params' => [
      'controller' => 'Stream',
      'action' => 'listUpdates',
      'id' => ':id',
      'scope' => ':controller'
    ],
    'adjustedRoute' => '/{controller}/{id}/updateStream'
  ],
  152 => [
    'route' => '/:controller/:id/subscription',
    'method' => 'put',
    'params' => [
      'controller' => ':controller',
      'id' => ':id',
      'action' => 'follow'
    ],
    'adjustedRoute' => '/{controller}/{id}/subscription'
  ],
  153 => [
    'route' => '/:controller/:id/subscription',
    'method' => 'delete',
    'params' => [
      'controller' => ':controller',
      'id' => ':id',
      'action' => 'unfollow'
    ],
    'adjustedRoute' => '/{controller}/{id}/subscription'
  ],
  154 => [
    'route' => '/:entityType/:id/streamAttachments',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\GetStreamAttachments',
    'adjustedRoute' => '/{entityType}/{id}/streamAttachments'
  ],
  155 => [
    'route' => '/:Note/:id/pin',
    'method' => 'post',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\PostNotePin',
    'adjustedRoute' => '/{Note}/{id}/pin'
  ],
  156 => [
    'route' => '/:Note/:id/pin',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Stream\\Api\\DeleteNotePin',
    'adjustedRoute' => '/{Note}/{id}/pin'
  ],
  157 => [
    'route' => '/:entityType/:id/starSubscription',
    'method' => 'put',
    'actionClassName' => 'Espo\\Tools\\Stars\\Api\\PutStar',
    'adjustedRoute' => '/{entityType}/{id}/starSubscription'
  ],
  158 => [
    'route' => '/:entityType/:id/starSubscription',
    'method' => 'delete',
    'actionClassName' => 'Espo\\Tools\\Stars\\Api\\DeleteUnstar',
    'adjustedRoute' => '/{entityType}/{id}/starSubscription'
  ],
  159 => [
    'route' => '/:entityType/:id/usersAccess',
    'method' => 'get',
    'actionClassName' => 'Espo\\Tools\\User\\Api\\PostRecordUsersAccess',
    'adjustedRoute' => '/{entityType}/{id}/usersAccess'
  ],
  160 => [
    'route' => '/:controller/:id/:link',
    'method' => 'get',
    'params' => [
      'controller' => ':controller',
      'action' => 'listLinked',
      'id' => ':id',
      'link' => ':link'
    ],
    'adjustedRoute' => '/{controller}/{id}/{link}'
  ],
  161 => [
    'route' => '/:controller/:id/:link',
    'method' => 'post',
    'params' => [
      'controller' => ':controller',
      'action' => 'createLink',
      'id' => ':id',
      'link' => ':link'
    ],
    'adjustedRoute' => '/{controller}/{id}/{link}'
  ],
  162 => [
    'route' => '/:controller/:id/:link',
    'method' => 'delete',
    'params' => [
      'controller' => ':controller',
      'action' => 'removeLink',
      'id' => ':id',
      'link' => ':link'
    ],
    'adjustedRoute' => '/{controller}/{id}/{link}'
  ]
];
