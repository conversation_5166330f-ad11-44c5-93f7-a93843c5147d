<?php
return [
  'Email' => 'Espo\\Services\\Email',
  'ExternalAccount' => 'Espo\\Services\\ExternalAccount',
  'Import' => 'Espo\\Services\\Import',
  'Integration' => 'Espo\\Services\\Integration',
  'Record' => 'Espo\\Services\\Record',
  'RecordTree' => 'Espo\\Services\\RecordTree',
  'Stream' => 'Espo\\Services\\Stream',
  'User' => 'Espo\\Services\\User',
  'BpmnFlowNode' => 'Espo\\Modules\\Advanced\\Services\\BpmnFlowNode',
  'BpmnFlowchart' => 'Espo\\Modules\\Advanced\\Services\\BpmnFlowchart',
  'BpmnProcess' => 'Espo\\Modules\\Advanced\\Services\\BpmnProcess',
  'Report' => 'Espo\\Modules\\Advanced\\Services\\Report',
  'ReportFilter' => 'Espo\\Modules\\Advanced\\Services\\ReportFilter',
  'ReportPanel' => 'Espo\\Modules\\Advanced\\Services\\ReportPanel',
  'Workflow' => 'Espo\\Modules\\Advanced\\Services\\Workflow',
  'DeliveryOrder' => 'Espo\\Modules\\Sales\\Services\\DeliveryOrder',
  'InventoryAdjustment' => 'Espo\\Modules\\Sales\\Services\\InventoryAdjustment',
  'InvoiceWorkflow' => 'Espo\\Modules\\Sales\\Services\\InvoiceWorkflow',
  'OpportunityWorkflow' => 'Espo\\Modules\\Sales\\Services\\OpportunityWorkflow',
  'ProductCategory' => 'Espo\\Modules\\Sales\\Services\\ProductCategory',
  'PurchaseOrderWorkflow' => 'Espo\\Modules\\Sales\\Services\\PurchaseOrderWorkflow',
  'QuoteWorkflow' => 'Espo\\Modules\\Sales\\Services\\QuoteWorkflow',
  'ReceiptOrder' => 'Espo\\Modules\\Sales\\Services\\ReceiptOrder',
  'SalesOrderWorkflow' => 'Espo\\Modules\\Sales\\Services\\SalesOrderWorkflow',
  'TransferOrder' => 'Espo\\Modules\\Sales\\Services\\TransferOrder'
];
