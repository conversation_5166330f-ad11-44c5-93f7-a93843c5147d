<?php
return (object) [
  'AppLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'message',
        2 => 'level',
        3 => 'code',
        4 => 'exceptionClass',
        5 => 'file',
        6 => 'line',
        7 => 'requestMethod',
        8 => 'requestResourcePath',
        9 => 'requestUrl'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'message',
        2 => 'level',
        3 => 'code',
        4 => 'exceptionClass',
        5 => 'file',
        6 => 'line',
        7 => 'requestMethod',
        8 => 'requestResourcePath',
        9 => 'requestUrl'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'AppSecret' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'value'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'deleteId'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'value'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'deleteId'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Attachment' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'source',
        1 => 'createdAt',
        2 => 'createdBy',
        3 => 'storage'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'sourceId',
        1 => 'sourceName',
        2 => 'createdAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'storage'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'AuthLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'username',
        1 => 'portal',
        2 => 'user',
        3 => 'authToken',
        4 => 'ipAddress',
        5 => 'createdAt',
        6 => 'isDenied',
        7 => 'denialReason',
        8 => 'requestTime',
        9 => 'requestUrl',
        10 => 'requestMethod',
        11 => 'authTokenIsActive'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'username',
        1 => 'portalId',
        2 => 'portalName',
        3 => 'userId',
        4 => 'userName',
        5 => 'authTokenId',
        6 => 'authTokenName',
        7 => 'ipAddress',
        8 => 'createdAt',
        9 => 'isDenied',
        10 => 'denialReason',
        11 => 'requestTime',
        12 => 'requestUrl',
        13 => 'requestMethod',
        14 => 'authTokenIsActive'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'AuthToken' => (object) [
    'fields' => (object) [
      'forbidden' => [
        0 => 'token',
        1 => 'hash',
        2 => 'secret'
      ],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'token',
        1 => 'hash',
        2 => 'secret',
        3 => 'user',
        4 => 'portal',
        5 => 'ipAddress',
        6 => 'lastAccess',
        7 => 'createdAt',
        8 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [
        0 => 'token',
        1 => 'hash',
        2 => 'secret'
      ],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'token',
        1 => 'hash',
        2 => 'secret',
        3 => 'userId',
        4 => 'userName',
        5 => 'portalId',
        6 => 'portalName',
        7 => 'ipAddress',
        8 => 'lastAccess',
        9 => 'createdAt',
        10 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'AuthenticationProvider' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'oidcAuthorizationRedirectUri'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'oidcAuthorizationRedirectUri'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'DashboardTemplate' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Email' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fromName',
        1 => 'fromAddress',
        2 => 'replyToName',
        3 => 'replyToAddress',
        4 => 'addressNameMap',
        5 => 'isRead',
        6 => 'isNotRead',
        7 => 'isReplied',
        8 => 'isNotReplied',
        9 => 'isImportant',
        10 => 'inTrash',
        11 => 'inArchive',
        12 => 'folderId',
        13 => 'isUsers',
        14 => 'isUsersSent',
        15 => 'folder',
        16 => 'folderString',
        17 => 'nameHash',
        18 => 'typeHash',
        19 => 'idHash',
        20 => 'messageId',
        21 => 'messageIdInternal',
        22 => 'fromEmailAddress',
        23 => 'toEmailAddresses',
        24 => 'ccEmailAddresses',
        25 => 'bccEmailAddresses',
        26 => 'replyToEmailAddresses',
        27 => 'bodyPlain',
        28 => 'hasAttachment',
        29 => 'deliveryDate',
        30 => 'createdAt',
        31 => 'modifiedAt',
        32 => 'createdBy',
        33 => 'sentBy',
        34 => 'modifiedBy',
        35 => 'replies',
        36 => 'isSystem',
        37 => 'isJustSent',
        38 => 'isBeingImported',
        39 => 'skipNotificationMap',
        40 => 'users',
        41 => 'assignedUsers',
        42 => 'inboundEmails',
        43 => 'emailAccounts',
        44 => 'icsContents',
        45 => 'icsEventData',
        46 => 'icsEventUid',
        47 => 'icsEventDateStart',
        48 => 'createdEvent',
        49 => 'groupFolder',
        50 => 'groupStatusFolder',
        51 => 'account',
        52 => 'tasks',
        53 => 'icsEventDateStartDate'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fromName',
        1 => 'fromAddress',
        2 => 'replyToName',
        3 => 'replyToAddress',
        4 => 'addressNameMap',
        5 => 'isRead',
        6 => 'isNotRead',
        7 => 'isReplied',
        8 => 'isNotReplied',
        9 => 'isImportant',
        10 => 'inTrash',
        11 => 'inArchive',
        12 => 'folderId',
        13 => 'isUsers',
        14 => 'isUsersSent',
        15 => 'folderId',
        16 => 'folderName',
        17 => 'folderStringId',
        18 => 'folderStringName',
        19 => 'nameHash',
        20 => 'typeHash',
        21 => 'idHash',
        22 => 'messageId',
        23 => 'messageIdInternal',
        24 => 'fromEmailAddressId',
        25 => 'fromEmailAddressName',
        26 => 'toEmailAddressesIds',
        27 => 'toEmailAddressesNames',
        28 => 'ccEmailAddressesIds',
        29 => 'ccEmailAddressesNames',
        30 => 'bccEmailAddressesIds',
        31 => 'bccEmailAddressesNames',
        32 => 'replyToEmailAddressesIds',
        33 => 'replyToEmailAddressesNames',
        34 => 'bodyPlain',
        35 => 'hasAttachment',
        36 => 'deliveryDate',
        37 => 'createdAt',
        38 => 'modifiedAt',
        39 => 'createdById',
        40 => 'createdByName',
        41 => 'sentById',
        42 => 'sentByName',
        43 => 'modifiedById',
        44 => 'modifiedByName',
        45 => 'repliesIds',
        46 => 'repliesNames',
        47 => 'isSystem',
        48 => 'isJustSent',
        49 => 'isBeingImported',
        50 => 'skipNotificationMap',
        51 => 'usersIds',
        52 => 'usersNames',
        53 => 'assignedUsersIds',
        54 => 'assignedUsersNames',
        55 => 'inboundEmailsIds',
        56 => 'inboundEmailsNames',
        57 => 'emailAccountsIds',
        58 => 'emailAccountsNames',
        59 => 'icsContents',
        60 => 'icsEventData',
        61 => 'icsEventUid',
        62 => 'icsEventDateStart',
        63 => 'icsEventDateStartDate',
        64 => 'createdEventId',
        65 => 'createdEventType',
        66 => 'createdEventName',
        67 => 'groupFolderId',
        68 => 'groupFolderName',
        69 => 'groupStatusFolder',
        70 => 'accountId',
        71 => 'accountName',
        72 => 'tasksIds',
        73 => 'tasksNames',
        74 => 'icsEventDateStartDate'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'tasks'
      ],
      'nonAdminReadOnly' => [
        0 => 'users'
      ]
    ]
  ],
  'EmailAccount' => (object) [
    'fields' => (object) [
      'forbidden' => [
        0 => 'imapHandler',
        1 => 'smtpHandler'
      ],
      'internal' => [
        0 => 'password',
        1 => 'smtpPassword'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fetchData',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'connectedAt',
        4 => 'imapHandler',
        5 => 'smtpHandler',
        6 => 'createdBy',
        7 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [
        0 => 'imapHandler',
        1 => 'smtpHandler'
      ],
      'internal' => [
        0 => 'password',
        1 => 'smtpPassword'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fetchData',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'connectedAt',
        4 => 'imapHandler',
        5 => 'smtpHandler',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'EmailFilter' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'EmailFolder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'assignedUser',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'assignedUserId',
        1 => 'assignedUserName',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'EmailTemplate' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'EmailTemplateCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'Export' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Extension' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdById',
        2 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'GroupEmailFolder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Import' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'entityType',
        1 => 'status',
        2 => 'file',
        3 => 'importedCount',
        4 => 'duplicateCount',
        5 => 'updatedCount',
        6 => 'lastIndex',
        7 => 'params',
        8 => 'attributeList',
        9 => 'createdAt',
        10 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'entityType',
        1 => 'status',
        2 => 'fileId',
        3 => 'fileName',
        4 => 'importedCount',
        5 => 'duplicateCount',
        6 => 'updatedCount',
        7 => 'lastIndex',
        8 => 'params',
        9 => 'attributeList',
        10 => 'createdAt',
        11 => 'createdById',
        12 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'errors'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ImportError' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'import',
        1 => 'rowIndex',
        2 => 'exportRowIndex',
        3 => 'lineNumber',
        4 => 'exportLineNumber',
        5 => 'type',
        6 => 'validationFailures',
        7 => 'row'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'importId',
        1 => 'importName',
        2 => 'rowIndex',
        3 => 'exportRowIndex',
        4 => 'lineNumber',
        5 => 'exportLineNumber',
        6 => 'type',
        7 => 'validationFailures',
        8 => 'row'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'InboundEmail' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'password',
        1 => 'smtpPassword',
        2 => 'imapHandler',
        3 => 'smtpHandler'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fetchData',
        1 => 'isSystem',
        2 => 'connectedAt',
        3 => 'imapHandler',
        4 => 'smtpHandler',
        5 => 'createdAt',
        6 => 'modifiedAt',
        7 => 'createdBy',
        8 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'password',
        1 => 'smtpPassword',
        2 => 'imapHandler',
        3 => 'smtpHandler'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'fetchData',
        1 => 'isSystem',
        2 => 'connectedAt',
        3 => 'imapHandler',
        4 => 'smtpHandler',
        5 => 'createdAt',
        6 => 'modifiedAt',
        7 => 'createdById',
        8 => 'createdByName',
        9 => 'modifiedById',
        10 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Job' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'createdAt',
        2 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'createdAt',
        2 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'LayoutSet' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'LeadCapture' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'apiKey',
        1 => 'formId',
        2 => 'exampleRequestUrl',
        3 => 'exampleRequestMethod',
        4 => 'exampleRequestPayload',
        5 => 'exampleRequestHeaders',
        6 => 'formUrl',
        7 => 'createdAt',
        8 => 'modifiedAt',
        9 => 'createdBy',
        10 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'apiKey',
        1 => 'formId',
        2 => 'exampleRequestUrl',
        3 => 'exampleRequestMethod',
        4 => 'exampleRequestPayload',
        5 => 'exampleRequestHeaders',
        6 => 'formUrl',
        7 => 'createdAt',
        8 => 'modifiedAt',
        9 => 'createdById',
        10 => 'createdByName',
        11 => 'modifiedById',
        12 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'LeadCaptureLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'MassAction' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Note' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'data',
        1 => 'type',
        2 => 'related',
        3 => 'number',
        4 => 'notifiedUserIdList',
        5 => 'isPinned',
        6 => 'reactionCounts',
        7 => 'myReactions',
        8 => 'createdAt',
        9 => 'modifiedAt',
        10 => 'createdBy',
        11 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'data',
        1 => 'type',
        2 => 'relatedId',
        3 => 'relatedType',
        4 => 'relatedName',
        5 => 'number',
        6 => 'notifiedUserIdList',
        7 => 'isPinned',
        8 => 'reactionCounts',
        9 => 'myReactions',
        10 => 'createdAt',
        11 => 'modifiedAt',
        12 => 'createdById',
        13 => 'createdByName',
        14 => 'modifiedById',
        15 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'teams',
        1 => 'users'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'Notification' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'related',
        2 => 'relatedParent',
        3 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'relatedId',
        2 => 'relatedType',
        3 => 'relatedName',
        4 => 'relatedParentId',
        5 => 'relatedParentType',
        6 => 'relatedParentName',
        7 => 'createdById',
        8 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'OAuthAccount' => (object) [
    'fields' => (object) [
      'forbidden' => [
        0 => 'accessToken',
        1 => 'refreshToken',
        2 => 'expiresAt'
      ],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'user',
        1 => 'hasAccessToken',
        2 => 'data',
        3 => 'accessToken',
        4 => 'refreshToken',
        5 => 'expiresAt',
        6 => 'createdAt',
        7 => 'modifiedAt',
        8 => 'createdBy',
        9 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [
        0 => 'accessToken',
        1 => 'refreshToken',
        2 => 'expiresAt'
      ],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'userId',
        1 => 'userName',
        2 => 'hasAccessToken',
        3 => 'data',
        4 => 'accessToken',
        5 => 'refreshToken',
        6 => 'expiresAt',
        7 => 'createdAt',
        8 => 'modifiedAt',
        9 => 'createdById',
        10 => 'createdByName',
        11 => 'modifiedById',
        12 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'OAuthProvider' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'clientSecret'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'authorizationRedirectUri',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'clientSecret'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'authorizationRedirectUri',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'accounts'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'PasswordChangeRequest' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'user',
        1 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'userId',
        1 => 'userName',
        2 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Portal' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'url',
        1 => 'modifiedAt',
        2 => 'modifiedBy',
        3 => 'createdAt',
        4 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'url',
        1 => 'modifiedAt',
        2 => 'modifiedById',
        3 => 'modifiedByName',
        4 => 'createdAt',
        5 => 'createdById',
        6 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'PortalRole' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Role' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ScheduledJob' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'lastRun',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'isInternal'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'lastRun',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'isInternal'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ScheduledJobLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'status',
        2 => 'executionTime',
        3 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'status',
        2 => 'executionTime',
        3 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Settings' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'addressPreview',
        1 => 'addressPreviewStreet',
        2 => 'addressPreviewCity',
        3 => 'addressPreviewState',
        4 => 'addressPreviewCountry',
        5 => 'addressPreviewPostalCode',
        6 => 'addressPreviewMap',
        7 => 'sellerAddressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'addressPreviewStreet',
        1 => 'addressPreviewCity',
        2 => 'addressPreviewState',
        3 => 'addressPreviewCountry',
        4 => 'addressPreviewPostalCode',
        5 => 'addressPreviewStreet',
        6 => 'addressPreviewCity',
        7 => 'addressPreviewState',
        8 => 'addressPreviewCountry',
        9 => 'addressPreviewPostalCode',
        10 => 'addressPreviewMap',
        11 => 'sellerAddressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Sms' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'replied',
        5 => 'replies'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'repliedId',
        7 => 'repliedName',
        8 => 'repliesIds',
        9 => 'repliesNames'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Team' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Template' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'TwoFactorCode' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'UniqueId' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdById',
        2 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'User' => (object) [
    'fields' => (object) [
      'forbidden' => [
        0 => 'authLogRecordId',
        1 => 'userData',
        2 => 'deleteId'
      ],
      'internal' => [
        0 => 'password',
        1 => 'passwordConfirm'
      ],
      'onlyAdmin' => [
        0 => 'authMethod',
        1 => 'apiKey',
        2 => 'secretKey',
        3 => 'layoutSet',
        4 => 'auth2FA'
      ],
      'readOnly' => [
        0 => 'apiKey',
        1 => 'secretKey',
        2 => 'position',
        3 => 'account',
        4 => 'portal',
        5 => 'createdAt',
        6 => 'modifiedAt',
        7 => 'createdBy',
        8 => 'auth2FA',
        9 => 'lastAccess',
        10 => 'emailAddressList',
        11 => 'userEmailAddressList',
        12 => 'excludeFromReplyEmailAddressList',
        13 => 'recordAccessLevels',
        14 => 'targetListIsOptedOut',
        15 => 'cVicidialPassword',
        16 => 'deleteId'
      ],
      'nonAdminReadOnly' => [
        0 => 'userName',
        1 => 'type',
        2 => 'password',
        3 => 'passwordConfirm',
        4 => 'apiKey',
        5 => 'isActive',
        6 => 'emailAddress',
        7 => 'defaultTeam',
        8 => 'teams',
        9 => 'roles',
        10 => 'portals',
        11 => 'portalRoles',
        12 => 'contact',
        13 => 'accounts',
        14 => 'workingTimeCalendar'
      ]
    ],
    'attributes' => (object) [
      'forbidden' => [
        0 => 'authLogRecordId',
        1 => 'userDataId',
        2 => 'userDataName',
        3 => 'deleteId'
      ],
      'internal' => [
        0 => 'password',
        1 => 'passwordConfirm'
      ],
      'onlyAdmin' => [
        0 => 'authMethod',
        1 => 'apiKey',
        2 => 'secretKey',
        3 => 'layoutSetId',
        4 => 'layoutSetName',
        5 => 'auth2FA'
      ],
      'readOnly' => [
        0 => 'apiKey',
        1 => 'secretKey',
        2 => 'position',
        3 => 'accountId',
        4 => 'accountName',
        5 => 'portalId',
        6 => 'portalName',
        7 => 'createdAt',
        8 => 'modifiedAt',
        9 => 'createdById',
        10 => 'createdByName',
        11 => 'auth2FA',
        12 => 'lastAccess',
        13 => 'emailAddressList',
        14 => 'userEmailAddressList',
        15 => 'excludeFromReplyEmailAddressList',
        16 => 'recordAccessLevels',
        17 => 'targetListIsOptedOut',
        18 => 'cVicidialPassword',
        19 => 'deleteId'
      ],
      'nonAdminReadOnly' => [
        0 => 'userName',
        1 => 'type',
        2 => 'password',
        3 => 'passwordConfirm',
        4 => 'apiKey',
        5 => 'isActive',
        6 => 'emailAddressIsOptedOut',
        7 => 'emailAddressIsInvalid',
        8 => 'emailAddress',
        9 => 'emailAddressData',
        10 => 'defaultTeamId',
        11 => 'defaultTeamName',
        12 => 'teamsIds',
        13 => 'teamsColumns',
        14 => 'teamsNames',
        15 => 'rolesIds',
        16 => 'rolesNames',
        17 => 'portalsIds',
        18 => 'portalsNames',
        19 => 'portalRolesIds',
        20 => 'portalRolesNames',
        21 => 'contactId',
        22 => 'contactName',
        23 => 'accountsIds',
        24 => 'accountsNames',
        25 => 'workingTimeCalendarId',
        26 => 'workingTimeCalendarName'
      ]
    ],
    'links' => (object) [
      'forbidden' => [
        0 => 'userData'
      ],
      'internal' => [],
      'onlyAdmin' => [
        0 => 'defaultTeam',
        1 => 'roles',
        2 => 'portalRoles',
        3 => 'dashboardTemplate',
        4 => 'accounts'
      ],
      'readOnly' => [],
      'nonAdminReadOnly' => [
        0 => 'teams',
        1 => 'workingTimeRanges'
      ]
    ]
  ],
  'Webhook' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [
        0 => 'user'
      ],
      'readOnly' => [
        0 => 'entityType',
        1 => 'type',
        2 => 'field',
        3 => 'secretKey',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdBy',
        7 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [
        0 => 'userId',
        1 => 'userName'
      ],
      'readOnly' => [
        0 => 'entityType',
        1 => 'type',
        2 => 'field',
        3 => 'secretKey',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'queueItems'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'WebhookEventQueueItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'WorkingTimeCalendar' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'teams',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'teamsIds',
        1 => 'teamsNames',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'teams'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'WorkingTimeRange' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Account' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'originalLead',
        5 => 'targetListIsOptedOut',
        6 => 'billingAddressMap',
        7 => 'shippingAddressMap',
        8 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'originalLeadId',
        7 => 'originalLeadName',
        8 => 'targetListIsOptedOut',
        9 => 'billingAddressMap',
        10 => 'shippingAddressMap',
        11 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Call' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'account',
        1 => 'uid',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdBy',
        5 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'accountId',
        1 => 'accountName',
        2 => 'uid',
        3 => 'createdAt',
        4 => 'modifiedAt',
        5 => 'createdById',
        6 => 'createdByName',
        7 => 'modifiedById',
        8 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Campaign' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'sentCount',
        5 => 'openedCount',
        6 => 'clickedCount',
        7 => 'optedInCount',
        8 => 'optedOutCount',
        9 => 'bouncedCount',
        10 => 'hardBouncedCount',
        11 => 'softBouncedCount',
        12 => 'leadCreatedCount',
        13 => 'openedPercentage',
        14 => 'clickedPercentage',
        15 => 'optedOutPercentage',
        16 => 'bouncedPercentage',
        17 => 'revenue',
        18 => 'revenueCurrency',
        19 => 'revenueConverted',
        20 => 'budgetConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'sentCount',
        7 => 'openedCount',
        8 => 'clickedCount',
        9 => 'optedInCount',
        10 => 'optedOutCount',
        11 => 'bouncedCount',
        12 => 'hardBouncedCount',
        13 => 'softBouncedCount',
        14 => 'leadCreatedCount',
        15 => 'openedPercentage',
        16 => 'clickedPercentage',
        17 => 'optedOutPercentage',
        18 => 'bouncedPercentage',
        19 => 'revenueCurrency',
        20 => 'revenue',
        21 => 'revenueCurrency',
        22 => 'revenueConverted',
        23 => 'budgetConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'CampaignLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdById',
        2 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'CampaignTrackingUrl' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'urlToUse',
        1 => 'modifiedAt',
        2 => 'modifiedBy',
        3 => 'createdAt',
        4 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'urlToUse',
        1 => 'modifiedAt',
        2 => 'modifiedById',
        3 => 'modifiedByName',
        4 => 'createdAt',
        5 => 'createdById',
        6 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Case' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'inboundEmail',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'inboundEmailId',
        1 => 'inboundEmailName',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName',
        8 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'inboundEmail'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'Contact' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'accountType',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'portalUser',
        6 => 'hasPortalUser',
        7 => 'originalLead',
        8 => 'targetListIsOptedOut',
        9 => 'cUniquePhoneNumber',
        10 => 'addressMap',
        11 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'accountType',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'portalUserId',
        8 => 'portalUserName',
        9 => 'hasPortalUser',
        10 => 'originalLeadId',
        11 => 'originalLeadName',
        12 => 'targetListIsOptedOut',
        13 => 'cUniquePhoneNumber',
        14 => 'addressMap',
        15 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Document' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'DocumentFolder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'EmailQueueItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'massEmail',
        1 => 'status',
        2 => 'attemptCount',
        3 => 'target',
        4 => 'createdAt',
        5 => 'sentAt',
        6 => 'emailAddress'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'massEmailId',
        1 => 'massEmailName',
        2 => 'status',
        3 => 'attemptCount',
        4 => 'targetId',
        5 => 'targetType',
        6 => 'targetName',
        7 => 'createdAt',
        8 => 'sentAt',
        9 => 'emailAddress'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'KnowledgeBaseArticle' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'bodyPlain'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'bodyPlain'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'KnowledgeBaseCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Lead' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'opportunityAmountConverted',
        1 => 'convertedAt',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdBy',
        5 => 'modifiedBy',
        6 => 'targetListIsOptedOut',
        7 => 'addressMap',
        8 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'opportunityAmountConverted',
        1 => 'convertedAt',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName',
        8 => 'targetListIsOptedOut',
        9 => 'addressMap',
        10 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'MassEmail' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Meeting' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'account',
        1 => 'uid',
        2 => 'joinUrl',
        3 => 'createdAt',
        4 => 'modifiedAt',
        5 => 'createdBy',
        6 => 'modifiedBy',
        7 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'accountId',
        1 => 'accountName',
        2 => 'uid',
        3 => 'joinUrl',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName',
        10 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Opportunity' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'amountConverted',
        1 => 'amountWeightedConverted',
        2 => 'originalLead',
        3 => 'createdAt',
        4 => 'modifiedAt',
        5 => 'createdBy',
        6 => 'modifiedBy',
        7 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'amountConverted',
        1 => 'amountWeightedConverted',
        2 => 'originalLeadId',
        3 => 'originalLeadName',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName',
        10 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Target' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'addressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'addressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'TargetList' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'entryCount',
        1 => 'optedOutCount',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdBy',
        5 => 'modifiedBy',
        6 => 'targetStatus',
        7 => 'isOptedOut'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'entryCount',
        1 => 'optedOutCount',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName',
        8 => 'targetStatus',
        9 => 'isOptedOut'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'TargetListCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'Task' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'dateCompleted',
        1 => 'isOverdue',
        2 => 'account',
        3 => 'contact',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdBy',
        7 => 'modifiedBy',
        8 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'dateCompleted',
        1 => 'isOverdue',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'contactId',
        5 => 'contactName',
        6 => 'createdAt',
        7 => 'modifiedAt',
        8 => 'createdById',
        9 => 'createdByName',
        10 => 'modifiedById',
        11 => 'modifiedByName',
        12 => 'streamUpdatedAt'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'BpmnFlowchart' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'elementsDataHash',
        1 => 'hasNoneStartEvent',
        2 => 'eventStartIdList',
        3 => 'eventStartAllIdList',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdBy',
        7 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'elementsDataHash',
        1 => 'hasNoneStartEvent',
        2 => 'eventStartIdList',
        3 => 'eventStartAllIdList',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'BpmnProcess' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'status',
        1 => 'flowchartData',
        2 => 'flowchartElementsDataHash',
        3 => 'flowchartVisualization',
        4 => 'parentProcess',
        5 => 'parentProcessFlowNode',
        6 => 'rootProcess',
        7 => 'createdEntitiesData',
        8 => 'variables',
        9 => 'workflowId',
        10 => 'createdAt',
        11 => 'modifiedAt',
        12 => 'endedAt',
        13 => 'createdBy',
        14 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'status',
        1 => 'flowchartData',
        2 => 'flowchartElementsDataHash',
        3 => 'flowchartVisualization',
        4 => 'parentProcessId',
        5 => 'parentProcessName',
        6 => 'parentProcessFlowNodeId',
        7 => 'parentProcessFlowNodeName',
        8 => 'rootProcessId',
        9 => 'rootProcessName',
        10 => 'createdEntitiesData',
        11 => 'variables',
        12 => 'workflowId',
        13 => 'createdAt',
        14 => 'modifiedAt',
        15 => 'endedAt',
        16 => 'createdById',
        17 => 'createdByName',
        18 => 'modifiedById',
        19 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'parentProcess',
        1 => 'childProcesses'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'BpmnUserTask' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'actionType',
        2 => 'target',
        3 => 'process',
        4 => 'isResolved',
        5 => 'instructions',
        6 => 'flowNode',
        7 => 'isCanceled',
        8 => 'createdAt',
        9 => 'modifiedAt',
        10 => 'createdBy',
        11 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'actionType',
        2 => 'targetId',
        3 => 'targetType',
        4 => 'targetName',
        5 => 'processId',
        6 => 'processName',
        7 => 'isResolved',
        8 => 'instructions',
        9 => 'flowNodeId',
        10 => 'flowNodeName',
        11 => 'isCanceled',
        12 => 'createdAt',
        13 => 'modifiedAt',
        14 => 'createdById',
        15 => 'createdByName',
        16 => 'modifiedById',
        17 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Report' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'internalClassName'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'internalClassName',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'emailSendingLastDateSent'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'internalClassName'
      ],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'internalClassName',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'emailSendingLastDateSent'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [
        0 => 'portals'
      ],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReportCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReportFilter' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReportPanel' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'reportEntityType',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'reportType'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'reportEntityType',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'reportType'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Workflow' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'isInternal',
        1 => 'lastRun',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdBy',
        5 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'isInternal',
        1 => 'lastRun',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdById',
        5 => 'createdByName',
        6 => 'modifiedById',
        7 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'WorkflowCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'WorkflowLogRecord' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'createdById',
        2 => 'createdByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'DeliveryOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amount',
        2 => 'amountConverted',
        3 => 'amountCurrency',
        4 => 'weight',
        5 => 'inventoryData',
        6 => 'inventoryStatus',
        7 => 'isDone',
        8 => 'isNotActual',
        9 => 'isLocked',
        10 => 'isHardLocked',
        11 => 'createdAt',
        12 => 'modifiedAt',
        13 => 'createdBy',
        14 => 'modifiedBy',
        15 => 'shippingAddressMap',
        16 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amountCurrency',
        2 => 'amount',
        3 => 'amountConverted',
        4 => 'amountCurrency',
        5 => 'weight',
        6 => 'inventoryData',
        7 => 'inventoryStatus',
        8 => 'isDone',
        9 => 'isNotActual',
        10 => 'isLocked',
        11 => 'isHardLocked',
        12 => 'createdAt',
        13 => 'modifiedAt',
        14 => 'createdById',
        15 => 'createdByName',
        16 => 'modifiedById',
        17 => 'modifiedByName',
        18 => 'shippingAddressMap',
        19 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items',
        1 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'DeliveryOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'deliveryOrder',
        1 => 'account',
        2 => 'deliveryOrderStatus',
        3 => 'product',
        4 => 'quantity',
        5 => 'quantityInt',
        6 => 'inventoryNumber',
        7 => 'inventoryNumberType',
        8 => 'isInventory',
        9 => 'allowFractionalQuantity',
        10 => 'productType',
        11 => 'weight',
        12 => 'order',
        13 => 'createdAt',
        14 => 'modifiedAt',
        15 => 'createdBy',
        16 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'deliveryOrderId',
        1 => 'deliveryOrderName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'deliveryOrderStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'quantity',
        8 => 'quantityInt',
        9 => 'inventoryNumberId',
        10 => 'inventoryNumberName',
        11 => 'inventoryNumberType',
        12 => 'isInventory',
        13 => 'allowFractionalQuantity',
        14 => 'productType',
        15 => 'weight',
        16 => 'order',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdById',
        20 => 'createdByName',
        21 => 'modifiedById',
        22 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'InventoryAdjustment' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amount',
        2 => 'amountConverted',
        3 => 'amountCurrency',
        4 => 'isLocked',
        5 => 'isDone',
        6 => 'isNotActual',
        7 => 'doneAt',
        8 => 'createdAt',
        9 => 'modifiedAt',
        10 => 'createdBy',
        11 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amountCurrency',
        2 => 'amount',
        3 => 'amountConverted',
        4 => 'amountCurrency',
        5 => 'isLocked',
        6 => 'isDone',
        7 => 'isNotActual',
        8 => 'doneAt',
        9 => 'createdAt',
        10 => 'modifiedAt',
        11 => 'createdById',
        12 => 'createdByName',
        13 => 'modifiedById',
        14 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items',
        1 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'InventoryAdjustmentItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'inventoryAdjustment',
        2 => 'inventoryAdjustmentStatus',
        3 => 'warehouse',
        4 => 'product',
        5 => 'allowFractionalQuantity',
        6 => 'productType',
        7 => 'quantityOnHand',
        8 => 'newQuantityOnHand',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'inventoryNumber',
        12 => 'inventoryNumberType',
        13 => 'isInventory',
        14 => 'order',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdBy',
        18 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'inventoryAdjustmentId',
        2 => 'inventoryAdjustmentName',
        3 => 'inventoryAdjustmentStatus',
        4 => 'warehouseId',
        5 => 'warehouseName',
        6 => 'productId',
        7 => 'productName',
        8 => 'allowFractionalQuantity',
        9 => 'productType',
        10 => 'quantityOnHand',
        11 => 'newQuantityOnHand',
        12 => 'quantity',
        13 => 'quantityInt',
        14 => 'inventoryNumberId',
        15 => 'inventoryNumberName',
        16 => 'inventoryNumberType',
        17 => 'isInventory',
        18 => 'order',
        19 => 'createdAt',
        20 => 'modifiedAt',
        21 => 'createdById',
        22 => 'createdByName',
        23 => 'modifiedById',
        24 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'InventoryNumber' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'quantityOnHand',
        1 => 'quantityReserved',
        2 => 'quantityInTransit',
        3 => 'quantityWarehouseOnHand',
        4 => 'quantityWarehouseReserved',
        5 => 'deleteId',
        6 => 'createdAt',
        7 => 'modifiedAt',
        8 => 'createdBy',
        9 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'quantityOnHand',
        1 => 'quantityReserved',
        2 => 'quantityInTransit',
        3 => 'quantityWarehouseOnHand',
        4 => 'quantityWarehouseReserved',
        5 => 'deleteId',
        6 => 'createdAt',
        7 => 'modifiedAt',
        8 => 'createdById',
        9 => 'createdByName',
        10 => 'modifiedById',
        11 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'InventoryTransaction' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'parent',
        2 => 'createdAt',
        3 => 'modifiedAt',
        4 => 'createdBy',
        5 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'parentId',
        2 => 'parentType',
        3 => 'parentName',
        4 => 'createdAt',
        5 => 'modifiedAt',
        6 => 'createdById',
        7 => 'createdByName',
        8 => 'modifiedById',
        9 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Invoice' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmount',
        2 => 'discountAmount',
        3 => 'preDiscountedAmount',
        4 => 'grandTotalAmount',
        5 => 'weight',
        6 => 'isDone',
        7 => 'isNotActual',
        8 => 'isLocked',
        9 => 'createdAt',
        10 => 'modifiedAt',
        11 => 'createdBy',
        12 => 'modifiedBy',
        13 => 'billingAddressMap',
        14 => 'shippingAddressMap',
        15 => 'shippingCostConverted',
        16 => 'taxAmountCurrency',
        17 => 'taxAmountConverted',
        18 => 'discountAmountCurrency',
        19 => 'discountAmountConverted',
        20 => 'amountConverted',
        21 => 'preDiscountedAmountCurrency',
        22 => 'preDiscountedAmountConverted',
        23 => 'grandTotalAmountCurrency',
        24 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmountCurrency',
        2 => 'taxAmount',
        3 => 'discountAmountCurrency',
        4 => 'discountAmount',
        5 => 'preDiscountedAmountCurrency',
        6 => 'preDiscountedAmount',
        7 => 'grandTotalAmountCurrency',
        8 => 'grandTotalAmount',
        9 => 'weight',
        10 => 'isDone',
        11 => 'isNotActual',
        12 => 'isLocked',
        13 => 'createdAt',
        14 => 'modifiedAt',
        15 => 'createdById',
        16 => 'createdByName',
        17 => 'modifiedById',
        18 => 'modifiedByName',
        19 => 'billingAddressMap',
        20 => 'shippingAddressMap',
        21 => 'shippingCostConverted',
        22 => 'taxAmountCurrency',
        23 => 'taxAmountConverted',
        24 => 'discountAmountCurrency',
        25 => 'discountAmountConverted',
        26 => 'amountConverted',
        27 => 'preDiscountedAmountCurrency',
        28 => 'preDiscountedAmountConverted',
        29 => 'grandTotalAmountCurrency',
        30 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'InvoiceItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'invoice',
        1 => 'account',
        2 => 'invoiceStatus',
        3 => 'product',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'listPrice',
        9 => 'unitPrice',
        10 => 'discount',
        11 => 'amount',
        12 => 'weight',
        13 => 'taxRate',
        14 => 'order',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdBy',
        18 => 'modifiedBy',
        19 => 'brutePrice',
        20 => 'listPriceCurrency',
        21 => 'listPriceConverted',
        22 => 'unitPriceCurrency',
        23 => 'unitPriceConverted',
        24 => 'amountCurrency',
        25 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'invoiceId',
        1 => 'invoiceName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'invoiceStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'allowFractionalQuantity',
        8 => 'productType',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'listPriceCurrency',
        12 => 'listPrice',
        13 => 'unitPriceCurrency',
        14 => 'unitPrice',
        15 => 'discount',
        16 => 'amountCurrency',
        17 => 'amount',
        18 => 'weight',
        19 => 'taxRate',
        20 => 'order',
        21 => 'createdAt',
        22 => 'modifiedAt',
        23 => 'createdById',
        24 => 'createdByName',
        25 => 'modifiedById',
        26 => 'modifiedByName',
        27 => 'brutePrice',
        28 => 'listPriceCurrency',
        29 => 'listPriceConverted',
        30 => 'unitPriceCurrency',
        31 => 'unitPriceConverted',
        32 => 'amountCurrency',
        33 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'OpportunityItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'opportunity',
        1 => 'product',
        2 => 'allowFractionalQuantity',
        3 => 'productType',
        4 => 'quantity',
        5 => 'quantityInt',
        6 => 'listPrice',
        7 => 'unitPrice',
        8 => 'discount',
        9 => 'amount',
        10 => 'order',
        11 => 'opportunityStage',
        12 => 'createdAt',
        13 => 'modifiedAt',
        14 => 'createdBy',
        15 => 'modifiedBy',
        16 => 'listPriceCurrency',
        17 => 'listPriceConverted',
        18 => 'unitPriceCurrency',
        19 => 'unitPriceConverted',
        20 => 'amountCurrency',
        21 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'opportunityId',
        1 => 'opportunityName',
        2 => 'productId',
        3 => 'productName',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'listPriceCurrency',
        9 => 'listPrice',
        10 => 'unitPriceCurrency',
        11 => 'unitPrice',
        12 => 'discount',
        13 => 'amountCurrency',
        14 => 'amount',
        15 => 'order',
        16 => 'opportunityStage',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdById',
        20 => 'createdByName',
        21 => 'modifiedById',
        22 => 'modifiedByName',
        23 => 'listPriceCurrency',
        24 => 'listPriceConverted',
        25 => 'unitPriceCurrency',
        26 => 'unitPriceConverted',
        27 => 'amountCurrency',
        28 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'PriceBook' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'PriceRule' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'PriceRuleCondition' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Product' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'unitPriceSelectConverted',
        1 => 'quantity',
        2 => 'quantityReserved',
        3 => 'quantitySoftReserved',
        4 => 'quantityOnHand',
        5 => 'quantityInTransit',
        6 => 'quantityOnOrder',
        7 => 'quantityWarehouse',
        8 => 'quantityWarehouseOnHand',
        9 => 'quantityWarehouseReserved',
        10 => 'createdAt',
        11 => 'modifiedAt',
        12 => 'createdBy',
        13 => 'modifiedBy',
        14 => 'template',
        15 => 'variantAttributeOptions',
        16 => 'variantOrder',
        17 => 'cTaxValue',
        18 => 'totalPrice',
        19 => 'costPriceConverted',
        20 => 'listPriceConverted',
        21 => 'unitPriceConverted',
        22 => 'totalPriceCurrency',
        23 => 'totalPriceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'unitPriceSelectConverted',
        1 => 'quantity',
        2 => 'quantityReserved',
        3 => 'quantitySoftReserved',
        4 => 'quantityOnHand',
        5 => 'quantityInTransit',
        6 => 'quantityOnOrder',
        7 => 'quantityWarehouse',
        8 => 'quantityWarehouseOnHand',
        9 => 'quantityWarehouseReserved',
        10 => 'createdAt',
        11 => 'modifiedAt',
        12 => 'createdById',
        13 => 'createdByName',
        14 => 'modifiedById',
        15 => 'modifiedByName',
        16 => 'templateId',
        17 => 'templateName',
        18 => 'variantAttributeOptionsIds',
        19 => 'variantAttributeOptionsNames',
        20 => 'variantOrder',
        21 => 'cTaxValue',
        22 => 'totalPriceCurrency',
        23 => 'totalPrice',
        24 => 'costPriceConverted',
        25 => 'listPriceConverted',
        26 => 'unitPriceConverted',
        27 => 'totalPriceCurrency',
        28 => 'totalPriceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'prices',
        1 => 'supplierPrices',
        2 => 'inventoryTransactions',
        3 => 'attributes',
        4 => 'attributeOptions',
        5 => 'variantAttributeOptions',
        6 => 'variants',
        7 => 'inventoryAdjustmentItems'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ProductAttribute' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'products'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ProductAttributeOption' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'order'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'products',
        1 => 'productVariants'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ProductBrand' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ProductCategory' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'children'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ProductPrice' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'priceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'priceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'PurchaseOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'account',
        2 => 'taxAmount',
        3 => 'discountAmount',
        4 => 'preDiscountedAmount',
        5 => 'grandTotalAmount',
        6 => 'weight',
        7 => 'isReceiptFullyCreated',
        8 => 'hasInventoryItems',
        9 => 'isDone',
        10 => 'isLocked',
        11 => 'isNotActual',
        12 => 'createdAt',
        13 => 'modifiedAt',
        14 => 'createdBy',
        15 => 'modifiedBy',
        16 => 'supplierAddressMap',
        17 => 'billingAddressMap',
        18 => 'shippingAddressMap',
        19 => 'shippingCostConverted',
        20 => 'taxAmountCurrency',
        21 => 'taxAmountConverted',
        22 => 'discountAmountCurrency',
        23 => 'discountAmountConverted',
        24 => 'amountConverted',
        25 => 'preDiscountedAmountCurrency',
        26 => 'preDiscountedAmountConverted',
        27 => 'grandTotalAmountCurrency',
        28 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'accountId',
        2 => 'accountName',
        3 => 'taxAmountCurrency',
        4 => 'taxAmount',
        5 => 'discountAmountCurrency',
        6 => 'discountAmount',
        7 => 'preDiscountedAmountCurrency',
        8 => 'preDiscountedAmount',
        9 => 'grandTotalAmountCurrency',
        10 => 'grandTotalAmount',
        11 => 'weight',
        12 => 'isReceiptFullyCreated',
        13 => 'hasInventoryItems',
        14 => 'isDone',
        15 => 'isLocked',
        16 => 'isNotActual',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdById',
        20 => 'createdByName',
        21 => 'modifiedById',
        22 => 'modifiedByName',
        23 => 'supplierAddressMap',
        24 => 'billingAddressMap',
        25 => 'shippingAddressMap',
        26 => 'shippingCostConverted',
        27 => 'taxAmountCurrency',
        28 => 'taxAmountConverted',
        29 => 'discountAmountCurrency',
        30 => 'discountAmountConverted',
        31 => 'amountConverted',
        32 => 'preDiscountedAmountCurrency',
        33 => 'preDiscountedAmountConverted',
        34 => 'grandTotalAmountCurrency',
        35 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrders',
        1 => 'items',
        2 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'PurchaseOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'purchaseOrder',
        1 => 'account',
        2 => 'purchaseOrderStatus',
        3 => 'product',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'listPrice',
        9 => 'unitPrice',
        10 => 'discount',
        11 => 'amount',
        12 => 'inventoryNumberType',
        13 => 'weight',
        14 => 'taxRate',
        15 => 'order',
        16 => 'createdAt',
        17 => 'modifiedAt',
        18 => 'createdBy',
        19 => 'modifiedBy',
        20 => 'listPriceCurrency',
        21 => 'listPriceConverted',
        22 => 'unitPriceCurrency',
        23 => 'unitPriceConverted',
        24 => 'amountCurrency',
        25 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'purchaseOrderId',
        1 => 'purchaseOrderName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'purchaseOrderStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'allowFractionalQuantity',
        8 => 'productType',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'listPriceCurrency',
        12 => 'listPrice',
        13 => 'unitPriceCurrency',
        14 => 'unitPrice',
        15 => 'discount',
        16 => 'amountCurrency',
        17 => 'amount',
        18 => 'inventoryNumberType',
        19 => 'weight',
        20 => 'taxRate',
        21 => 'order',
        22 => 'createdAt',
        23 => 'modifiedAt',
        24 => 'createdById',
        25 => 'createdByName',
        26 => 'modifiedById',
        27 => 'modifiedByName',
        28 => 'listPriceCurrency',
        29 => 'listPriceConverted',
        30 => 'unitPriceCurrency',
        31 => 'unitPriceConverted',
        32 => 'amountCurrency',
        33 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Quote' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmount',
        2 => 'discountAmount',
        3 => 'preDiscountedAmount',
        4 => 'grandTotalAmount',
        5 => 'weight',
        6 => 'isDone',
        7 => 'isNotActual',
        8 => 'isLocked',
        9 => 'inventoryData',
        10 => 'inventoryStatus',
        11 => 'createdAt',
        12 => 'modifiedAt',
        13 => 'createdBy',
        14 => 'modifiedBy',
        15 => 'billingAddressMap',
        16 => 'shippingAddressMap',
        17 => 'shippingCostConverted',
        18 => 'taxAmountCurrency',
        19 => 'taxAmountConverted',
        20 => 'discountAmountCurrency',
        21 => 'discountAmountConverted',
        22 => 'amountConverted',
        23 => 'preDiscountedAmountCurrency',
        24 => 'preDiscountedAmountConverted',
        25 => 'grandTotalAmountCurrency',
        26 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmountCurrency',
        2 => 'taxAmount',
        3 => 'discountAmountCurrency',
        4 => 'discountAmount',
        5 => 'preDiscountedAmountCurrency',
        6 => 'preDiscountedAmount',
        7 => 'grandTotalAmountCurrency',
        8 => 'grandTotalAmount',
        9 => 'weight',
        10 => 'isDone',
        11 => 'isNotActual',
        12 => 'isLocked',
        13 => 'inventoryData',
        14 => 'inventoryStatus',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdById',
        18 => 'createdByName',
        19 => 'modifiedById',
        20 => 'modifiedByName',
        21 => 'billingAddressMap',
        22 => 'shippingAddressMap',
        23 => 'shippingCostConverted',
        24 => 'taxAmountCurrency',
        25 => 'taxAmountConverted',
        26 => 'discountAmountCurrency',
        27 => 'discountAmountConverted',
        28 => 'amountConverted',
        29 => 'preDiscountedAmountCurrency',
        30 => 'preDiscountedAmountConverted',
        31 => 'grandTotalAmountCurrency',
        32 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'QuoteItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'quote',
        1 => 'account',
        2 => 'quoteStatus',
        3 => 'product',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'listPrice',
        9 => 'unitPrice',
        10 => 'discount',
        11 => 'amount',
        12 => 'weight',
        13 => 'taxRate',
        14 => 'order',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdBy',
        18 => 'modifiedBy',
        19 => 'listPriceCurrency',
        20 => 'listPriceConverted',
        21 => 'unitPriceCurrency',
        22 => 'unitPriceConverted',
        23 => 'amountCurrency',
        24 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'quoteId',
        1 => 'quoteName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'quoteStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'allowFractionalQuantity',
        8 => 'productType',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'listPriceCurrency',
        12 => 'listPrice',
        13 => 'unitPriceCurrency',
        14 => 'unitPrice',
        15 => 'discount',
        16 => 'amountCurrency',
        17 => 'amount',
        18 => 'weight',
        19 => 'taxRate',
        20 => 'order',
        21 => 'createdAt',
        22 => 'modifiedAt',
        23 => 'createdById',
        24 => 'createdByName',
        25 => 'modifiedById',
        26 => 'modifiedByName',
        27 => 'listPriceCurrency',
        28 => 'listPriceConverted',
        29 => 'unitPriceCurrency',
        30 => 'unitPriceConverted',
        31 => 'amountCurrency',
        32 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReceiptOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'account',
        2 => 'amount',
        3 => 'amountConverted',
        4 => 'amountCurrency',
        5 => 'weight',
        6 => 'isDone',
        7 => 'isNotActual',
        8 => 'isLocked',
        9 => 'isHardLocked',
        10 => 'createdAt',
        11 => 'modifiedAt',
        12 => 'createdBy',
        13 => 'modifiedBy',
        14 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'accountId',
        2 => 'accountName',
        3 => 'amountCurrency',
        4 => 'amount',
        5 => 'amountConverted',
        6 => 'amountCurrency',
        7 => 'weight',
        8 => 'isDone',
        9 => 'isNotActual',
        10 => 'isLocked',
        11 => 'isHardLocked',
        12 => 'createdAt',
        13 => 'modifiedAt',
        14 => 'createdById',
        15 => 'createdByName',
        16 => 'modifiedById',
        17 => 'modifiedByName',
        18 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items',
        1 => 'receivedItems',
        2 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReceiptOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrder',
        1 => 'account',
        2 => 'receiptOrderStatus',
        3 => 'product',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'quantityReceived',
        9 => 'quantityReceivedInt',
        10 => 'inventoryNumberType',
        11 => 'isInventory',
        12 => 'weight',
        13 => 'order',
        14 => 'createdAt',
        15 => 'modifiedAt',
        16 => 'createdBy',
        17 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrderId',
        1 => 'receiptOrderName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'receiptOrderStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'allowFractionalQuantity',
        8 => 'productType',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'quantityReceived',
        12 => 'quantityReceivedInt',
        13 => 'inventoryNumberType',
        14 => 'isInventory',
        15 => 'weight',
        16 => 'order',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdById',
        20 => 'createdByName',
        21 => 'modifiedById',
        22 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReceiptOrderReceivedItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrder',
        1 => 'product',
        2 => 'inventoryNumber',
        3 => 'quantity',
        4 => 'inventoryNumberType',
        5 => 'order',
        6 => 'createdAt',
        7 => 'modifiedAt',
        8 => 'createdBy',
        9 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrderId',
        1 => 'receiptOrderName',
        2 => 'productId',
        3 => 'productName',
        4 => 'inventoryNumberId',
        5 => 'inventoryNumberName',
        6 => 'quantity',
        7 => 'inventoryNumberType',
        8 => 'order',
        9 => 'createdAt',
        10 => 'modifiedAt',
        11 => 'createdById',
        12 => 'createdByName',
        13 => 'modifiedById',
        14 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReturnOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmount',
        2 => 'discountAmount',
        3 => 'preDiscountedAmount',
        4 => 'grandTotalAmount',
        5 => 'weight',
        6 => 'isReceiptFullyCreated',
        7 => 'hasInventoryItems',
        8 => 'isDone',
        9 => 'isNotActual',
        10 => 'isLocked',
        11 => 'createdAt',
        12 => 'modifiedAt',
        13 => 'createdBy',
        14 => 'modifiedBy',
        15 => 'billingAddressMap',
        16 => 'shippingAddressMap',
        17 => 'fromAddressMap',
        18 => 'shippingCostConverted',
        19 => 'taxAmountCurrency',
        20 => 'taxAmountConverted',
        21 => 'discountAmountCurrency',
        22 => 'discountAmountConverted',
        23 => 'amountConverted',
        24 => 'preDiscountedAmountCurrency',
        25 => 'preDiscountedAmountConverted',
        26 => 'grandTotalAmountCurrency',
        27 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'taxAmountCurrency',
        2 => 'taxAmount',
        3 => 'discountAmountCurrency',
        4 => 'discountAmount',
        5 => 'preDiscountedAmountCurrency',
        6 => 'preDiscountedAmount',
        7 => 'grandTotalAmountCurrency',
        8 => 'grandTotalAmount',
        9 => 'weight',
        10 => 'isReceiptFullyCreated',
        11 => 'hasInventoryItems',
        12 => 'isDone',
        13 => 'isNotActual',
        14 => 'isLocked',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdById',
        18 => 'createdByName',
        19 => 'modifiedById',
        20 => 'modifiedByName',
        21 => 'billingAddressMap',
        22 => 'shippingAddressMap',
        23 => 'fromAddressMap',
        24 => 'shippingCostConverted',
        25 => 'taxAmountCurrency',
        26 => 'taxAmountConverted',
        27 => 'discountAmountCurrency',
        28 => 'discountAmountConverted',
        29 => 'amountConverted',
        30 => 'preDiscountedAmountCurrency',
        31 => 'preDiscountedAmountConverted',
        32 => 'grandTotalAmountCurrency',
        33 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'receiptOrders',
        1 => 'items'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'ReturnOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'returnOrder',
        1 => 'account',
        2 => 'returnOrderStatus',
        3 => 'product',
        4 => 'quantity',
        5 => 'quantityInt',
        6 => 'inventoryNumber',
        7 => 'inventoryNumberType',
        8 => 'isInventory',
        9 => 'allowFractionalQuantity',
        10 => 'productType',
        11 => 'taxRate',
        12 => 'listPrice',
        13 => 'unitPrice',
        14 => 'discount',
        15 => 'amount',
        16 => 'weight',
        17 => 'order',
        18 => 'createdAt',
        19 => 'modifiedAt',
        20 => 'createdBy',
        21 => 'modifiedBy',
        22 => 'listPriceCurrency',
        23 => 'listPriceConverted',
        24 => 'unitPriceCurrency',
        25 => 'unitPriceConverted',
        26 => 'amountCurrency',
        27 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'returnOrderId',
        1 => 'returnOrderName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'returnOrderStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'quantity',
        8 => 'quantityInt',
        9 => 'inventoryNumberId',
        10 => 'inventoryNumberName',
        11 => 'inventoryNumberType',
        12 => 'isInventory',
        13 => 'allowFractionalQuantity',
        14 => 'productType',
        15 => 'taxRate',
        16 => 'listPriceCurrency',
        17 => 'listPrice',
        18 => 'unitPriceCurrency',
        19 => 'unitPrice',
        20 => 'discount',
        21 => 'amountCurrency',
        22 => 'amount',
        23 => 'weight',
        24 => 'order',
        25 => 'createdAt',
        26 => 'modifiedAt',
        27 => 'createdById',
        28 => 'createdByName',
        29 => 'modifiedById',
        30 => 'modifiedByName',
        31 => 'listPriceCurrency',
        32 => 'listPriceConverted',
        33 => 'unitPriceCurrency',
        34 => 'unitPriceConverted',
        35 => 'amountCurrency',
        36 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'SalesOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'shippingCost',
        2 => 'taxAmount',
        3 => 'discountAmount',
        4 => 'preDiscountedAmount',
        5 => 'grandTotalAmount',
        6 => 'weight',
        7 => 'isDone',
        8 => 'isNotActual',
        9 => 'isLocked',
        10 => 'isHardLocked',
        11 => 'inventoryData',
        12 => 'inventoryStatus',
        13 => 'isDeliveryCreated',
        14 => 'hasInventoryItems',
        15 => 'createdAt',
        16 => 'modifiedAt',
        17 => 'createdBy',
        18 => 'modifiedBy',
        19 => 'billingAddressMap',
        20 => 'shippingAddressMap',
        21 => 'shippingCostCurrency',
        22 => 'shippingCostConverted',
        23 => 'taxAmountCurrency',
        24 => 'taxAmountConverted',
        25 => 'discountAmountCurrency',
        26 => 'discountAmountConverted',
        27 => 'amountConverted',
        28 => 'preDiscountedAmountCurrency',
        29 => 'preDiscountedAmountConverted',
        30 => 'grandTotalAmountCurrency',
        31 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'shippingCostCurrency',
        2 => 'shippingCost',
        3 => 'taxAmountCurrency',
        4 => 'taxAmount',
        5 => 'discountAmountCurrency',
        6 => 'discountAmount',
        7 => 'preDiscountedAmountCurrency',
        8 => 'preDiscountedAmount',
        9 => 'grandTotalAmountCurrency',
        10 => 'grandTotalAmount',
        11 => 'weight',
        12 => 'isDone',
        13 => 'isNotActual',
        14 => 'isLocked',
        15 => 'isHardLocked',
        16 => 'inventoryData',
        17 => 'inventoryStatus',
        18 => 'isDeliveryCreated',
        19 => 'hasInventoryItems',
        20 => 'createdAt',
        21 => 'modifiedAt',
        22 => 'createdById',
        23 => 'createdByName',
        24 => 'modifiedById',
        25 => 'modifiedByName',
        26 => 'billingAddressMap',
        27 => 'shippingAddressMap',
        28 => 'shippingCostCurrency',
        29 => 'shippingCostConverted',
        30 => 'taxAmountCurrency',
        31 => 'taxAmountConverted',
        32 => 'discountAmountCurrency',
        33 => 'discountAmountConverted',
        34 => 'amountConverted',
        35 => 'preDiscountedAmountCurrency',
        36 => 'preDiscountedAmountConverted',
        37 => 'grandTotalAmountCurrency',
        38 => 'grandTotalAmountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'deliveryOrders',
        1 => 'items',
        2 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'SalesOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'salesOrder',
        1 => 'account',
        2 => 'salesOrderStatus',
        3 => 'product',
        4 => 'allowFractionalQuantity',
        5 => 'productType',
        6 => 'quantity',
        7 => 'quantityInt',
        8 => 'listPrice',
        9 => 'unitPrice',
        10 => 'discount',
        11 => 'amount',
        12 => 'inventoryNumberType',
        13 => 'isInventory',
        14 => 'weight',
        15 => 'taxRate',
        16 => 'order',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdBy',
        20 => 'modifiedBy',
        21 => 'brutePrice',
        22 => 'listPriceCurrency',
        23 => 'listPriceConverted',
        24 => 'unitPriceCurrency',
        25 => 'unitPriceConverted',
        26 => 'amountCurrency',
        27 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'salesOrderId',
        1 => 'salesOrderName',
        2 => 'accountId',
        3 => 'accountName',
        4 => 'salesOrderStatus',
        5 => 'productId',
        6 => 'productName',
        7 => 'allowFractionalQuantity',
        8 => 'productType',
        9 => 'quantity',
        10 => 'quantityInt',
        11 => 'listPriceCurrency',
        12 => 'listPrice',
        13 => 'unitPriceCurrency',
        14 => 'unitPrice',
        15 => 'discount',
        16 => 'amountCurrency',
        17 => 'amount',
        18 => 'inventoryNumberType',
        19 => 'isInventory',
        20 => 'weight',
        21 => 'taxRate',
        22 => 'order',
        23 => 'createdAt',
        24 => 'modifiedAt',
        25 => 'createdById',
        26 => 'createdByName',
        27 => 'modifiedById',
        28 => 'modifiedByName',
        29 => 'brutePrice',
        30 => 'listPriceCurrency',
        31 => 'listPriceConverted',
        32 => 'unitPriceCurrency',
        33 => 'unitPriceConverted',
        34 => 'amountCurrency',
        35 => 'amountConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'ShippingProvider' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Supplier' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'billingAddressStreet',
        1 => 'billingAddressCity',
        2 => 'billingAddressState',
        3 => 'billingAddressPostalCode',
        4 => 'billingAddressCountry',
        5 => 'createdAt',
        6 => 'modifiedAt',
        7 => 'createdBy',
        8 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'billingAddressStreet',
        1 => 'billingAddressCity',
        2 => 'billingAddressState',
        3 => 'billingAddressPostalCode',
        4 => 'billingAddressCountry',
        5 => 'createdAt',
        6 => 'modifiedAt',
        7 => 'createdById',
        8 => 'createdByName',
        9 => 'modifiedById',
        10 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'SupplierProductPrice' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdBy',
        4 => 'modifiedBy',
        5 => 'priceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'name',
        1 => 'createdAt',
        2 => 'modifiedAt',
        3 => 'createdById',
        4 => 'createdByName',
        5 => 'modifiedById',
        6 => 'modifiedByName',
        7 => 'priceConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Tax' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'TransferOrder' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amountConverted',
        2 => 'weight',
        3 => 'inventoryData',
        4 => 'inventoryStatus',
        5 => 'isDone',
        6 => 'isNotActual',
        7 => 'isLocked',
        8 => 'isHardLocked',
        9 => 'createdAt',
        10 => 'modifiedAt',
        11 => 'createdBy',
        12 => 'modifiedBy',
        13 => 'fromAddressMap',
        14 => 'toAddressMap',
        15 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'number',
        1 => 'amountConverted',
        2 => 'weight',
        3 => 'inventoryData',
        4 => 'inventoryStatus',
        5 => 'isDone',
        6 => 'isNotActual',
        7 => 'isLocked',
        8 => 'isHardLocked',
        9 => 'createdAt',
        10 => 'modifiedAt',
        11 => 'createdById',
        12 => 'createdByName',
        13 => 'modifiedById',
        14 => 'modifiedByName',
        15 => 'fromAddressMap',
        16 => 'toAddressMap',
        17 => 'shippingCostConverted'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'items',
        1 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'TransferOrderItem' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'transferOrder',
        1 => 'transferOrderStatus',
        2 => 'product',
        3 => 'allowFractionalQuantity',
        4 => 'productType',
        5 => 'quantity',
        6 => 'quantityInt',
        7 => 'inventoryNumber',
        8 => 'inventoryNumberType',
        9 => 'isInventory',
        10 => 'quantityReceived',
        11 => 'quantityReceivedInt',
        12 => 'weight',
        13 => 'order',
        14 => 'createdAt',
        15 => 'modifiedAt',
        16 => 'createdBy',
        17 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'transferOrderId',
        1 => 'transferOrderName',
        2 => 'transferOrderStatus',
        3 => 'productId',
        4 => 'productName',
        5 => 'allowFractionalQuantity',
        6 => 'productType',
        7 => 'quantity',
        8 => 'quantityInt',
        9 => 'inventoryNumberId',
        10 => 'inventoryNumberName',
        11 => 'inventoryNumberType',
        12 => 'isInventory',
        13 => 'quantityReceived',
        14 => 'quantityReceivedInt',
        15 => 'weight',
        16 => 'order',
        17 => 'createdAt',
        18 => 'modifiedAt',
        19 => 'createdById',
        20 => 'createdByName',
        21 => 'modifiedById',
        22 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Warehouse' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'addressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'addressMap'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'inventoryTransactions'
      ],
      'nonAdminReadOnly' => []
    ]
  ],
  'CInsertion' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy',
        4 => 'firstName',
        5 => 'lastName',
        6 => 'address',
        7 => 'city',
        8 => 'postalCode',
        9 => 'type',
        10 => 'phoneNumber',
        11 => 'leadId',
        12 => 'vicidialUser',
        13 => 'callerId',
        14 => 'agentLogId'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName',
        6 => 'firstName',
        7 => 'lastName',
        8 => 'address',
        9 => 'city',
        10 => 'postalCode',
        11 => 'type',
        12 => 'phoneNumber',
        13 => 'leadId',
        14 => 'vicidialUser',
        15 => 'callerId',
        16 => 'agentLogId'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ],
  'Place' => (object) [
    'fields' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdBy',
        3 => 'modifiedBy'
      ],
      'nonAdminReadOnly' => []
    ],
    'attributes' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [
        0 => 'createdAt',
        1 => 'modifiedAt',
        2 => 'createdById',
        3 => 'createdByName',
        4 => 'modifiedById',
        5 => 'modifiedByName'
      ],
      'nonAdminReadOnly' => []
    ],
    'links' => (object) [
      'forbidden' => [],
      'internal' => [],
      'onlyAdmin' => [],
      'readOnly' => [],
      'nonAdminReadOnly' => []
    ]
  ]
];
