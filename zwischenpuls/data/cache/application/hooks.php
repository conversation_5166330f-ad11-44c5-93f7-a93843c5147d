<?php
return [
  'Account' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Custom\\Hooks\\Account\\AddPhone',
        'order' => 1
      ],
      1 => [
        'className' => 'Espo\\Custom\\Hooks\\Account\\SetUniquePhoneNumber',
        'order' => 2
      ]
    ],
    'afterRelate' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Account\\Contacts',
        'order' => 9
      ]
    ],
    'afterUnrelate' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Account\\Contacts',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Account\\TargetList',
        'order' => 9
      ]
    ]
  ],
  'Place' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Custom\\Hooks\\Place\\SetName',
        'order' => 9
      ]
    ]
  ],
  'Product' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Custom\\Hooks\\Product\\SetTaxValue',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Product\\InventoryNumberType',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Product\\SetUnitPrice',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Product\\TemplateSync',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Product\\TemplateSync',
        'order' => 9
      ]
    ]
  ],
  'SalesOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Custom\\Hooks\\SalesOrder\\CopyAccountToContact',
        'order' => 1
      ],
      1 => [
        'className' => 'Espo\\Custom\\Hooks\\SalesOrder\\CopyEmptyContacts',
        'order' => 2
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\Prepare',
        'order' => 9
      ],
      3 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\SetValues',
        'order' => 9
      ],
      4 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'User' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Custom\\Hooks\\User\\CreateVicidialUser',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\User\\ApiKey',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\User\\ApiKey',
        'order' => 9
      ]
    ]
  ],
  'Call' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Call\\ParentLink',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Call\\Uid',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Call\\Users',
        'order' => 12
      ]
    ]
  ],
  'Case' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\CaseObj\\Contacts',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\CaseObj\\IsInternal',
        'order' => 9
      ]
    ]
  ],
  'Contact' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Contact\\Accounts',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Contact\\TargetList',
        'order' => 9
      ]
    ],
    'afterRelate' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Contact\\Opportunities',
        'order' => 9
      ]
    ],
    'afterUnrelate' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Contact\\Opportunities',
        'order' => 9
      ]
    ]
  ],
  'KnowledgeBaseArticle' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\KnowledgeBaseArticle\\SetBodyPlain',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\KnowledgeBaseArticle\\SetOrder',
        'order' => 9
      ]
    ]
  ],
  'Lead' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Lead\\ConvertedAt',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Lead\\TargetList',
        'order' => 9
      ]
    ]
  ],
  'MassEmail' => [
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\MassEmail\\DeleteQueue',
        'order' => 9
      ]
    ]
  ],
  'Meeting' => [
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Meeting\\EmailCreatedEvent',
        'order' => 9
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Meeting\\ParentLink',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Meeting\\Uid',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Meeting\\Users',
        'order' => 12
      ]
    ]
  ],
  'Opportunity' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Opportunity\\AmountWeightedConverted',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Opportunity\\Contacts',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Opportunity\\OpportunityItem',
        'order' => 9
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Opportunity\\Probability',
        'order' => 7
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Opportunity\\LastStage',
        'order' => 8
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Opportunity\\OpportunityItem',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Opportunity\\OpportunityItem',
        'order' => 9
      ]
    ]
  ],
  'Task' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Task\\DateCompleted',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Crm\\Hooks\\Task\\ParentLink',
        'order' => 9
      ]
    ]
  ],
  'BpmnFlowchart' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnFlowchart\\CreateWorkflows',
        'order' => 9
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnFlowchart\\Prepare',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnFlowchart\\RemoveRoundRobin',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnFlowchart\\RemoveWorkflows',
        'order' => 9
      ]
    ]
  ],
  'BpmnProcess' => [
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnProcess\\RejectFlowNodes',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnProcess\\SubProcesses',
        'order' => 9
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnProcess\\RootProcess',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnProcess\\StartProcess',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnProcess\\StopProcess',
        'order' => 9
      ]
    ]
  ],
  'BpmnUserTask' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnUserTask\\Prepare',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\BpmnUserTask\\Resolve',
        'order' => 9
      ]
    ]
  ],
  'CampaignTrackingUrl' => [
    'afterClick' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\CampaignTrackingUrl\\Signal',
        'order' => 100
      ]
    ]
  ],
  'Common' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\FieldProcessing',
        'order' => -11
      ],
      1 => [
        'className' => 'Espo\\Hooks\\Common\\ForeignFields',
        'order' => 8
      ],
      2 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\UpdateDeferredFlowNodes',
        'order' => 9
      ],
      3 => [
        'className' => 'Espo\\Hooks\\Common\\AssignmentEmailNotification',
        'order' => 9
      ],
      4 => [
        'className' => 'Espo\\Hooks\\Common\\Stream',
        'order' => 9
      ],
      5 => [
        'className' => 'Espo\\Hooks\\Common\\Notifications',
        'order' => 10
      ],
      6 => [
        'className' => 'Espo\\Hooks\\Common\\StreamNotesAcl',
        'order' => 10
      ],
      7 => [
        'className' => 'Espo\\Hooks\\Common\\WebSocketSubmit',
        'order' => 20
      ],
      8 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Workflow',
        'order' => 99
      ],
      9 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ],
      10 => [
        'className' => 'Espo\\Hooks\\Common\\Webhook',
        'order' => 101
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\Stream',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Hooks\\Common\\Notifications',
        'order' => 10
      ],
      2 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ],
      3 => [
        'className' => 'Espo\\Hooks\\Common\\Webhook',
        'order' => 101
      ]
    ],
    'afterRelate' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\Stream',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterUnrelate' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\Stream',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterMassRelate' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterLeadCapture' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterConfirmation' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterOptOut' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'afterCancelOptOut' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Common\\Signal',
        'order' => 100
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\CurrencyConverted',
        'order' => 1
      ],
      1 => [
        'className' => 'Espo\\Hooks\\Common\\Collaborators',
        'order' => 7
      ],
      2 => [
        'className' => 'Espo\\Hooks\\Common\\DeleteId',
        'order' => 9
      ],
      3 => [
        'className' => 'Espo\\Hooks\\Common\\NextNumber',
        'order' => 9
      ],
      4 => [
        'className' => 'Espo\\Hooks\\Common\\Stream',
        'order' => 9
      ],
      5 => [
        'className' => 'Espo\\Hooks\\Common\\VersionNumber',
        'order' => 9
      ],
      6 => [
        'className' => 'Espo\\Hooks\\Common\\Formula',
        'order' => 11
      ],
      7 => [
        'className' => 'Espo\\Hooks\\Common\\CurrencyDefault',
        'order' => 200
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\Common\\DeleteId',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Hooks\\Common\\Notifications',
        'order' => 10
      ]
    ]
  ],
  'Report' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Report\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'TargetList' => [
    'afterOptOut' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\TargetList\\Signal',
        'order' => 100
      ]
    ],
    'afterCancelOptOut' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\TargetList\\Signal',
        'order' => 100
      ]
    ]
  ],
  'Workflow' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Workflow\\ReloadWorkflows',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Workflow\\RemoveJobs',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Workflow\\ReloadWorkflows',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Advanced\\Hooks\\Workflow\\RemoveRoundRobin',
        'order' => 9
      ]
    ]
  ],
  'DeliveryOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'DeliveryOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\DeliveryOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'InventoryAdjustment' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryAdjustment\\Validate',
        'order' => 10
      ]
    ]
  ],
  'InventoryNumber' => [
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryNumber\\DeleteId',
        'order' => 9
      ]
    ],
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryNumber\\DeleteId',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InventoryNumber\\SetIncomingDate',
        'order' => 9
      ]
    ]
  ],
  'Invoice' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Invoice\\Validate',
        'order' => 10
      ]
    ]
  ],
  'InvoiceItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\InvoiceItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'ProductAttribute' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ProductAttribute\\Order',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ProductAttribute\\Order',
        'order' => 9
      ]
    ]
  ],
  'ProductAttributeOption' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ProductAttributeOption\\Order',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ProductAttributeOption\\Order',
        'order' => 9
      ]
    ]
  ],
  'PurchaseOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\PrepareBefore',
        'order' => 8
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\Prepare',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\SetValues',
        'order' => 9
      ],
      3 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'PurchaseOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\PurchaseOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'Quote' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\Quote\\Validate',
        'order' => 10
      ]
    ]
  ],
  'QuoteItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\QuoteItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'ReceiptOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'check' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\SerialNumberCheck',
        'order' => 9
      ]
    ],
    'findInStock' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\SerialNumberCheck',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'ReceiptOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReceiptOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'ReturnOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'ReturnOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\ReturnOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'SalesOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\SalesOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'TransferOrder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\Prepare',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\SetValues',
        'order' => 9
      ],
      2 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\Validate',
        'order' => 10
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\SaveItems',
        'order' => 9
      ]
    ],
    'beforeRemove' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrder\\Validate',
        'order' => 10
      ]
    ]
  ],
  'TransferOrderItem' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Modules\\Sales\\Hooks\\TransferOrderItem\\Prepare',
        'order' => 9
      ]
    ]
  ],
  'AddressCountry' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\AddressCountry\\ClearCache',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\AddressCountry\\ClearCache',
        'order' => 9
      ]
    ]
  ],
  'Attachment' => [
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\Attachment\\RemoveFile',
        'order' => 9
      ]
    ]
  ],
  'EmailFilter' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\EmailFilter\\CacheClearing',
        'order' => 9
      ]
    ],
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\EmailFilter\\CacheClearing',
        'order' => 9
      ]
    ]
  ],
  'GroupEmailFolder' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\GroupEmailFolder\\Order',
        'order' => 9
      ]
    ]
  ],
  'Integration' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Integration\\GoogleMaps',
        'order' => 9
      ]
    ]
  ],
  'LayoutSet' => [
    'afterRemove' => [
      0 => [
        'className' => 'Espo\\Hooks\\LayoutSet\\Removal',
        'order' => 9
      ]
    ]
  ],
  'LeadCapture' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\LeadCapture\\ClearCache',
        'order' => 9
      ]
    ]
  ],
  'Note' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Note\\Mentions',
        'order' => 9
      ]
    ],
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Note\\StreamUpdatedAt',
        'order' => 9
      ],
      1 => [
        'className' => 'Espo\\Hooks\\Note\\Notifications',
        'order' => 14
      ],
      2 => [
        'className' => 'Espo\\Hooks\\Note\\WebSocketSubmit',
        'order' => 20
      ]
    ]
  ],
  'Notification' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Notification\\WebSocketSubmit',
        'order' => 20
      ]
    ]
  ],
  'Portal' => [
    'afterSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Portal\\WriteConfig',
        'order' => 9
      ]
    ]
  ],
  'Sms' => [
    'beforeSave' => [
      0 => [
        'className' => 'Espo\\Hooks\\Sms\\Numbers',
        'order' => 9
      ]
    ]
  ]
];
