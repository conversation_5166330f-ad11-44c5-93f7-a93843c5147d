<?php
return [
  'useCache' => true,
  'jobMaxPortion' => 15,
  'jobRunInParallel' => true,
  'jobPoolConcurrencyNumber' => 8,
  'daemonMaxProcessNumber' => 5,
  'daemonInterval' => 10,
  'daemonProcessTimeout' => 36000,
  'jobForceUtc' => false,
  'recordsPerPage' => 20,
  'recordsPerPageSmall' => 5,
  'recordsPerPageSelect' => 10,
  'recordsPerPageKanban' => 5,
  'applicationName' => 'Zwischenpuls CRM',
  'version' => '9.1.7',
  'timeZone' => 'Europe/Berlin',
  'dateFormat' => 'DD.MM.YYYY',
  'timeFormat' => 'HH:mm',
  'weekStart' => 1,
  'thousandSeparator' => '.',
  'decimalMark' => ',',
  'exportDelimiter' => ',',
  'currencyList' => [
    0 => 'EUR'
  ],
  'defaultCurrency' => 'EUR',
  'baseCurrency' => 'EUR',
  'currencyRates' => [],
  'currencyNoJoinMode' => false,
  'outboundEmailIsShared' => true,
  'outboundEmailFromName' => 'Zwischenpuls',
  'outboundEmailFromAddress' => '<EMAIL>',
  'smtpServer' => NULL,
  'smtpPort' => 587,
  'smtpAuth' => true,
  'smtpSecurity' => 'TLS',
  'smtpUsername' => '<EMAIL>',
  'language' => 'en_US',
  'authenticationMethod' => 'Espo',
  'globalSearchEntityList' => [],
  'tabList' => [
    0 => (object) [
      'type' => 'divider',
      'id' => '342567',
      'text' => '$CRM'
    ],
    1 => 'Account',
    2 => 'Contact',
    3 => 'Lead',
    4 => 'Opportunity',
    5 => (object) [
      'type' => 'group',
      'text' => '$SalesPack',
      'iconClass' => 'fas fa-boxes',
      'color' => NULL,
      'id' => '581923',
      'itemList' => [
        0 => 'Product',
        1 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '893944'
        ],
        2 => 'Quote',
        3 => (object) [
          'type' => 'url',
          'text' => 'My Sales Orders',
          'url' => '#SalesOrder/list/primaryFilter=onlyMy',
          'iconClass' => NULL,
          'color' => NULL,
          'aclScope' => 'SalesOrder',
          'onlyAdmin' => false,
          'id' => '572224'
        ],
        4 => 'SalesOrder',
        5 => 'Invoice',
        6 => 'DeliveryOrder',
        7 => 'ReturnOrder',
        8 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '909329'
        ],
        9 => 'PurchaseOrder',
        10 => 'ReceiptOrder',
        11 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '272414'
        ],
        12 => 'TransferOrder',
        13 => 'InventoryAdjustment',
        14 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '444529'
        ],
        15 => 'Warehouse',
        16 => 'InventoryNumber',
        17 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '103372'
        ],
        18 => 'InventoryTransaction'
      ]
    ],
    6 => (object) [
      'type' => 'divider',
      'text' => '$Activities',
      'id' => '219419'
    ],
    7 => 'Email',
    8 => 'Meeting',
    9 => 'Call',
    10 => 'Task',
    11 => 'Calendar',
    12 => (object) [
      'type' => 'divider',
      'id' => '655187',
      'text' => '$Support'
    ],
    13 => 'Case',
    14 => 'KnowledgeBaseArticle',
    15 => (object) [
      'type' => 'divider',
      'text' => NULL,
      'id' => '137994'
    ],
    16 => '_delimiter_',
    17 => (object) [
      'type' => 'divider',
      'text' => '$Marketing',
      'id' => '463280'
    ],
    18 => 'Campaign',
    19 => 'TargetList',
    20 => (object) [
      'type' => 'divider',
      'text' => '$Business',
      'id' => '518202'
    ],
    21 => 'Document',
    22 => (object) [
      'type' => 'divider',
      'text' => '$Organization',
      'id' => '566592'
    ],
    23 => 'User',
    24 => 'Team',
    25 => 'WorkingTimeCalendar',
    26 => (object) [
      'type' => 'divider',
      'text' => NULL,
      'id' => '898671'
    ],
    27 => 'EmailTemplate',
    28 => 'Template',
    29 => 'Import',
    30 => 'Report',
    31 => (object) [
      'type' => 'divider',
      'text' => NULL,
      'id' => '250470'
    ],
    32 => 'CInsertion',
    33 => 'Supplier',
    34 => 'Tax',
    35 => 'TransferOrder',
    36 => 'Warehouse',
    37 => 'Workflow',
    38 => 'SupplierProductPrice',
    39 => 'Stream',
    40 => 'ShippingProvider',
    41 => 'ReturnOrder',
    42 => 'SalesOrder',
    43 => 'ReceiptOrder',
    44 => 'Quote',
    45 => 'PurchaseOrder',
    46 => 'Product',
    47 => 'ProductPrice',
    48 => 'ProductBrand',
    49 => 'ProductAttribute',
    50 => 'ProductAttributeOption',
    51 => 'BpmnProcess',
    52 => 'BpmnUserTask',
    53 => 'BpmnFlowchart',
    54 => 'PriceBook',
    55 => 'Invoice',
    56 => 'InventoryTransaction',
    57 => 'InventoryNumber',
    58 => 'InventoryAdjustment',
    59 => 'GlobalStream',
    60 => 'DeliveryOrder',
    61 => 'Place'
  ],
  'quickCreateList' => [
    0 => 'Account',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Opportunity',
    4 => 'Meeting',
    5 => 'Call',
    6 => 'Task',
    7 => 'Case',
    8 => 'Email',
    9 => 'Campaign',
    10 => 'DeliveryOrderItem',
    11 => 'DeliveryOrder',
    12 => 'Document',
    13 => 'CInsertion',
    14 => 'InventoryAdjustmentItem',
    15 => 'InventoryAdjustment',
    16 => 'InventoryNumber',
    17 => 'InventoryTransaction',
    18 => 'InvoiceItem',
    19 => 'Invoice',
    20 => 'KnowledgeBaseArticle',
    21 => 'OpportunityItem',
    22 => 'PriceBook',
    23 => 'PriceRule',
    24 => 'BpmnUserTask',
    25 => 'BpmnProcess',
    26 => 'ProductBrand',
    27 => 'ProductPrice',
    28 => 'Product',
    29 => 'PurchaseOrderItem',
    30 => 'PurchaseOrder',
    31 => 'QuoteItem',
    32 => 'ReceiptOrderItem',
    33 => 'Quote',
    34 => 'ReceiptOrder',
    35 => 'ReturnOrderItem',
    36 => 'ReturnOrder',
    37 => 'SalesOrderItem',
    38 => 'SalesOrder',
    39 => 'SupplierProductPrice',
    40 => 'Supplier',
    41 => 'TargetList',
    42 => 'TransferOrderItem',
    43 => 'TransferOrder',
    44 => 'User'
  ],
  'exportDisabled' => true,
  'adminNotifications' => false,
  'adminNotificationsNewVersion' => true,
  'adminNotificationsCronIsNotConfigured' => true,
  'adminNotificationsNewExtensionVersion' => true,
  'assignmentEmailNotifications' => false,
  'assignmentEmailNotificationsEntityList' => [
    0 => 'Lead',
    1 => 'Opportunity',
    2 => 'Task',
    3 => 'Case'
  ],
  'assignmentNotificationsEntityList' => [
    0 => 'Call',
    1 => 'Email',
    2 => 'BpmnUserTask'
  ],
  'portalStreamEmailNotifications' => true,
  'streamEmailNotificationsEntityList' => [
    0 => 'Case'
  ],
  'streamEmailNotificationsTypeList' => [
    0 => 'Post',
    1 => 'Status',
    2 => 'EmailReceived'
  ],
  'emailNotificationsDelay' => 30,
  'emailMessageMaxSize' => 10,
  'emailRecipientAddressMaxCount' => 100,
  'notificationsCheckInterval' => 10,
  'popupNotificationsCheckInterval' => 15,
  'maxEmailAccountCount' => 2,
  'followCreatedEntities' => false,
  'b2cMode' => false,
  'theme' => 'Dark',
  'themeParams' => (object) [
    'navbar' => 'top'
  ],
  'massEmailMaxPerHourCount' => 100,
  'massEmailMaxPerBatchCount' => NULL,
  'massEmailVerp' => false,
  'personalEmailMaxPortionSize' => 50,
  'inboundEmailMaxPortionSize' => 50,
  'emailAddressLookupEntityTypeList' => [
    0 => 'User'
  ],
  'emailAddressSelectEntityTypeList' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'emailAddressEntityLookupDefaultOrder' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'phoneNumberEntityLookupDefaultOrder' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'authTokenLifetime' => 0,
  'authTokenMaxIdleTime' => 9000,
  'userNameRegularExpression' => '[^a-z0-9\\-@_\\.\\s]',
  'addressFormat' => 1,
  'displayListViewRecordCount' => true,
  'dashboardLayout' => [
    0 => (object) [
      'name' => 'My Espo',
      'layout' => [
        0 => (object) [
          'id' => 'default-stream',
          'name' => 'Stream',
          'x' => 0,
          'y' => 0,
          'width' => 2,
          'height' => 4
        ],
        1 => (object) [
          'id' => 'default-activities',
          'name' => 'Activities',
          'x' => 2,
          'y' => 2,
          'width' => 2,
          'height' => 4
        ]
      ]
    ]
  ],
  'calendarEntityList' => [
    0 => 'Meeting',
    1 => 'Call',
    2 => 'Task'
  ],
  'activitiesEntityList' => [
    0 => 'Meeting',
    1 => 'Call'
  ],
  'historyEntityList' => [
    0 => 'Meeting',
    1 => 'Call',
    2 => 'Email'
  ],
  'busyRangesEntityList' => [
    0 => 'Meeting',
    1 => 'Call'
  ],
  'emailAutoReplySuppressPeriod' => '2 hours',
  'emailAutoReplyLimit' => 5,
  'cleanupJobPeriod' => '1 month',
  'cleanupActionHistoryPeriod' => '15 days',
  'cleanupAuthTokenPeriod' => '1 month',
  'cleanupSubscribers' => true,
  'cleanupAudit' => true,
  'cleanupAuditPeriod' => '3 months',
  'currencyFormat' => 3,
  'currencyDecimalPlaces' => 2,
  'aclAllowDeleteCreated' => false,
  'aclAllowDeleteCreatedThresholdPeriod' => '24 hours',
  'attachmentAvailableStorageList' => NULL,
  'attachmentUploadMaxSize' => 256,
  'attachmentUploadChunkSize' => 4,
  'inlineAttachmentUploadMaxSize' => 20,
  'textFilterUseContainsForVarchar' => false,
  'tabColorsDisabled' => false,
  'massPrintPdfMaxCount' => 50,
  'emailKeepParentTeamsEntityList' => [
    0 => 'Case'
  ],
  'streamEmailWithContentEntityTypeList' => [
    0 => 'Case'
  ],
  'recordListMaxSizeLimit' => 200,
  'noteDeleteThresholdPeriod' => '1 month',
  'noteEditThresholdPeriod' => '7 days',
  'emailForceUseExternalClient' => false,
  'useWebSocket' => true,
  'auth2FAMethodList' => [
    0 => 'Totp'
  ],
  'auth2FAInPortal' => false,
  'personNameFormat' => 'firstLast',
  'newNotificationCountInTitle' => false,
  'pdfEngine' => 'Dompdf',
  'smsProvider' => NULL,
  'mapProvider' => 'Google',
  'defaultFileStorage' => 'EspoUploadDir',
  'ldapUserNameAttribute' => 'sAMAccountName',
  'ldapUserFirstNameAttribute' => 'givenName',
  'ldapUserLastNameAttribute' => 'sn',
  'ldapUserTitleAttribute' => 'title',
  'ldapUserEmailAddressAttribute' => 'mail',
  'ldapUserPhoneNumberAttribute' => 'telephoneNumber',
  'ldapUserObjectClass' => 'person',
  'ldapPortalUserLdapAuth' => false,
  'passwordGenerateLength' => 10,
  'massActionIdleCountThreshold' => 100,
  'exportIdleCountThreshold' => 1000,
  'oidcJwtSignatureAlgorithmList' => [
    0 => 'RS256'
  ],
  'oidcUsernameClaim' => 'sub',
  'oidcFallback' => true,
  'oidcScopes' => [
    0 => 'profile',
    1 => 'email',
    2 => 'phone'
  ],
  'listViewSettingsDisabled' => false,
  'cleanupDeletedRecords' => true,
  'phoneNumberNumericSearch' => true,
  'phoneNumberInternational' => true,
  'phoneNumberPreferredCountryList' => [
    0 => 'de'
  ],
  'wysiwygCodeEditorDisabled' => false,
  'customPrefixDisabled' => true,
  'listPagination' => true,
  'cacheTimestamp' => **********,
  'microtime' => **********.619754,
  'siteUrl' => 'https://zwischenpulscrm.integradial.us',
  'fullTextSearchMinLength' => 4,
  'appTimestamp' => **********,
  'adminPanelIframeDisabled' => true,
  'userThemesDisabled' => true,
  'webSocketUrl' => 'wss://zwischenpulscrm.integradial.us:8080',
  'avatarsDisabled' => true,
  'scopeColorsDisabled' => false,
  'tabIconsDisabled' => false,
  'dashletsOptions' => (object) [],
  'maintenanceMode' => false,
  'cronDisabled' => false,
  'adminPanelIframeUrl' => '',
  'fiscalYearShift' => 0,
  'addressCountryList' => [],
  'addressCityList' => [],
  'addressStateList' => [],
  'emailAddressIsOptedOutByDefault' => false,
  'workingTimeCalendarName' => NULL,
  'workingTimeCalendarId' => NULL,
  'inventoryTransactionsEnabled' => true,
  'warehousesEnabled' => true,
  'priceBooksEnabled' => true,
  'defaultPriceBookName' => NULL,
  'defaultPriceBookId' => NULL,
  'companyLogoId' => '66429ecf2ece4a0b3',
  'companyLogoName' => 'zwichenspuls_logo.jpg',
  'authTokenPreventConcurrent' => false,
  'auth2FA' => false,
  'passwordStrengthLength' => NULL,
  'passwordStrengthLetterCount' => NULL,
  'passwordStrengthBothCases' => false,
  'passwordStrengthNumberCount' => NULL,
  'passwordRecoveryDisabled' => false,
  'passwordRecoveryForAdminDisabled' => false,
  'passwordRecoveryNoExposure' => false,
  'passwordRecoveryForInternalUsersDisabled' => false,
  'appLogAdminAllowed' => false,
  'notePinnedMaxCount' => 5,
  'oidcAuthorizationPrompt' => 'consent',
  'phoneNumberExtensions' => false,
  'starsLimit' => 500,
  'quickSearchFullTextAppendWildcard' => false,
  'authIpAddressCheck' => false,
  'authIpAddressWhitelist' => [],
  'authIpAddressCheckExcludedUsersIds' => [],
  'authIpAddressCheckExcludedUsersNames' => (object) [],
  'outboundEmailBccAddress' => NULL,
  'massEmailOpenTracking' => true,
  'massEmailDisableMandatoryOptOutLink' => false,
  'advancedPackUpdateSkipWorkflowOrder' => true,
  'tabQuickSearch' => true,
  'passwordStrengthSpecialCharacterCount' => NULL,
  'availableReactions' => [
    0 => 'Like'
  ],
  'streamReactionsCheckMaxSize' => 50,
  'emailScheduledBatchCount' => 50,
  'emailAddressMaxCount' => 10,
  'phoneNumberMaxCount' => 10,
  'iframeSandboxExcludeDomainList' => [
    0 => 'youtube.com',
    1 => 'google.com'
  ]
];
