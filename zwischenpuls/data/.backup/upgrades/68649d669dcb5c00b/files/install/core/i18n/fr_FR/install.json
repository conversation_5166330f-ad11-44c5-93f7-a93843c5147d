{"labels": {"Main page title": "Bienvenue sur EspoCRM", "Start page title": "Accord de Licence", "Step1 page title": "Accord de Licence", "License Agreement": "Accord de Licence", "I accept the agreement": "J'accepte l'accord de Licence", "Step2 page title": "Configuration de la base de données", "Step3 page title": "Configuration de l'Administrateur", "Step4 page title": "Paramètres du système", "Step5 page title": "Paramètres SMTP pour envoyer les emails", "Errors page title": "<PERSON><PERSON><PERSON>", "Finish page title": "L'installation est terminée", "Congratulation! Welcome to EspoCRM": "Félicitation! EspoCRM a été installé avec succès.", "share": "Si vous appréciez EspoCRM, partagez le avec vos amis. Faites leur connaître le produit", "Installation Guide": "Instructions d'installation", "Locale": "Local", "Outbound Email Configuration": "Configuration de l'envoi d'email", "Start": "Lancement", "Back": "Retour", "Next": "Suivant", "Go to EspoCRM": "Aller vers EspoCRM", "Re-check": "Revérifier", "Test settings": "Tester la connexion", "Database Settings Description": "Entrez vos informations de connexion à la base MYSQL (nom d'hôte, nom d'utilisateur et mot de passe). <PERSON><PERSON> pouvez spécifier le port serveur pour le nom d'hôte comme localhost:3306.", "Install": "Installation", "Configuration Instructions": "Instructions de configuration", "phpVersion": "Version PHP", "requiredMysqlVersion": "Version MySQL", "dbHostName": "Nom d'hôte", "dbName": "Nom de la base de donnée", "dbUserName": "Nom d'utilisateur de la base de données", "SetupConfirmation page title": "Configuration requise", "PHP Configuration": "Paramètres PHP", "MySQL Configuration": "Paramètres de base de données", "Permission Requirements": "Les permissions", "Success": "Su<PERSON>ès", "Fail": "<PERSON><PERSON><PERSON>", "is recommended": "est recommandé", "extension is missing": "l'extension est manquante", "headerTitle": "Installation EspoCRM", "Crontab setup instructions": "Sans exécuter les e-mails entrants des travaux planifiés, les notifications et les rappels ne fonctionneront pas. Ici vous pouvez lire {SETUP_INSTRUCTIONS}.", "Setup instructions": "instructions d'installation"}, "fields": {"Choose your language": "Choisissez votre langue", "Database Name": "Nom de la base de données", "Host Name": "Nom d'hôte", "Database User Name": "Nom d'utilisateur de la base de données", "Database User Password": "Mot de passe pour l'utilisateur de la base de données", "Database driver": "Pilote de base de données", "User Name": "Nom d'utilisateur", "Password": "Mot de passe", "smtpPassword": "Mot de passe", "Confirm Password": "Confirmer le mot de passe", "From Address": "<PERSON><PERSON><PERSON> <PERSON> l’émetteur", "From Name": "Nom de l'expéditeur", "Is Shared": "Est partagé", "Date Format": "Format de date", "Time Format": "Format de l'heure", "Time Zone": "<PERSON><PERSON> ho<PERSON>", "First Day of Week": "Premier j<PERSON> de la semaine", "Thousand Separator": "Séparateur de milliers", "Decimal Mark": "Marqueur décimal", "Default Currency": "Monnaie courante", "Currency List": "Liste des monnaies", "Language": "<PERSON><PERSON>", "smtpServer": "Ser<PERSON><PERSON>", "smtpAuth": "Autorisation", "smtpSecurity": "Sécurité", "smtpUsername": "Nom d'utilisateur"}, "messages": {"1049": "Base de donnée inconnue", "2005": "Hôte du server MYSQL est inconnu", "Some errors occurred!": "Des erreurs se sont produites !", "The PHP extension was not found...": "L'extension PHP <b>{extName}</b> est introuvable...", "All Settings correct": "Les Paramètres sont corrects", "Failed to connect to database": "Impossible de connecter la base de donnée", "PHP version": "Version PHP", "You must agree to the license agreement": "<PERSON><PERSON> devez agréer l'accord de licence", "Passwords do not match": "Les mots de passes sont différents", "Enable mod_rewrite in Apache server": "Activer mod_rewrite sur le serveur Apache", "checkWritable error": "Erreur de vérification d'écriture", "applySett error": "<PERSON><PERSON><PERSON> apply<PERSON>", "buildDatabase error": "Erreur dans la construction de la base de donnée", "createUser error": "Erreur dans la création de l'utilisateur", "checkAjaxPermission error": "Erreur checkAjaxPermission", "Cannot create user": "Impossible de créer l'utilisateur", "Permission denied": "Permission refusée", "Permission denied to": "Permission refusée", "Can not save settings": "Impossible d'enregistrer les paramètres", "Cannot save preferences": "Impossible d'enregistrer les préférences", "extension": "l'extension {0} est manquante", "option": "la valeur recommandée est {0}", "requiredMariadbVersion": "Votre version de MariaDB n’est pas supportée par EspoCRM, ve<PERSON><PERSON>z mettre à jour au moins vers MariaDB {minVersion}", "Ajax failed": "une erreur inattendue est apparue", "Bad init Permission": "Autorisation refusée pour le répertoire \\ \"{*} \". Veuillez définir 77 \"pour \" {*} \\ \"ou simplement exécuter cette commande dans le terminal <pre> <b> {C} </ b> </ pre> L'opération n'est pas autorisée? Essayez celui-ci: {CSU}", "permissionInstruction": "<br> Exécutez cette commande dans le terminal: <pre> <b> \\ \"{C} \" </ b> </ pre>", "operationNotPermitted": "L'opération n'est pas autorisée? Essayez celui-ci: <br> <br> {CSU}"}, "systemRequirements": {"requiredPhpVersion": "Version PHP", "requiredMysqlVersion": "Version MySQL", "host": "Nom d'hôte", "dbname": "Nom de la base de données", "user": "Nom d'utilisateur", "writable": "Enregistrable", "readable": "Lisible", "requiredMariadbVersion": "Version de MariaDB"}}