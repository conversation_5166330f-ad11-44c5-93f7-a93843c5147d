{"fields": {"name": "Nom", "status": "Statut", "job": "<PERSON><PERSON><PERSON>", "scheduling": "Planification (crontab notation)"}, "labels": {"Create ScheduledJob": "<PERSON><PERSON>er une tâche planifiée", "As often as possible": "Aussi souvent que possible"}, "options": {"job": {"Cleanup": "<PERSON><PERSON><PERSON>", "CheckInboundEmails": "Vérifier les emails entrants", "CheckEmailAccounts": "Vérifier ses comptes emails personnels", "SendEmailReminders": "Envoyer des notifications par email", "CheckNewVersion": "Vérifier la nouvelle version", "ProcessWebhookQueue": "Traiter la file d'attente Webhook"}, "cronSetup": {"linux": "Note: <PERSON><PERSON><PERSON><PERSON> cette ligne dans le fichier crontab pour lancer les tâches planifiées:", "mac": "Note: <PERSON><PERSON><PERSON><PERSON> cette ligne dans le fichier crontab pour lancer les tâches planifiées:", "windows": "Note: <PERSON><PERSON>ez un fichier de commandes avec les commandes suivantes pour exécuter des tâches planifiées Windows", "default": "Note: A<PERSON><PERSON> cette commande pour Cron (tâche planifiée):"}, "status": {"Active": "Actif", "Inactive": "Inactif"}}}