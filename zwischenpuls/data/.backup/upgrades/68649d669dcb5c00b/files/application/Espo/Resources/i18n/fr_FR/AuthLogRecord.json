{"fields": {"username": "Nom d'utilisateur", "ipAddress": "Adresse IP", "requestTime": "Temps de demande", "createdAt": "<PERSON><PERSON><PERSON>", "isDenied": "Est refusé", "denialReason": "<PERSON><PERSON><PERSON>", "portal": "Portail", "user": "Utilisa<PERSON>ur", "authToken": "Jeton d'authentification créé", "requestUrl": "URL de demande", "requestMethod": "<PERSON><PERSON><PERSON><PERSON> de demande", "authTokenIsActive": "Le jeton d'authentification est actif", "authenticationMethod": "Méthode d'authentification"}, "links": {"authToken": "Jeton d'authentification créé", "user": "Utilisa<PERSON>ur", "portal": "Portail", "actionHistoryRecords": "Histoire d'action"}, "presetFilters": {"denied": "<PERSON><PERSON><PERSON><PERSON>", "accepted": "Accepté"}, "options": {"denialReason": {"CREDENTIALS": "les informations d'identification invalides", "INACTIVE_USER": "Utilisateur inactif", "IS_PORTAL_USER": "Utilisateur du portail", "IS_NOT_PORTAL_USER": "Pas un utilisateur du portail", "USER_IS_NOT_IN_PORTAL": "L'utilisateur n'est pas lié au portail"}}}