{"scopeNames": {"User": "Utilisa<PERSON>ur", "Team": "Équipe", "Role": "R<PERSON><PERSON>", "EmailTemplate": "<PERSON>d<PERSON><PERSON> d'email", "EmailAccount": "<PERSON><PERSON><PERSON>", "EmailAccountScope": "<PERSON><PERSON><PERSON>", "OutboundEmail": "Email Sortant", "ScheduledJob": "Tâche planifiée", "ExternalAccount": "Compte externe", "Dashboard": "Tableau de bord", "InboundEmail": "Email entrant", "Stream": "Flux", "Template": "<PERSON><PERSON><PERSON><PERSON>", "Job": "<PERSON><PERSON><PERSON>", "EmailFilter": "Filtre email", "Portal": "Portail", "PortalRole": "Rôle Portail", "Attachment": "Pièce jointe", "LastViewed": "Dernier enregistrement", "Settings": "Paramètres", "EntityManager": "Gestionnaire de Fonctionnalités", "Export": "Exporter", "AuthLogRecord": "Enregistrement du journal d'authentification", "AuthFailLogRecord": "Enregistrement du journal d'échec d'authentification", "EmailTemplateCategory": "Catégories de modèles de courrier électronique", "LeadCapture": "Point d'entrée de capture de plomb", "LeadCaptureLogRecord": "Enregistrement du journal de capture de plomb", "ArrayValue": "<PERSON><PERSON> du <PERSON>au", "ApiUser": "API utilisateur", "DashboardTemplate": "<PERSON><PERSON><PERSON><PERSON>au de bord", "Currency": "<PERSON><PERSON>"}, "scopeNamesPlural": {"User": "Utilisateurs", "Team": "Équipes", "Role": "<PERSON><PERSON><PERSON>", "EmailTemplate": "Mod<PERSON><PERSON> d'email", "EmailAccount": "<PERSON><PERSON><PERSON>", "EmailAccountScope": "<PERSON><PERSON><PERSON>", "OutboundEmail": "Emails sortants", "ScheduledJob": "Tâches planifiées", "ExternalAccount": "Comptes externes", "Dashboard": "Tableau de bord", "InboundEmail": "Emails entrants", "Stream": "Flux", "Template": "<PERSON><PERSON><PERSON><PERSON>", "Job": "Tâches", "EmailFilter": "Filtres Email", "Portal": "Portails", "PortalRole": "Roles <PERSON>", "Attachment": "Pièces jointes", "LastViewed": "Dernières vues", "AuthLogRecord": "Journal d'authentification", "AuthFailLogRecord": "Journal d'échec d'authentification", "EmailTemplateCategory": "Catégories de modèles de courrier électronique", "Import": "Importation", "LeadCapture": "Capture de plomb", "LeadCaptureLogRecord": "Journal de capture de plomb", "ArrayValue": "<PERSON><PERSON> de tableau", "ApiUser": "Utilisateurs d'API", "DashboardTemplate": "<PERSON><PERSON><PERSON><PERSON> tableau de bord", "EmailAddress": "Adresses email", "PhoneNumber": "Numéros de téléphone", "Currency": "<PERSON><PERSON>"}, "labels": {"Misc": "Divers", "Merge": "<PERSON><PERSON>", "None": "Aucun", "Home": "Accueil", "by": "par", "Saved": "<PERSON><PERSON><PERSON><PERSON>", "Error": "<PERSON><PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Not valid": "Invalide", "Please wait...": "Veuillez patienter...", "Please wait": "<PERSON><PERSON><PERSON><PERSON>er", "Loading...": "Chargement...", "Uploading...": "Mise en ligne...", "Sending...": "Envoi...", "Merged": "Fusionné", "Removed": "Supprimé", "Posted": "<PERSON><PERSON>", "Linked": "<PERSON><PERSON>", "Unlinked": "<PERSON><PERSON><PERSON><PERSON>", "Done": "Effectué ", "Access denied": "<PERSON><PERSON>ès refusé", "Not found": "Pas de rés<PERSON>at", "Access": "Accès", "Are you sure?": "Êtes-vous sûr?", "Record has been removed": "La donnée a été supprimée", "Wrong username/password": "Mauvaise combinaison nom d'utilisateur/mot de passe", "Post cannot be empty": "La note ne peut pas être laissée vide", "Username can not be empty!": "Le nom d'utilisateur ne peut pas être laissé vide!", "Cache is not enabled": "Le cache est désactivé", "Cache has been cleared": "Le cache a été vidé", "Rebuild has been done": "La reconstruction a été faite", "Modified": "<PERSON><PERSON><PERSON><PERSON>", "Created": "<PERSON><PERSON><PERSON>", "Create": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "Overview": "Vue d'ensemble", "Details": "Détails", "Add Field": "Ajouter un filtre", "Add Dashlet": "Ajouter un widget", "Filter": "Filtre", "Edit Dashboard": "<PERSON><PERSON><PERSON> le tableau de bord", "Add": "Ajouter", "Add Item": "Ajouter un élément", "Reset": "Réinitialiser", "More": "Plus", "Search": "Recherche", "Only My": "<PERSON><PERSON>", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "About": "A propos", "Refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Username": "Nom d'utilisateur", "Password": "Mot de passe", "Login": "Connexion", "Log Out": "Se déconnecter", "Preferences": "Préférences", "State": "Région", "Street": "Rue", "Country": "Pays", "City": "Ville", "PostalCode": "Code postal", "Followed": "<PERSON><PERSON><PERSON>", "Follow": "Suivre", "Followers": "<PERSON><PERSON><PERSON>", "Clear Local Cache": "Vider le cache local", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Update": "Mettre à jour", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "É<PERSON>er", "View": "Voir", "Cancel": "Annuler", "Apply": "Appliquer", "Unlink": "Délier", "Mass Update": "Mise à jour groupée", "Export": "Exporter", "No Data": "<PERSON><PERSON><PERSON> don<PERSON>", "No Access": "Au<PERSON>n <PERSON>", "All": "Tous", "Active": "Actif", "Inactive": "Inactif", "Write your comment here": "Écrivez votre commentaire ici", "Post": "Poster", "Stream": "Flux", "Show more": "Voir davantage", "Dashlet Options": "Options du widget", "Full Form": "Formulaire complet", "Insert": "<PERSON><PERSON><PERSON><PERSON>", "Person": "<PERSON><PERSON>", "First Name": "Prénom", "Last Name": "Nom", "You": "Vous", "you": "vous", "change": "changer", "Change": "Changer", "Primary": "Primaire", "Save Filter": "Sauvegarder le filtre", "Run Import": "Démarrer l'import", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "Mark all read": "<PERSON><PERSON> tout comme lu", "See more": "Voir davantage", "Today": "<PERSON><PERSON><PERSON>'hui", "Tomorrow": "<PERSON><PERSON><PERSON>", "Yesterday": "<PERSON>er", "Submit": "Envoyer", "Close": "<PERSON><PERSON><PERSON>", "Yes": "O<PERSON>", "No": "Non", "Value": "<PERSON><PERSON>", "Current version": "Version actuelle", "List View": "Vue en liste", "Tree View": "Vue en arborescence", "Unlink All": "Tout déconnecter", "Print to PDF": "Enregistrer en PDF", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "Number": "Nombre", "From": "De", "To": "A", "Create Post": "Créer une publication", "Previous Entry": "Précédent", "Next Entry": "Suivant", "View List": "Vue en liste", "Attach File": "Attacher un fichier", "Return to Application": "Retour à l'application", "Expand": "Développer", "Collapse": "<PERSON><PERSON><PERSON><PERSON>", "New notifications": "Nouvelles notifications", "Manage Categories": "<PERSON><PERSON><PERSON> les catégories", "Manage Folders": "<PERSON><PERSON><PERSON> les dossiers", "Convert to": "Convertir en", "View Personal Data": "Voir les données personnelles", "Personal Data": "<PERSON><PERSON><PERSON>", "Erase": "<PERSON><PERSON><PERSON><PERSON>", "Move Over": "Bouge", "Restore": "<PERSON><PERSON><PERSON>", "View Followers": "Voir les abonnés", "Convert Currency": "Convertir la monnaie", "Middle Name": "Second prénom", "View on Map": "Voir sur la carte", "Preview": "Prévisualisation", "Sort": "<PERSON><PERSON>", "Global Search": "Recherche générale", "Copy to Clipboard": "Copier dans le presse-papiers", "Copied to clipboard": "Copié dans le presse-papiers"}, "messages": {"pleaseWait": "Veuillez patienter...", "confirmLeaveOutMessage": "Êtes-vous sûr de vouloir quitter le formulaire?", "notModified": "Vous n'avez pas modifié l'enregistrement", "fieldIsRequired": "{field} est requis", "fieldShouldAfter": "{field} doit être après {otherField}", "fieldShouldBefore": "{field} doit être avant {otherField}", "fieldShouldBeBetween": "{field} doit être compris entre {min} et {max}", "fieldBadPasswordConfirm": "{field} n'a pas été confirmé correctement", "resetPreferencesDone": "Vos préférences ont été réinitialisées.", "confirmation": "Êtes-vous sûr?", "unlinkAllConfirmation": "Êtes-vous certain de vouloir déconnecter tous les enregistrements liés ?", "resetPreferencesConfirmation": "Êtes-vous sûr de vouloir réinitialiser vos préférences?", "removeRecordConfirmation": "Êtes-vous sûr de vouloir supprimer cet enregistrement ?", "unlinkRecordConfirmation": "Êtes-vous sûr de vouloir casser cette relation?", "removeSelectedRecordsConfirmation": "Êtes-vous sûr de vouloir supprimer les enregistrements sélectionnés?", "massUpdateResult": "{count} enregistrements ont été mis à jour", "massUpdateResultSingle": "{count} enregistrement a été mis à jour", "noRecordsUpdated": "Aucun enregistrement n'a été trouvé", "massRemoveResult": "{count} enregistrements ont été supprimés", "massRemoveResultSingle": "{count} enregistrement a été supprimé", "noRecordsRemoved": "Aucun enregistrement n'a été trouvé", "clickToRefresh": "Cliquer pour rafraîchir", "writeYourCommentHere": "Écrivez votre commentaire ici", "writeMessageToUser": "Écrire un message à {user}", "typeAndPressEnter": "Écrivez puis appuyez sur la touche Entrée", "checkForNewNotifications": "Vérifier les nouvelles notifications", "loading": "Chargement...", "saving": "Sauvegarde...", "fieldMaxFileSizeError": "Le fichier ne doit pas dépasser {max} Mo", "fieldIsUploading": "En cours de téléchargement", "erasePersonalDataConfirmation": "Les champs cochés seront effacés de façon permanente. Êtes-vous sûr?", "massPrintPdfMaxCountError": "Impossible d'imprimer plus que {maxCount} enregistrements.", "fieldValueDuplicate": "<PERSON><PERSON><PERSON><PERSON> la valeur", "unlinkSelectedRecordsConfirmation": "Êtes-vous sûr de vouloir dissocier les enregistrements sélectionnés?", "recalculateFormulaConfirmation": "Êtes-vous sûr de vouloir recalculer la formule pour les enregistrements sélectionnés?", "fieldExceedsMaxCount": "Le nombre dépasse le maximum autorisé {maxCount}", "notUpdated": "Pas à jour", "maintenanceMode": "L'application est actuellement en mode maintenance. Seuls les utilisateurs administrateurs ont accès. \n\nLe mode maintenance peut être désactivé dans Administration → Paramètres.", "fieldInvalid": "{field} n'est pas valide", "fieldPhoneInvalid": "{field} n'est pas valide", "fieldNotMatchingPattern": "{field} ne correspond pas au modèle `{pattern}`", "fieldNotMatchingPattern$noBadCharacters": "{field} contient des caractères interdits", "fieldNotMatchingPattern$noAsciiSpecialCharacters": "{field} ne devrait pas contenir des caractères spéciaux ASCII", "fieldNotMatchingPattern$latinLetters": "{field} ne peut contenir que des lettres latines", "fieldNotMatchingPattern$latinLettersDigits": "{field} ne peut contenir que des lettres latines et des chiffres", "fieldNotMatchingPattern$latinLettersDigitsWhitespace": "{field} ne peut contenir que des lettres latines, des chiffres et des espaces", "fieldNotMatchingPattern$latinLettersWhitespace": "{field} ne peut contenir que des lettres latines et des espaces", "fieldNotMatchingPattern$digits": "{field} ne peut contenir que des chiffres", "confirmAppRefresh": "L'application a été mise à jour. Il est recommandé de rafraîchir la page pour en assurer le bon fonctionnement.", "fieldShouldBeNumber": "{field} devrait être un nombre valide", "maintenanceModeError": "L'application est actuellement en mode maintenance.", "fieldNotMatchingPattern$uriOptionalProtocol": "{field} doit être une URL valide", "fieldShouldBeLess": "{field} ne devrait pas être supérieur à {value}", "fieldShouldBeGreater": "{field} ne devrait pas être inférieur à {value}", "fieldPhoneInvalidCode": "Le code du pays est invalide"}, "boolFilters": {"onlyMy": "<PERSON><PERSON>", "followed": "<PERSON><PERSON><PERSON>", "onlyMyTeam": "Mon équipe"}, "presetFilters": {"followed": "<PERSON><PERSON><PERSON>", "all": "Tous"}, "massActions": {"remove": "<PERSON><PERSON><PERSON><PERSON>", "merge": "<PERSON><PERSON>", "massUpdate": "Mise à jour groupée", "export": "Exporter", "follow": "Suivre", "unfollow": "Ne plus suivre", "convertCurrency": "Convertir la monnaie", "printPdf": "Imprimer en PDF", "unlink": "Dissocier", "recalculateFormula": "Recalculer la formule"}, "fields": {"name": "Nom", "firstName": "Prénom", "lastName": "Nom de famille", "assignedUser": "Utilisateur <PERSON>", "assignedUsers": "Utilisateurs assignés", "assignedUserName": "Nom de l'utilisateur assigné", "teams": "Équipes", "createdAt": "Date de création", "modifiedAt": "Modi<PERSON><PERSON>", "createdBy": "C<PERSON><PERSON> par", "modifiedBy": "Modifié par", "address": "<PERSON><PERSON><PERSON>", "phoneNumber": "Téléphone", "phoneNumberMobile": "Téléphone (Mobile)", "phoneNumberHome": "Téléphone (Maison)", "phoneNumberFax": "Fax", "phoneNumberOffice": "Téléphone (Bureau)", "phoneNumberOther": "Téléphone (Autre)", "order": "Tri", "children": "<PERSON><PERSON><PERSON>", "emailAddressData": "Adresse électronique", "phoneNumberData": "Numéro de téléphone", "ids": "Identifiants", "names": "Des noms", "emailAddressIsOptedOut": "L'adresse e-mail est désactivée", "targetListIsOptedOut": "Est désinscrit (liste cible)", "phoneNumberIsOptedOut": "Le numéro de téléphone est désactivé", "types": "Les types", "middleName": "Second prénom", "phoneNumberIsInvalid": "Le numéro de téléphone est invalide"}, "links": {"assignedUser": "Utilisateur <PERSON>", "createdBy": "C<PERSON><PERSON> par", "modifiedBy": "Modifié par", "team": "Équipe", "roles": "<PERSON><PERSON><PERSON>", "teams": "Équipes", "users": "Utilisateurs", "children": "<PERSON><PERSON><PERSON>"}, "dashlets": {"Stream": "Flux", "Emails": "<PERSON> boite de ré<PERSON>"}, "notificationMessages": {"assign": "{entityType} {entity} vous a été assigné", "emailReceived": "Email reçu de la part de {from}", "entityRemoved": "{user} a supprimé {entityType} {entity}"}, "streamMessages": {"post": "{user} a posté sur {entityType} {entity}", "attach": "{user} a joint un fichier à {entityType} {entity}", "status": "{user} a mis à jour le {field} pour {entityType} {entity}", "update": "{user} a mis à jour {entityType} {entity}", "postTargetTeam": "{user} a publié pour l’équipe {target}", "postTargetTeams": "{user} a publié pour les équipes {target}", "postTargetPortal": "{user} a publié sur le portail {target}", "postTargetPortals": "{user} a publié sur les portails {target}", "postTarget": "{user} a publié pour {target}", "postTargetYou": "{user} a publié pour vous", "postTargetYouAndOthers": "{user} a publié pour {target} et vous", "postTargetAll": "{user} a publié pour tous", "mentionInPost": "{user} a mentionné {mentioned} dans {entityType} {entity}", "mentionYouInPost": "{user} vous a mentionné dans {entityType} {entity}", "mentionInPostTarget": "{user} a mentionné {mentionned} dans une publication", "mentionYouInPostTarget": "{user} vous a mentionné dans une publication pour {target}", "mentionYouInPostTargetAll": "{user} vous a mentionné dans une publication pour tous", "mentionYouInPostTargetNoTarget": "{user} vous a mentionné dans une publication", "create": "{user} a créé {entityType} {entity}", "createThis": "{user} a créé {entityType}", "createAssignedThis": "{user} a créé {entityType} assignée à {assignee}", "createAssigned": "{user} a créé {entityType} {entity} et l'a assigné à {assignee}", "assign": "{user} a assigné {entityType} {entity} à {assignee}", "assignThis": "{user} a assigné {entityType} à {assignee}", "postThis": "{user} a posté", "attachThis": "{user} a joint un fichier", "statusThis": "{user} a mis à jour {field}", "updateThis": "{user} a mis à jour {entityType}", "createRelatedThis": "{user} a créé {relatedEntityType} {relatedEntity} reliée à {entityType}", "createRelated": "{user} a créé {relatedEntityType} {relatedEntity} relié au {entityType} {entity}", "relate": "{user} a relié {relatedEntityType} {relatedEntity} avec {entityType} {entity}", "relateThis": "{user} a relié {relatedEntityType} {relatedEntity} avec {entityType}", "emailReceivedFromThis": "Email reçu de la part de {from}", "emailReceivedInitialFromThis": "Email reçu de la part de {from}, {entityType} créé", "emailReceivedThis": "<PERSON><PERSON>", "emailReceivedInitialThis": "<PERSON><PERSON>, {entityType} c<PERSON><PERSON>", "emailReceivedFrom": "Email reçu de la part de  {from}, relié à {entityType} {entity}", "emailReceivedFromInitial": "Email reçu de la part de {from}, {entityType} {entity} créé", "emailReceivedInitialFrom": "Email reçu de la part de {from}, {entityType} {entity} créé", "emailReceived": "Email reçu relié à {entityType} {entity}", "emailReceivedInitial": "Email reçu: {entityType} {entity} créé", "emailSent": "{by} a envoyé un email lié à {entityType} {entity}", "emailSentThis": "{by} a envoyé un email", "createAssignedYou": "{user} a créé {entityType} {entity} et vous l'a attribué", "createAssignedThisSelf": "{user} a créé {entityType} avec auto-attribution", "createAssignedSelf": "{user} a créé {entityType} {entity} avec auto-attribution", "assignYou": "{user} vous a attribué {entityType} {entity}", "assignThisVoid": "{user} a retiré l'attribution de {entityType}", "assignVoid": "{user} a retiré l'attribution de {entityType} {entity}", "assignThisSelf": "{user} a mis {entityType} en auto-attribution", "assignSelf": "{user} a mis {entityType} {entity} en auto-attribution"}, "lists": {"monthNames": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mars", "Avril", "<PERSON>", "Juin", "<PERSON><PERSON><PERSON>", "Août", "Septembre", "Octobre", "Novembre", "Décembre"], "monthNamesShort": ["Jan", "Fév", "Mar", "Avr", "<PERSON>", "Jun", "Jul", "Aoû", "Sep", "Oct", "Nov", "Déc"], "dayNames": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "dayNamesShort": ["<PERSON><PERSON>", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ven", "Sam"], "dayNamesMin": ["Di", "<PERSON>", "Ma", "Me", "Je", "Ve", "Sa"]}, "options": {"salutationName": {"Mr.": "<PERSON>.", "Mrs.": "Mme.", "Ms.": "<PERSON><PERSON><PERSON>"}, "dateSearchRanges": {"on": "Actif", "notOn": "Inactif", "after": "<PERSON><PERSON>", "before": "Avant", "between": "<PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "past": "Passé", "future": "Futur", "currentMonth": "Mois en cours", "lastMonth": "<PERSON><PERSON> mois", "currentQuarter": "<PERSON>uinzaine en cours", "lastQuarter": "<PERSON><PERSON><PERSON> quinzaine", "currentYear": "Année en cours", "lastYear": "<PERSON><PERSON><PERSON> ann<PERSON>", "lastSevenDays": "7 derniers jours", "lastXDays": "Derniers X jours", "nextXDays": "Prochains X jours", "ever": "Toujours", "nextMonth": "Le mois prochain", "currentFiscalYear": "Année fiscale en cours", "lastFiscalYear": "<PERSON><PERSON> exercice financier", "currentFiscalQuarter": "Trimestre financier en cours", "lastFiscalQuarter": "Dernier trimestre fiscal"}, "searchRanges": {"is": "Est", "isEmpty": "Est vide", "isNotEmpty": "Non vide", "isFromTeams": "Provient de l’équipe", "allOf": "<PERSON><PERSON> de", "any": "<PERSON><PERSON><PERSON>"}, "varcharSearchRanges": {"equals": "<PERSON><PERSON><PERSON>", "like": "Correspond à (%)", "startsWith": "Commence par", "endsWith": "Fini par", "contains": "Contient", "isEmpty": "Est vide", "isNotEmpty": "Non vide", "notLike": "N'est pas (%)", "notContains": "Ne contient pas", "notEquals": "N'est pas égale"}, "intSearchRanges": {"equals": "<PERSON>gal<PERSON>", "notEquals": "N'est pas égale", "greaterThan": "Plus grand que", "lessThan": "Moins grand que", "greaterThanOrEquals": "Plus grand que ou égale", "lessThanOrEquals": "Moins grand que ou égale", "between": "<PERSON><PERSON>"}, "autorefreshInterval": {"0": "Aucun", "0.5": "30 secondes"}, "phoneNumber": {"Office": "Bureau", "Home": "<PERSON><PERSON>", "Other": "<PERSON><PERSON>"}}, "sets": {"summernote": {"NOTICE": "V<PERSON> pouvez trouver des traductions ici: https://github.com/HackerWins/summernote/tree/master/lang", "font": {"bold": "Gras", "italic": "Italique", "underline": "Soulignement", "strike": "<PERSON><PERSON>", "clear": "Supprimer le style d'écriture", "height": "<PERSON><PERSON> <PERSON> ligne", "name": "Police de caractère", "size": "Taille de police"}, "image": {"image": "Image", "insert": "Insérer une Image", "resizeFull": "Redimensionner 100%", "resizeHalf": "Redimensionner de moitié", "resizeQuarter": "Redimensionner de quart", "floatLeft": "Marge à gauche", "floatRight": "Marge à droite", "floatNone": "<PERSON><PERSON> de marge", "dragImageHere": "Glissez une image ici", "selectFromFiles": "Sélectionner depuis les fichiers", "url": "URL de l'image", "remove": "Supprimer l'Image"}, "link": {"link": "<PERSON><PERSON>", "insert": "Insérer un Lien", "unlink": "Supprimer le lien", "edit": "É<PERSON>er", "textToDisplay": "Texte à afficher", "url": "Vers quelle URL le lien doit-il pointer?", "openInNewWindow": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans une nouvelle fenêtre"}, "video": {"video": "Vidéo", "videoLink": "<PERSON>n vid<PERSON>o", "insert": "Insérer une Vidéo", "url": "URL de la vidéo ?", "providers": "(YouTube, Vimeo, Vine, Instagram, ou DailyMotion)"}, "table": {"table": "<PERSON><PERSON>"}, "hr": {"insert": "Insérer une règle horizontale"}, "style": {"blockquote": "Citation", "h1": "Titre 1", "h2": "Titre 2", "h3": "Titre 3", "h4": "Titre 4", "h5": "Titre 5", "h6": "Titre 6"}, "lists": {"unordered": "Liste non ordonnée", "ordered": "Liste ordonnée"}, "options": {"help": "Aide", "fullscreen": "Plein écran", "codeview": "Voir le code"}, "paragraph": {"paragraph": "Paragraphe", "outdent": "Diminuer le retrait", "indent": "Accentuer le retrait", "left": "<PERSON><PERSON><PERSON> à gauche", "center": "Aligner au centre", "right": "<PERSON><PERSON><PERSON> d<PERSON>", "justify": "Justifier"}, "color": {"recent": "<PERSON><PERSON><PERSON> ré<PERSON>e", "more": "Plus de couleurs", "background": "<PERSON><PERSON><PERSON> de fond", "foreground": "Couleur de la police", "setTransparent": "Rendre transparent", "reset": "Réinitialiser", "resetToDefault": "Remettre à zéro"}, "shortcut": {"shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "close": "<PERSON><PERSON><PERSON>", "textFormatting": "Formattage du texte", "paragraphFormatting": "Formattage du paragraphe", "documentStyle": "Style du document"}, "history": {"undo": "<PERSON>er en arrière", "redo": "<PERSON><PERSON> en avant"}}}, "durationUnits": {"d": "ré"}, "listViewModes": {"list": "liste"}, "fieldValidations": {"phoneNumber": "Numéro de téléphone valide"}}