{"scopeNames": {"Account": "<PERSON><PERSON><PERSON>", "Lead": "Prospect", "Target": "Cible", "Opportunity": "Opportunité", "Meeting": "<PERSON><PERSON><PERSON>vous", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON>", "Task": "<PERSON><PERSON><PERSON>", "Case": "Ticket", "DocumentFolder": "Dossier", "Campaign": "Campagne", "TargetList": "Liste de cibles", "MassEmail": "Emails groupé", "Activities": "Activités", "KnowledgeBaseArticle": "Article de la base de connaissance", "KnowledgeBaseCategory": "Catégorie de la base de connaissance"}, "scopeNamesPlural": {"Account": "<PERSON><PERSON><PERSON>", "Lead": "Prospects", "Target": "Cibles", "Opportunity": "Opportunités", "Meeting": "<PERSON><PERSON><PERSON>vous", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "<PERSON><PERSON><PERSON>", "Task": "Tâches", "Case": "Tickets", "DocumentFolder": "Dossiers", "Campaign": "Campagnes", "TargetList": "Listes de cibles", "MassEmail": "Emails groupés", "EmailQueueItem": "File d'attente", "Activities": "Activités", "KnowledgeBaseArticle": "Base de connaissance", "KnowledgeBaseCategory": "Catégories de la base de connaissance", "CampaignLogRecord": "Logs"}, "dashlets": {"Leads": "Mes prospects", "Opportunities": "Mes opportunités", "Tasks": "<PERSON><PERSON> t<PERSON>", "Cases": "Mes tickets", "Calendar": "<PERSON><PERSON><PERSON>", "Calls": "Mes appels", "Meetings": "Mes rendez-vous", "OpportunitiesByStage": "Opportunités par étape", "OpportunitiesByLeadSource": "Opportunités par source de prospects", "SalesByMonth": "Ventes par mois", "SalesPipeline": "Canaux de vente", "Activities": "Mes activités"}, "labels": {"Create InboundEmail": "<PERSON><PERSON><PERSON> un email entrant", "Activities": "Activités", "History": "Historique", "Attendees": "Participants", "Schedule Meeting": "Planifier un rendez-vous", "Schedule Call": "Programmer un appel", "Compose Email": "Composer un Email", "Log Meeting": "Rapporter un rendez-vous", "Log Call": "Rapporter un appel", "Archive Email": "Archiver l'Email", "Create Task": "<PERSON><PERSON><PERSON> une tâche", "Tasks": "Tâches", "Scheduler": "Emploi du temps"}, "fields": {"billingAddressCity": "Ville", "addressCity": "Ville", "billingAddressCountry": "Pays", "addressCountry": "Pays", "billingAddressPostalCode": "Code postal", "addressPostalCode": "Code postal", "billingAddressState": "Régions", "addressState": "Régions", "billingAddressStreet": "Rue", "addressStreet": "Rue", "billingAddressMap": "<PERSON><PERSON>", "addressMap": "<PERSON><PERSON>", "shippingAddressCity": "Ville (livraison)", "shippingAddressStreet": "Rue (livraison)", "shippingAddressCountry": "Pays (livraison)", "shippingAddressState": "Région (livraison)", "shippingAddressPostalCode": "Code Postal (livraison)", "shippingAddressMap": "Carte (Transport)"}, "links": {"opportunities": "Opportunités", "leads": "Prospects", "meetings": "<PERSON><PERSON><PERSON>vous", "calls": "<PERSON><PERSON><PERSON>", "tasks": "Tâches", "accounts": "<PERSON><PERSON><PERSON>", "cases": "Tickets", "account": "<PERSON><PERSON><PERSON>", "opportunity": "Opportunité"}, "streamMessages": {"eventConfirmationAcceptedThis": "{invitee} a accepté de participer", "eventConfirmationDeclinedThis": "{invitee} a refusé de participer", "eventConfirmationTentativeThis": "{invitee} est hésitant à participer"}}