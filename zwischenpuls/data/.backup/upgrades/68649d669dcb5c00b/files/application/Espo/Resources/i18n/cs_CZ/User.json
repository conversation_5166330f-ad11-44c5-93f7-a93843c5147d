{"fields": {"name": "Jméno", "userName": "Uživatelské jméno", "title": "Title", "isAdmin": "Je admin", "defaultTeam": "Výchozí tým", "emailAddress": "Email", "phoneNumber": "Telefon", "roles": "Role", "teamRole": "Pozice", "password": "He<PERSON><PERSON>", "passwordConfirm": "Potvrzen<PERSON> hesla", "newPassword": "<PERSON><PERSON>", "newPasswordConfirm": "Potvrzení nového hesla", "avatar": "Avatar", "isActive": "Je aktivní"}, "links": {"teams": "<PERSON><PERSON><PERSON>", "roles": "Role"}, "labels": {"Create User": "Vytvořit uživatele", "Generate": "Generovat", "Access": "Přístup", "Preferences": "Předvolby", "Change Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Teams and Access Control": "Týmy a kontrola přístupu", "Forgot Password?": "Zapomenut<PERSON> he<PERSON>lo?", "Password Change Request": "Požadavek na změnu hesla", "Email Address": "<PERSON><PERSON><PERSON> ad<PERSON>", "External Accounts": "<PERSON><PERSON><PERSON>", "Email Accounts": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltips": {"defaultTeam": "All records created by this user will be related to this team by default.", "userName": "Letters a-z, numbers 0-9 and underscores are allowed.", "isAdmin": "Admin user can access everything.", "isActive": "If unchecked then user won't be able to login.", "teams": "Teams which this user belongs to. Access control level is inherited from team's roles.", "roles": "Additional access roles. Use it if user doesn't belong to any team or you need to extend access control level only for this user."}, "messages": {"passwordWillBeSent": "<PERSON><PERSON><PERSON> bude posláno na emailovou adresu uživatele.", "passwordChanged": "<PERSON><PERSON><PERSON>", "userCantBeEmpty": "Uživatelské jméno nemůže být prázdné", "wrongUsernamePassword": "Nesprávné u<PERSON>é j<PERSON>/heslo", "emailAddressCantBeEmpty": "Emailová adresa nemůže zůstat prázdná", "userNameEmailAddressNotFound": "Uživatelské j<PERSON>/he<PERSON><PERSON>o", "forbidden": "Tato operace nen<PERSON>, pros<PERSON><PERSON> zkus<PERSON> to později", "uniqueLinkHasBeenSent": "Unikátní link byl poslán na zadanou emailovou adresu.", "passwordChangedByRequest": "<PERSON><PERSON><PERSON>lo <PERSON>."}, "boolFilters": {"onlyMyTeam": "<PERSON><PERSON> můj tým"}, "presetFilters": {"active": "Aktivní"}}