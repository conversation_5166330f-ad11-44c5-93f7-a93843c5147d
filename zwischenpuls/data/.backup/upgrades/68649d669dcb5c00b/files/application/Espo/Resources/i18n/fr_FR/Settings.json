{"fields": {"useCache": "Utiliser la mise en cache", "dateFormat": "Format de la date", "timeFormat": "Format horaire", "timeZone": "<PERSON><PERSON> ho<PERSON>", "weekStart": "Premier j<PERSON> de la semaine", "thousandSeparator": "Séparateur de millier", "decimalMark": "Symbole décimal", "defaultCurrency": "<PERSON><PERSON> par défaut", "baseCurrency": "Devise de <PERSON>", "currencyRates": "Taux des devises", "currencyList": "Liste des devises", "language": "<PERSON><PERSON>", "companyLogo": "Logo", "smtpServer": "Ser<PERSON><PERSON>", "smtpSecurity": "Sécurité", "ldapSecurity": "Sécurité", "smtpUsername": "Nom d'utilisateur", "smtpPassword": "Mot de passe", "ldapPassword": "Mot de passe", "outboundEmailFromName": "De", "outboundEmailFromAddress": "De<PERSON>is l'adresse", "outboundEmailIsShared": "Est partagé", "recordsPerPage": "Enregistrements par page", "recordsPerPageSmall": "Enregistrements par page (réduits)", "tabList": "Liste des onglets", "quickCreateList": "Liste des créations rapides", "exportDelimiter": "Délimitant en exportation", "globalSearchEntityList": "Liste des Fonctionnalités pour la Recherche globale", "authenticationMethod": "Méthode d'authentification", "ldapHost": "<PERSON><PERSON><PERSON>", "ldapAccountCanonicalForm": "Compte forme canonique", "ldapAccountDomainName": "Compte du nom de domaine", "ldapCreateEspoUser": "Créer un utilisateur dans EspoCRM", "ldapUserLoginFilter": "Filtre des connexions utilisateur", "ldapAccountDomainNameShort": "Compte du nom de domaine réduit", "ldapOptReferrals": "Abonnés <PERSON>", "exportDisabled": "Désactiver l'Export (seul l'administrateur pourra le faire)", "b2cMode": "Mode B2C", "avatarsDisabled": "Désactiver les avatars", "displayListViewRecordCount": "Afficher le Nombre Total (listes)", "theme": "Thème", "userThemesDisabled": "Désactiver les thèmes utilisateur", "emailMessageMaxSize": "<PERSON>lle maximale d'Emails (Mo)", "siteUrl": "URL du site", "assignmentNotificationsEntityList": "Fonctionnalités à notifier lors de l'attribution", "calendarEntityList": "<PERSON>e Cale<PERSON>rier", "activitiesEntityList": "Liste Activités", "historyEntityList": "Liste Historique", "adminNotifications": "Notifications du système dans le volet Administration", "adminNotificationsNewVersion": "Afficher la notification lorsque la nouvelle version d'EspoCRM est disponible", "massEmailMaxPerHourCount": "Nombre maximum d'Emails par heure", "maxEmailAccountCount": "Nombre maximum de comptes Emails personnels par utilisateur", "streamEmailNotificationsTypeList": "Ce qu'il faut signaler", "authTokenPreventConcurrent": "Un seul jeton d'authentification par utilisateur", "scopeColorsDisabled": "Désactiver les couleurs de la portée", "tabColorsDisabled": "Désactiver les couleurs des onglets", "tabIconsDisabled": "Désactiver les icônes d'onglets", "textFilterUseContainsForVarchar": "Utilisez l'opérateur 'contient' lors du filtrage des champs varchar", "emailAddressIsOptedOutByDefault": "Marquer les nouvelles adresses e-mail comme désactivées", "outboundEmailBccAddress": "Adresse BCC pour les clients externes", "adminNotificationsNewExtensionVersion": "Afficher la notification lorsque de nouvelles versions d'extensions sont disponibles", "cleanupDeletedRecords": "Nettoyer les enregistrements supprimés", "ldapPortalUserLdapAuth": "Utiliser l'authentification LDAP pour les utilisateurs du portail", "ldapPortalUserPortals": "Portails par défaut pour un utilisateur du portail", "ldapPortalUserRoles": "Rôles par défaut pour un utilisateur du portail", "addressCountryList": "Adresse Pays Liste de saisie semi-automatique", "fiscalYearShift": "Année fiscale début", "jobRunInParallel": "Travaux exécutés en parallèle", "jobMaxPortion": "Emplois Portion Max", "jobPoolConcurrencyNumber": "Numéro d'accès simultané au pool d'emplois", "daemonInterval": "Intervalle de démon", "daemonMaxProcessNumber": "Nombre de processus Daemon Max", "daemonProcessTimeout": "<PERSON><PERSON><PERSON> traitement du démon", "addressCityList": "Adresse Ville Liste de saisie semi-automatique", "addressStateList": "Liste de complétion automatique d'état d'adresse", "cronDisabled": "<PERSON><PERSON><PERSON><PERSON>", "maintenanceMode": "Mode de Maintenance", "useWebSocket": "Utiliser WebSocket", "emailNotificationsDelay": "<PERSON><PERSON>lai de notification par courrier électronique (en secondes)", "massEmailOpenTracking": "Suivi des e-mails ouverts", "passwordRecoveryDisabled": "Désactiver la récupération du mot de passe", "passwordRecoveryForAdminDisabled": "Désactiver la récupération de mot de passe pour les utilisateurs administrateurs", "passwordGenerateLength": "Longueur des mots de passe générés", "passwordStrengthLength": "Longueur minimale du mot de passe", "passwordStrengthLetterCount": "Nombre de lettres requises dans le mot de passe", "passwordStrengthNumberCount": "Nombre de chiffres requis dans le mot de passe", "passwordStrengthBothCases": "Le mot de passe doit contenir des lettres en majuscule et en minuscule", "auth2FA": "Activer l'authentification à 2 facteurs", "auth2FAMethodList": "Méthodes 2FA disponibles", "personNameFormat": "Format du nom", "newNotificationCountInTitle": "Afficher le nombre de nouvelles notifications dans le titre de la page web", "massEmailVerp": "Utiliser VERP", "busyRangesEntityList": "Liste de Fonctionnalités libres/inactives", "passwordRecoveryForInternalUsersDisabled": "Désactiver la récupération de mot de passe pour les utilisateurs internes", "passwordRecoveryNoExposure": "Évi<PERSON><PERSON> d'afficher l'adresse courriel sur le formulaire de récupération de mot de passe", "auth2FAForced": "Forcer les utilisateurs réguliers à utiliser l'authentification à deux facteurs"}, "tooltips": {"recordsPerPageSmall": "Nombre d'enregistrements dans les volets Relations.", "followCreatedEntities": "Les utilisateurs suivront automatiquement leurs entrées", "userThemesDisabled": "Si coché alors les utilisateurs ne pourront pas sélectionner un autre thème", "textFilterUseContainsForVarchar": "Si cette case n'est pas cochée, l'opérateur \"commence par\" est utilisé. Vous pouvez utiliser le caractère générique '%'.", "streamEmailNotificationsEntityList": "Notifications par courriel des mises à jour de flux des éléments suivis. Uniquement pour les Fonctionnalités spécifiées.", "authTokenPreventConcurrent": "Les utilisateurs ne pourront pas se connecter simultanément sur plusieurs appareils.", "emailAddressIsOptedOutByDefault": "Lors de la création d'un nouvel enregistrement, l'adresse e-mail sera marquée comme désactivée.", "cleanupDeletedRecords": "Les enregistrements supprimés seront supprimés de la base de données après un certain temps.", "ldapPortalUserLdapAuth": "Autoriser les utilisateurs du portail à utiliser l'authentification LDAP au lieu de l'authentification Espo.", "ldapPortalUserPortals": "Portails par défaut pour l'utilisateur de portail créé", "ldapPortalUserRoles": "Rôles par défaut pour l'utilisateur de portail créé", "jobRunInParallel": "Les travaux seront exécutés dans des processus parallèles.", "jobPoolConcurrencyNumber": "Nombre maximal de processus exécutés simultanément.", "jobMaxPortion": "Nombre maximal de travaux traités par exécution.", "daemonInterval": "L'intervalle entre les processus cron s'exécute en secondes.", "daemonMaxProcessNumber": "Nombre maximal de processus cron exécutés simultanément.", "daemonProcessTimeout": "Temps d'exécution maximal (en secondes) alloué pour un seul processus cron.", "cronDisabled": "<PERSON><PERSON> ne courra pas.", "maintenanceMode": "Seuls les administrateurs auront accès au système.", "ldapAccountCanonicalForm": "Le type de votre compte forme canonique. Il y a 4 options: \n\n- 'Dn' - le formulaire au format 'CN = testeur, OU = espocr, DC = test, DC = lan'. \n\n- 'Nom d'utilisateur' - le formulaire 'testeur '. \n\n-' Barre oblique inverse '- le formulaire' ENTREPRISE \\ testeur '. \n\n-' Principal '- le formulaire' <EMAIL> '.", "displayListViewRecordCount": "Le nombre total d'éléments sera affiché dans les affichages en Liste.", "currencyList": "Devises disponibles dans le système.", "activitiesEntityList": "Éléments disponibles dans le volet Activités.", "historyEntityList": "Éléments disponibles dans le volet Historique.", "calendarEntityList": "Éléments disponibles dans le Calendrier.", "addressStateList": "Suggestions de Régions pour les champs d'adresse.", "addressCityList": "Suggestions de Villes pour les champs d'adresse. ", "addressCountryList": "Suggestions de Pays pour les champs d'adresse. ", "exportDisabled": "Les utilisateurs ne pourront pas faire d'export. L'export sera réservé aux administrateurs.", "globalSearchEntityList": "Éléments trouvables grâce à la Recherche globale.", "siteUrl": "Lien vers cette installation d'EspoCRM. Vous devrez le modifier si vous souhaitez migrer vers un autre domaine.", "useCache": "Gardez cette option activée, sauf pour des motifs de développement.", "passwordRecoveryForInternalUsersDisabled": "Seuls les utilisateurs Portail pourront récupérer leur mot de passe.", "passwordRecoveryNoExposure": "Il ne sera pas possible de déterminer si une adresse courriel spécifique est enregistrée dans le système."}, "labels": {"System": "Système", "Locale": "Local", "In-app Notifications": "Notifications internes", "Email Notifications": "Notifications par email", "Currency Settings": "Paramètres de devises", "Currency Rates": "Taux des devises", "Mass Email": "Emails groupés", "Admin Notifications": "Notifications de l'administrateur", "Search": "<PERSON><PERSON><PERSON>", "Passwords": "Mots de passe", "2-Factor Authentication": "Authentification à 2 facteurs", "Group Tab": "Onglet de Groupe"}, "options": {"streamEmailNotificationsTypeList": {"Post": "<PERSON>", "Status": "Mises à jour de statut", "EmailReceived": "<PERSON><PERSON><PERSON><PERSON>"}, "personNameFormat": {"firstLast": "Prénom Nom", "lastFirst": "Nom Prénom", "firstMiddleLast": "Prénom Second Prénom Nom", "lastFirstMiddle": "Nom Prénom Second Prénom"}, "currencyFormat": {"3": "10 €"}}}