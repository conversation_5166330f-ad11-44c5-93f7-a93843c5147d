{"labels": {"Fields": "Pole", "Relationships": "Vztahy"}, "fields": {"name": "Jméno", "type": "<PERSON><PERSON>", "labelSingular": "<PERSON><PERSON>k j<PERSON>notn<PERSON><PERSON>", "labelPlural": "Popisek množného č<PERSON>", "stream": "Stream", "label": "<PERSON><PERSON><PERSON>", "linkType": "<PERSON>p linku", "entityForeign": "Cizí entita", "linkForeign": "Cizí link", "link": "Link", "labelForeign": "Cizí popisek", "sortBy": "Výchozí <PERSON> (pole)", "sortDirection": "Výchozí <PERSON> (směr)"}, "options": {"type": {"": "", "Base": "<PERSON><PERSON><PERSON>", "Person": "Osoba", "CategoryTree": "Strom kategorií"}, "linkType": {"manyToMany": "N-N", "oneToMany": "1-N", "manyToOne": "N-1", "parentToChildren": "Rodič-Potomek", "childrenToParent": "Potomek-<PERSON>ič"}, "sortDirection": {"asc": "Vzestupně", "desc": "Sestupně"}}, "messages": {"entityCreated": "<PERSON>tita by<PERSON>", "linkAlreadyExists": "Konflikt: link již <PERSON>."}}