{"fields": {"from": "De", "to": "A", "subject": "Objet", "bodyContains": "Corps de texte contient"}, "labels": {"Create EmailFilter": "<PERSON><PERSON>er un filtre email"}, "tooltips": {"from": "Emails envoyés de cette adresse. Laisser vide si inutile. Vous pouvez utiliser des caractères de substitution: *", "to": "Emails envoyés de cette adresse. Laisser vide si inutile. Vous pouvez utiliser des caractères de substitution: *", "name": "Juste un nom de filtre", "bodyContains": "Le corps de l'email contient l'un des mots ou phrases", "subject": "* `texte*` – commence par texte,\n* `*texte*` – contient texte,\n* `*texte` – finit par texte."}}