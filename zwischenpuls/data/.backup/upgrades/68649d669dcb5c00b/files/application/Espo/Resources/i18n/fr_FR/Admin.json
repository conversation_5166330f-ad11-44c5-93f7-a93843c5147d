{"labels": {"Enabled": "Actif", "Disabled": "Inactif", "System": "Système", "Users": "Utilisateurs", "Email": "<PERSON><PERSON><PERSON>", "Data": "<PERSON><PERSON><PERSON>", "Customization": "Personnalisation", "Available Fields": "Champs disponibles", "Layout": "Agencements des champs", "Entity Manager": "Gestionnaire de Fonctionnalités", "Add Panel": "Ajouter un volet", "Add Field": "Ajouter un champ", "Settings": "Paramètres", "Scheduled Jobs": "Tâches planifiées", "Upgrade": "Mettre à jour", "Clear Cache": "Vider le cache", "Rebuild": "Reconstruire", "Teams": "Équipes", "Roles": "<PERSON><PERSON><PERSON>", "Portal": "Portail", "Portals": "Portails", "Portal Roles": "<PERSON><PERSON><PERSON>", "Outbound Emails": "Emails sortants", "Group Email Accounts": "Comptes email communs", "Personal Email Accounts": "Comptes email personnels", "Inbound Emails": "Emails entrants", "Email Templates": "Mod<PERSON><PERSON> d'email", "Layout Manager": "Gestionnaire de modèles", "User Interface": "Interface utilisateur", "Auth Tokens": "Jetons d'authentification", "Authentication": "Authentification", "Currency": "<PERSON><PERSON>", "Integrations": "Intégrations", "Upload": "Mettre en ligne", "Installing...": "Installation...", "Upgrading...": "Mise à jour...", "Upgraded successfully": "Mise à jour réussie", "Installed successfully": "Installé avec succès", "Ready for upgrade": "<PERSON>r<PERSON><PERSON> pour la mise à jour", "Run Upgrade": "<PERSON><PERSON><PERSON><PERSON> la mise à jour", "Install": "Installer", "Ready for installation": "<PERSON>r<PERSON><PERSON> pour l'installation", "Uninstalling...": "Désinstallation...", "Uninstalled": "Désinstallé", "Create Entity": "Créer une Fonctionnalité", "Edit Entity": "Modifier une Fonctionnalités", "Create Link": "<PERSON><PERSON>er un lien", "Edit Link": "Éditer un lien", "Jobs": "<PERSON><PERSON><PERSON><PERSON>", "Reset to Default": "Valeurs par défaut", "Email Filters": "Filtres email", "Auth Log": "Journal d'authentification", "Lead Capture": "Capture de plomb", "Attachments": "Les pièces jointes", "API Users": "Utilisateurs d'API", "Template Manager": "Gestionnaire de modèles", "System Requirements": "Configuration requise", "PHP Settings": "Paramètres PHP", "Database Settings": "Paramètres de la base de données", "Permissions": "Les permissions", "Success": "Su<PERSON>ès", "Fail": "<PERSON><PERSON><PERSON>", "is recommended": "est recommandé", "extension is missing": "l'extension est manquante", "PDF Templates": "<PERSON>d<PERSON><PERSON> PDF", "Dashboard Templates": "<PERSON><PERSON><PERSON><PERSON> tableau de bord", "Email Addresses": "Adresses email", "Phone Numbers": "Numéros de téléphone", "Layout Sets": "Modèles de Disposition d'écrans"}, "layouts": {"list": "Liste", "detail": "Détail", "listSmall": "Liste (réduite)", "detailSmall": "Détail (réduit)", "filters": "Filtres de recherche", "massUpdate": "Mise à jour groupée", "relationships": "Volets de Relations", "sidePanelsDetail": "Volets latéraux (Detail)", "sidePanelsEdit": "Volets latéraux (Edit)", "sidePanelsDetailSmall": "Volets latéraux (Detail Small)", "sidePanelsEditSmall": "<PERSON>ets latéraux (Edit Small)", "detailPortal": "<PERSON><PERSON><PERSON> (portail)", "detailSmallPortal": "Détail (petit, portail)", "listSmallPortal": "Liste (petit, portail)", "listPortal": "Liste (portail)", "relationshipsPortal": "Volets de Relations (portail)", "defaultSidePanel": "Champs du volet latéral", "bottomPanelsDetail": "Volets inférieurs", "bottomPanelsEdit": "Volets inférieurs (modification)", "bottomPanelsDetailSmall": "Volets inférieurs (détails réduits)", "bottomPanelsEditSmall": "Volets inférieurs (modification réduite)"}, "fieldTypes": {"address": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON>", "foreign": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "password": "Mot de passe", "personName": "Nom", "autoincrement": "Auto-incrément", "bool": "Case à cocher", "currency": "<PERSON><PERSON>", "enum": "Énumération", "linkMultiple": "Lien multiple", "linkParent": "Lien parent", "phone": "Téléphone", "text": "Texte", "varchar": "Phrase", "file": "<PERSON><PERSON><PERSON>", "attachmentMultiple": "Pièces jointes multiples", "rangeInt": "Plage d'entiers", "rangeFloat": "Intervalle réel", "rangeCurrency": "Plage de devises", "map": "<PERSON><PERSON>", "int": "Int", "number": "Number", "jsonObject": "Ob<PERSON>", "datetime": "Date-heure", "datetimeOptional": "Date / Date-<PERSON><PERSON>", "checklist": "Liste de contrôle", "linkOne": "<PERSON><PERSON>", "barcode": "Code barre"}, "fields": {"name": "Nom", "label": "Libellé", "required": "Requis", "default": "<PERSON><PERSON> <PERSON><PERSON>", "maxLength": "Longueur max", "after": "<PERSON><PERSON> (le champ)", "before": "Avant (le champ)", "link": "<PERSON><PERSON>", "field": "<PERSON><PERSON>", "translation": "Traduction", "previewSize": "Prévisualiser la taille", "defaultType": "Type par défaut", "seeMoreDisabled": "Désactiver l'abréviation de texte", "entityList": "Liste des Fonctionnalités", "isSorted": "Trié (alphabétique)", "audited": "Audité", "trim": "<PERSON><PERSON><PERSON>", "height": "<PERSON><PERSON> (px)", "minHeight": "Hauteur min. (px)", "provider": "Fournisseur", "typeList": "Liste de types", "lengthOfCut": "Longueur de coupe", "sourceList": "Liste des sources", "maxFileSize": "<PERSON>lle maximale du fi<PERSON> (Mo)", "isPersonalData": "Est-ce que des données personnelles", "useIframe": "<PERSON><PERSON><PERSON><PERSON>", "useNumericFormat": "Utiliser le format numérique", "strip": "Bande", "cutHeight": "Hauteur de coupe (px)", "inlineEditDisabled": "Désactiver la modification en ligne", "displayAsLabel": "Afficher comme étiquette", "allowCustomOptions": "Autoriser les options personnalisées", "maxCount": "Nombre d'éléments maximum", "displayRawText": "Affiche<PERSON> le texte brut (pas de démarques)", "notActualOptions": "Options non réelles", "accept": "<PERSON><PERSON><PERSON><PERSON>", "displayAsList": "Afficher en Liste", "viewMap": "Bouton Voir la carte", "codeType": "Code", "lastChar": "<PERSON><PERSON>", "copyToClipboard": "Bouton copier dans le presse-papiers"}, "messages": {"selectEntityType": "Sélectionner le type de Fonctionnalité dans le menu de gauche.", "selectUpgradePackage": "Sélectionner le pack de mise à jour", "selectLayout": "Sélectionnez le modèle dans le menu de gauche et modifiez-le.", "selectExtensionPackage": "Sélectionner un pack d'extension", "extensionInstalled": "L'extension {name} {version} a été installé.", "installExtension": "L'extension {name} {version} est prête pour l'installation.", "uninstallConfirmation": "Êtes-vous sûr de vouloir désinstaller l'extension?", "cronIsNotConfigured": "Les travaux planifiés ne sont pas en cours d'exécution. Par conséquent, les courriels entrants, les notifications et les rappels ne fonctionnent pas. Suivez les [instructions](https://www.espocrm.com/documentation/administration/server-configuration/#user-content-setup-a-crontab) pour configurer le travail cron.", "newExtensionVersionIsAvailable": "La nouvelle version de {extensionName} {latestVersion} est disponible.", "upgradeVersion": "EspoCRM sera mis à niveau vers la version ** {version} **. S'il vous plaît soyez patient car cela peut prendre un certain temps.", "upgradeDone": "EspoCRM a été mis à niveau vers la version ** {version} **.", "downloadUpgradePackage": "Téléchargez le (s) package (s) de mise à niveau [ici]({url}).", "upgradeInfo": "Consultez la [documentation]({url}) sur la mise à niveau de votre instance EspoCRM.", "upgradeRecommendation": "Cette méthode de mise à niveau n'est pas recommandée. Il est préférable de mettre à niveau à partir de CLI.", "newVersionIsAvailable": "Nouvelle version de EspoCRM {latestVersion} disponible. Veuillez suivre les [instructions](https://www.espocrm.com/documentation/administration/upgrading/) pour mettre à jour votre instance."}, "descriptions": {"settings": "Paramètres système de l'application.", "scheduledJob": "Tâches qui sont exécutées par Cron.", "upgrade": "Mettre à jour le système.", "clearCache": "Vider tout le cache.", "rebuild": "Reconstruire et vider tout le cache.", "users": "Gestion des utilisateurs.", "teams": "Gestion des équipes.", "roles": "Gestion des rôles.", "portals": "Gestion des portails", "portalRoles": "Rôles du portail", "outboundEmails": "Paramètres SMTP pour les emails sortants.", "groupEmailAccounts": "Comptes email IMAP communs", "personalEmailAccounts": "Comptes email utilisateurs", "emailTemplates": "Modèles pour les emails sortants.", "import": "Importer les données d'un fichier CSV.", "layoutManager": "Personnalisation des interfaces (listes, détails, édition, recherche, mise à jour groupée).", "userInterface": "Configurer l'interface utilisateur.", "authTokens": "Auth sessions actives. Adresse IP et date du dernier accès.", "authentication": "Paramètres d'authentification.", "currency": "Réglages des devises et des taux.", "extensions": "Installer ou désinstaller des extensions.", "integrations": "Intégration avec des services tiers.", "notifications": "Paramètres des notifications emails et internes au CRM.", "inboundEmails": "Group IMAP email accouts. Email import and Email-to-Case.", "entityManager": "Créer et modifier les Fonctionnalités personnalisées. Organiser les champs et leurs relations.", "authLog": "Historique de connexion.", "leadCapture": "Points d'entrée API pour Web-to-Lead.", "attachments": "Toutes les pièces jointes stockées dans le système.", "templateManager": "Personnaliser les modèles de message.", "systemRequirements": "Configuration système requise pour EspoCRM.", "apiUsers": "<PERSON><PERSON><PERSON>er les utilisateurs à des fins d'intégration.", "jobs": "Les tâches exécutent des tâches en arrière-plan.", "pdfTemplates": "Modèles pour l'impression au format PDF.", "webhooks": "<PERSON><PERSON><PERSON> les webhooks.", "dashboardTemplates": "Déployer des tableaux de bord pour les utilisateurs.", "phoneNumbers": "Tous les numéros de téléphone stocké dans le système."}, "options": {"previewSize": {"x-small": "<PERSON><PERSON><PERSON> petit", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "": "Défaut"}}, "systemRequirements": {"requiredPhpVersion": "Version PHP", "requiredMysqlVersion": "Version MySQL", "host": "Nom d'hôte", "dbname": "Nom de la base de données", "user": "Nom d'utilisateur", "writable": "Enregistrable", "readable": "Lisible", "requiredMariadbVersion": "Version de MariaDB"}, "templates": {"accessInfo": "Informations d'accès", "accessInfoPortal": "Informations d'accès pour les portails", "assignment": "Affectation", "notePost": "Note sur le post", "notePostNoParent": "Note sur Post (pas de parent)", "noteStatus": "Note sur la mise à jour du statut", "passwordChangeLink": "Lien de changement de mot de passe", "noteEmailReceived": "Note à propos de l'Email reçu"}, "keywords": {"entityManager": "champs,relations,liens", "jobs": "<PERSON><PERSON>"}}