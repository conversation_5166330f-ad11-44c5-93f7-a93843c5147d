{"labels": {"Fields": "<PERSON><PERSON>", "Relationships": "Relations", "Schedule": "Emploi du temps", "Formula": "Formule", "Layouts": "Dispositions d'écran"}, "fields": {"name": "Nom", "labelSingular": "Libellé au singulier", "labelPlural": "Libellé au pluriel", "stream": "Flux", "label": "Libellé", "linkType": "Type de lien", "entityForeign": "Fonctionnalité externe", "linkForeign": "Lien externe", "link": "<PERSON><PERSON>", "labelForeign": "Libellé externe", "sortBy": "Tri par défaut (champ)", "sortDirection": "Tri par d<PERSON><PERSON> (direction)", "relationName": "Nom de table intermédiaire", "linkMultipleField": "Lier un champ multiple", "linkMultipleFieldForeign": "Lien externe vers un champ multiple", "disabled": "Désactivé", "textFilterFields": "Champs de filtre textuel", "audited": "<PERSON><PERSON><PERSON><PERSON>", "statusField": "Champ d'État", "color": "<PERSON><PERSON><PERSON>", "kanbanViewMode": "<PERSON><PERSON>", "kanbanStatusIgnoreList": "Groupes ignorés dans la vue Kanban", "iconClass": "Icône", "fullTextSearch": "Recherche du texte complet", "countDisabled": "<PERSON><PERSON><PERSON><PERSON> le compteur de lignes", "parentEntityTypeList": "Types de Fonctionnalités parentes", "foreignLinkEntityTypeList": "Liens externes"}, "options": {"type": {"": "Aucun", "Person": "Individu", "CategoryTree": "Arborescence des catégories", "Event": "<PERSON><PERSON><PERSON><PERSON>", "Company": "Structure"}, "linkType": {"manyToMany": "Plusieurs-à-Plusieurs", "oneToMany": "Un-à-Plusieurs", "manyToOne": "Plusieurs-à-Un", "parentToChildren": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenToParent": "<PERSON><PERSON>-<PERSON><PERSON>", "oneToOneRight": "Un-à-<PERSON> Droit<PERSON>", "oneToOneLeft": "Un-à-Un Gauche"}, "sortDirection": {"asc": "Croissant", "desc": "Décroissant"}}, "messages": {"entityCreated": "La Fonctionnalités a bien été créée", "linkAlreadyExists": "Conflit : lien déjà existant.", "linkConflict": "Conflit de nom : un lien ou un champ portant ce nom existe déjà.", "confirmRemove": "Êtes-vous sûr de vouloir supprimer ce type de Fonctionnalité du système ?"}, "tooltips": {"statusField": "Les mises à jour de ce champ sont inscrites dans le Flux.", "textFilterFields": "Champs utilisés lors de recherches textuelles.", "stream": "Quand la Fonctionnalité a un Flux.", "disabled": "Vérifiez que vous n'ayez pas besoin de cette Fonctionnalité dans votre système.", "linkMultipleField": "Le champ de Liens multiples est très utile à la création de relations. Mais ne l'utilisez pas si un trop grand nombre d'éléments sont reliés.", "entityType": "Base Plus - ajoute les volets Activités, Historique et Tâches.\n\nÉvènement - est disponible dans les volets Calendrier and Activités.", "fullTextSearch": "Une reconstruction est requise.", "countDisabled": "Le nombre total ne sera pas affiché en vue Liste. Cela peut faire baisser les temps de chargement quand la base de données est grande."}}