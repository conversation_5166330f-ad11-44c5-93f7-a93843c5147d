{"fields": {"status": "Statut", "dateSent": "Date d'envoi", "from": "De", "to": "À", "replyTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replyToString": "<PERSON><PERSON><PERSON><PERSON><PERSON> (texte)", "body": "Corps", "subject": "Objet", "attachments": "Pièces-jointes", "selectTemplate": "Sélectionner un modèle", "fromAddress": "De<PERSON>is l'adresse", "emailAddress": "<PERSON><PERSON><PERSON> email", "deliveryDate": "Date de remise", "account": "<PERSON><PERSON><PERSON>", "users": "Utilisateurs", "replied": "Répondu", "replies": "Réponses", "isRead": "<PERSON>", "isNotRead": "Non lu", "isImportant": "Important", "isUsers": "Utilisateurs", "inTrash": "Supprimé", "bodyPlain": "Corps (plaine)", "ccEmailAddresses": "Adresses mail CC", "messageId": "ID du message", "messageIdInternal": "Identifiant du message (interne)", "folderId": "ID de dossier", "fromName": "De nom", "fromString": "De la corde", "isSystem": "Est système", "toEmailAddresses": "À EmailAddresses", "replyToEmailAddresses": "Répondre à EmailAddresses", "personStringData": "<PERSON>n<PERSON> de chaîne de personne", "fromEmailAddress": "De l'adresse (lien)", "replyToName": "Nom de réponse", "replyToAddress": "Ré<PERSON>ndre à l'adresse"}, "links": {"replied": "Répondu", "replies": "Réponses", "attachments": "Les pièces jointes", "fromEmailAddress": "De l'adresse e-mail", "toEmailAddresses": "À EmailAddresses", "replyToEmailAddresses": "Répondre à EmailAddresses"}, "options": {"status": {"Draft": "Brouillon", "Sending": "En cours d'envoi", "Sent": "<PERSON><PERSON><PERSON>", "Archived": "Archivé", "Received": "<PERSON><PERSON><PERSON>", "Failed": "Echec"}}, "labels": {"Create Email": "Archiver l'Email", "Archive Email": "Archiver l'Email", "Compose": "Composer", "Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reply to All": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous", "Forward": "<PERSON><PERSON><PERSON><PERSON>", "Original message": "Message d'origine", "Forwarded message": "Message transféré", "Email Accounts": "Comptes emails", "Inbound Emails": "Comptes email communs", "Email Templates": "Mod<PERSON><PERSON> d'email", "Send Test Email": "Envoyer un email test", "Send": "Envoyer", "Email Address": "<PERSON><PERSON><PERSON> email", "Mark Read": "Marquer comme lu", "Sending...": "Envoi en cours...", "Save Draft": "Sauvegarder le brouillon", "Mark all as read": "<PERSON><PERSON> tout comme lu", "Show Plain Text": "Afficher texte brut", "Mark as Important": "<PERSON>quer comme important", "Unmark Importance": "Marquer comme non important", "Move to Trash": "Mettre à la corbeille", "Retrieve from Trash": "<PERSON><PERSON><PERSON> <PERSON> la corbeille", "View Users": "Afficher les utilisateurs", "No Subject": "Aucun Objet", "Insert Field": "Insère un champ"}, "messages": {"testEmailSent": "L'email de test a été envoyé", "emailSent": "L'email a été envoyé", "savedAsDraft": "Sauvegardé en tant que brouillon", "confirmInsertTemplate": "Le corps de l'e-mail sera perdu. Êtes-vous sûr de vouloir insérer le modèle?", "noSmtpSetup": "SMTP n'est pas configuré: {link}", "sendConfirm": "Envoyer l'email ?", "removeSelectedRecordsConfirmation": "Êtes-vous sûr de vouloir supprimer les courriels sélectionnés ?\n\nIls seront supprimés pour tous les utilisateurs.", "removeRecordConfirmation": "Êtes-vous sûr de vouloir supprimer ce courriel ?\n\nIl sera supprimé pour tous les utilisateurs."}, "presetFilters": {"sent": "<PERSON><PERSON><PERSON>", "archived": "Archivé", "inbox": "Boite de r<PERSON>", "drafts": "Brouillons", "trash": "<PERSON><PERSON><PERSON><PERSON>"}, "massActions": {"markAsRead": "<PERSON>quer commer lu", "markAsNotRead": "Marquer comme non-lu", "markAsImportant": "<PERSON>quer comme important", "markAsNotImportant": "Marquer comme non important", "moveToTrash": "Mettre à la corbeille", "moveToFolder": "Deplacer vers", "retrieveFromTrash": "Récupérer de la corbeille"}, "strings": {"sendingFailed": "Échec d'envoi du courriel"}}