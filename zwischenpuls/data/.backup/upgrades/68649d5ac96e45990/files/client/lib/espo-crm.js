/*! espocrm 2025-05-29 */
define("modules/crm/views/meeting/fields/attendees",["exports","views/fields/link-multiple-with-role"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{columnName="status";roleFieldIsForeign=!1;emptyRoleValue="None"}e.default=s});define("modules/crm/views/calendar/fields/teams",["exports","views/fields/link-multiple"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{foreignScope="Team";getSelectBoolFilterList(){if("team"===this.getAcl().getPermissionLevel("userCalendar"))return["onlyMy"]}}e.default=s});define("modules/crm/knowledge-base-helper",["exports","ajax"],function(e,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;s=(t=s,t&&t.__esModule?t:{default:t});var t;e.default=class{constructor(e){this.language=e}getAttributesForEmail(e,t,i){t=t||{};t.body=e.get("body");t.name?t.name=t.name+" ":t.name="";t.name+=this.language.translate("KnowledgeBaseArticle","scopeNames")+": "+e.get("name");s.default.postRequest("KnowledgeBaseArticle/action/getCopiedAttachments",{id:e.id,parentType:"Email",field:"attachments"}).then(e=>{t.attachmentsIds=e.ids;t.attachmentsNames=e.names;t.isHtml=!0;i(t)})}}});define("modules/crm/views/task/record/list",["exports","views/record/list"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{rowActionsView="crm:views/task/record/row-actions/default";actionSetCompleted(e){var t=e.id;if(t){t=this.collection.get(t);if(t){Espo.Ui.notify(this.translate("saving","messages"));t.save({status:"Completed"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved"));this.collection.fetch()})}}}}e.default=s});define("modules/crm/views/record/panels/tasks",["exports","views/record/panels/relationship","helpers/record/create-related"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{name="tasks";entityType="Task";filterList=["all","actual","completed"];orderBy="createdAt";orderDirection="desc";rowActionsView="crm:views/record/row-actions/tasks";buttonList=[{action:"createTask",title:"Create Task",acl:"create",aclScope:"Task",html:'<span class="fas fa-plus"></span>'}];actionList=[{label:"View List",action:"viewRelatedList"}];listLayout={rows:[[{name:"name",link:!0}],[{name:"isOverdue"},{name:"assignedUser"},{name:"dateEnd",soft:!0},{name:"status"}]]};setup(){this.parentScope=this.model.entityType;this.link="tasks";this.panelName="tasksSide";this.defs.create=!0;"Account"===this.parentScope&&(this.link="tasksPrimary");this.url=this.model.entityType+"/"+this.model.id+"/"+this.link;this.setupSorting();this.filterList&&this.filterList.length&&(this.filter=this.getStoredFilter());this.setupFilterActions();this.setupTitle();this.wait(!0);this.getCollectionFactory().create("Task",e=>{this.collection=e;e.seeds=this.seeds;e.url=this.url;e.orderBy=this.defaultOrderBy;e.order=this.defaultOrder;e.maxSize=this.getConfig().get("recordsPerPageSmall")||5;this.setFilter(this.filter);this.wait(!1)});this.once("show",()=>{this.isRendered()||this.isBeingRendered()||this.collection.fetch()});let e=`update-related:${this.link} update-all`;"Account"===this.parentScope&&(e+=" update-related:tasks");this.listenTo(this.model,e,()=>this.collection.fetch())}afterRender(){this.createView("list","views/record/list-expanded",{selector:"> .list-container",pagination:!1,type:"listRelationship",rowActionsView:this.defs.rowActionsView||this.rowActionsView,checkboxes:!1,collection:this.collection,listLayout:this.listLayout,skipBuildRows:!0},e=>{e.getSelectAttributeList(e=>{e&&(this.collection.data.select=e.join(","));this.disabled?this.once("show",()=>this.collection.fetch()):this.collection.fetch()})})}actionCreateRelated(){this.actionCreateTask()}actionCreateTask(){let e=this.link;"Account"===this.parentScope&&(e="tasks");var t=new i.default(this);t.process(this.model,e)}actionComplete(e){var t=e.id;if(t){t=this.collection.get(t);t.save({status:"Completed"},{patch:!0}).then(()=>this.collection.fetch())}}actionViewRelatedList(e){e.viewOptions=e.viewOptions||{};e.viewOptions.massUnlinkDisabled=!0;super.actionViewRelatedList(e)}}e.default=a});define("modules/crm/views/record/panels/activities",["exports","views/record/panels/relationship","multi-collection","helpers/record-modal"],function(e,t,s,n){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);s=i(s);n=i(n);function i(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{name="activities";orderBy="dateStart";serviceName="Activities";order="desc";rowActionsView="crm:views/record/row-actions/activities";relatedListFiltersDisabled=!0;buttonMaxCount=null;actionList=[{action:"composeEmail",label:"Compose Email",acl:"create",aclScope:"Email"}];listLayout={};defaultListLayout={rows:[[{name:"ico",view:"crm:views/fields/ico"},{name:"name",link:!0,view:"views/event/fields/name-for-history"}],[{name:"dateStart",soft:!0},{name:"assignedUser"}]]};BUTTON_MAX_COUNT=3;setup(){this.scopeList=this.getConfig().get(this.name+"EntityList")||[];this.buttonMaxCount=this.getConfig().get("activitiesCreateButtonMaxCount");void 0===this.buttonMaxCount&&(this.buttonMaxCount=this.BUTTON_MAX_COUNT);this.listLayout=Espo.Utils.cloneDeep(this.listLayout);this.defs.create=!0;this.createAvailabilityHash={};this.entityTypeLinkMap={};this.createEntityTypeStatusMap={};this.setupActionList();this.setupFinalActionList();this.setupSorting();this.scopeList.forEach(e=>{e in this.listLayout||(this.listLayout[e]=this.defaultListLayout)});this.url=this.serviceName+"/"+this.model.entityType+"/"+this.model.id+"/"+this.name;this.seeds={};this.wait(!0);let i=0;this.scopeList.forEach(t=>{this.getModelFactory().create(t,e=>{this.seeds[t]=e;i++;i===this.scopeList.length&&this.wait(!1)})});0===this.scopeList.length&&this.wait(!1);this.filterList=[];this.filterList.length&&this.filterList.unshift("all");this.filterList&&this.filterList.length&&(this.filter=this.getStoredFilter());this.setupFilterActions();this.setupTitle();this.collection=new s.default;this.collection.seeds=this.seeds;this.collection.url=this.url;this.collection.orderBy=this.orderBy;this.collection.order=this.order;this.collection.maxSize=this.getConfig().get("recordsPerPageSmall")||5;let e="update-related:activities update-all";for(var t of this.scopeList){t=this.entityTypeLinkMap[t];t&&(e+=" update-related:"+t)}"history"===this.name&&(e+=" update-related:emails");this.listenTo(this.model,e,()=>this.collection.fetch());this.setFilter(this.filter);this.once("show",()=>{this.isRendered()||this.isBeingRendered()||this.collection.fetch()})}translateFilter(e){return"all"===e?this.translate(e,"presetFilters"):this.translate(e,"scopeNamesPlural")}isCreateAvailable(e){return this.createAvailabilityHash[e]}setupActionList(){"activities"===this.name&&this.buttonMaxCount&&this.buttonList.push({action:"composeEmail",title:"Compose Email",acl:"create",aclScope:"Email",html:$("<span>").addClass(this.getMetadata().get(["clientDefs","Email","iconClass"])).get(0).outerHTML});this.scopeList.forEach(e=>{if(this.getMetadata().get(["clientDefs",e,"activityDefs",this.name+"Create"])&&this.getAcl().checkScope(e,"create")){var t=("history"===this.name?"Log":"Schedule")+" "+e,i={action:"createActivity",text:this.translate(t,"labels",e),data:{},acl:"create",aclScope:e},s=this.getMetadata().get(["clientDefs",e,"activityDefs","link"]);if(s){i.data.link=s;this.entityTypeLinkMap[e]=s;if(!this.model.hasLink(s))return}else{i.data.scope=e;if("User"!==this.model.entityType&&!this.checkParentTypeAvailability(e,this.model.entityType))return}this.createAvailabilityHash[e]=!0;i.data=i.data||{};if(!i.data.status){s=this.getMetadata().get(["scopes",e,this.name+"StatusList"]);s&&s.length&&(i.data.status=s[0])}this.createEntityTypeStatusMap[e]=i.data.status;this.actionList.push(i);if("activities"===this.name&&this.buttonList.length<this.buttonMaxCount){s=Espo.Utils.cloneDeep(i),i=this.getMetadata().get(["clientDefs",e,"iconClass"]);if(i){s.title=t;s.html=$("<span>").addClass(i).get(0).outerHTML;this.buttonList.push(s)}}}})}setupFinalActionList(){this.scopeList.forEach((e,t)=>{0===t&&this.actionList.length&&this.actionList.push(!1);if(this.getAcl().checkScope(e,"read")){var i={action:"viewRelatedList",html:$("<span>").append($("<span>").text(this.translate(e,"scopeNamesPlural"))).get(0).innerHTML,data:{scope:e},acl:"read",aclScope:e};this.actionList.push(i)}})}setFilter(e){this.filter=e;this.collection.data.entityType=null;e&&"all"!==e&&(this.collection.data.entityType=this.filter)}afterRender(){let e=()=>{this.createView("list","views/record/list-expanded",{selector:"> .list-container",pagination:!1,type:"listRelationship",rowActionsView:this.rowActionsView,checkboxes:!1,collection:this.collection,listLayout:this.listLayout},e=>{e.render();this.listenTo(e,"after:save",()=>{this.model.trigger("update-related:activities")})})};this.disabled?this.once("show",()=>{this.collection.fetch().then(()=>e())}):this.collection.fetch().then(()=>e())}getCreateActivityAttributes(e,t,i){t=t||{};var s={status:t.status};if("User"===this.model.entityType){var a=this.model;if(a.isPortal()){s.usersIds=[a.id];var n={};n[a.id]=a.get("name");s.usersIdsNames=n}else{s.assignedUserId=a.id;s.assignedUserName=a.get("name")}}else{if("Contact"===this.model.entityType){if(this.model.get("accountId")&&!this.getConfig().get("b2cMode")){s.parentType="Account";s.parentId=this.model.get("accountId");s.parentName=this.model.get("accountName");if(e&&!this.getMetadata().get(["entityDefs",e,"links","contacts"])&&!this.getMetadata().get(["entityDefs",e,"links","contact"])){delete s.parentType;delete s.parentId;delete s.parentName}}}else if("Lead"===this.model.entityType){s.parentType="Lead";s.parentId=this.model.id;s.parentName=this.model.get("name")}if("Account"!==this.model.entityType&&this.model.has("contactsIds")){s.contactsIds=this.model.get("contactsIds");s.contactsNames=this.model.get("contactsNames")}if(e)if(s.parentId){if(s.parentType&&!this.checkParentTypeAvailability(e,s.parentType)){s.parentType=null;s.parentId=null;s.parentName=null}}else if(this.checkParentTypeAvailability(e,this.model.entityType)){s.parentType=this.model.entityType;s.parentId=this.model.id;s.parentName=this.model.get("name")}}i.call(this,Espo.Utils.cloneDeep(s))}checkParentTypeAvailability(e,t){return(this.getMetadata().get(["entityDefs",e,"fields","parent","entityList"])||[]).includes(t)}actionCreateRelated(e){e.link=this.entityTypeLinkMap[e.scope];this.createEntityTypeStatusMap[e.scope]&&(e.status=this.createEntityTypeStatusMap[e.scope]);this.actionCreateActivity(e)}actionCreateActivity(e){let i=e.link,s,a;if(i){a=this.model.getLinkParam(i,"entity");s=this.model.getLinkParam(i,"foreign")}else a=e.scope;Espo.Ui.notifyWait();this.getCreateActivityAttributes(a,e,e=>{var t=new n.default;t.showCreate(this,{entityType:a,relate:i?{model:this.model,link:s}:void 0,attributes:e,afterSave:()=>{this.model.trigger("update-related:"+i);this.model.trigger("after:relate")}})})}getComposeEmailAttributes(e,t,i){let s={status:"Draft",to:this.model.get("emailAddress")};if("Contact"===this.model.entityType){if(this.getConfig().get("b2cMode")){s.parentType="Contact";s.parentName=this.model.get("name");s.parentId=this.model.id}else if(this.model.get("accountId")){s.parentType="Account";s.parentId=this.model.get("accountId");s.parentName=this.model.get("accountName")}}else if("Lead"===this.model.entityType){s.parentType="Lead";s.parentId=this.model.id;s.parentName=this.model.get("name")}if(["Contact","Lead","Account"].includes(this.model.entityType)&&this.model.get("emailAddress")){s.nameHash={};s.nameHash[this.model.get("emailAddress")]=this.model.get("name")}if(e)if(s.parentId){if(s.parentType&&!this.checkParentTypeAvailability(e,s.parentType)){s.parentType=null;s.parentId=null;s.parentName=null}}else if(this.checkParentTypeAvailability(e,this.model.entityType)){s.parentType=this.model.entityType;s.parentId=this.model.id;s.parentName=this.model.get("name")}var a=this.getConfig().get("emailKeepParentTeamsEntityList")||[];if(s.parentType&&s.parentType===this.model.entityType&&a.includes(s.parentType)&&this.model.get("teamsIds")&&this.model.get("teamsIds").length){s.teamsIds=Espo.Utils.clone(this.model.get("teamsIds"));s.teamsNames=Espo.Utils.clone(this.model.get("teamsNames")||{});a=this.getUser().get("defaultTeamId");if(a&&!s.teamsIds.includes(a)){s.teamsIds.push(a);s.teamsNames[a]=this.getUser().get("defaultTeamName")}s.teamsIds=s.teamsIds.filter(e=>this.getAcl().checkTeamAssignmentPermission(e))}if(this.model.attributes.accountId&&"link"===this.model.getFieldType("account")&&"Account"===this.model.getLinkParam("account","entity")){s.accountId=this.model.attributes.accountId;s.accountName=this.model.attributes.accountName}if(!s.to&&this.isBasePlus()){Espo.Ui.notifyWait();Espo.Ajax.getRequest(`Activities/${this.model.entityType}/${this.model.id}/composeEmailAddressList`).then(e=>{if(e.length){s.to="";s.nameHash={};e.forEach(e=>{s.to+=e.emailAddress+";";s.nameHash[e.emailAddress]=e.name});Espo.Ui.notify(!1)}i.call(this,s)})}else i.call(this,s)}actionComposeEmail(e){let t=null;"emails"in this.model.defs.links&&(t={model:this.model,link:this.model.defs.links.emails.foreign});Espo.Ui.notifyWait();this.getComposeEmailAttributes("Email",e,e=>{this.createView("quickCreate","views/modals/compose-email",{relate:t,attributes:e},e=>{e.render();e.notify(!1);this.listenToOnce(e,"after:save",()=>{this.model.trigger("update-related:emails");this.model.trigger("after:relate")})})})}actionSetHeld(e){var t=e.id;if(t){t=this.collection.get(t);t.save({status:"Held"},{patch:!0}).then(()=>{this.model.trigger("update-related:activities")})}}actionSetNotHeld(e){var t=e.id;if(t){t=this.collection.get(t);t.save({status:"Not Held"},{patch:!0}).then(()=>{this.model.trigger("update-related:activities")})}}actionViewRelatedList(e){e.url=`Activities/${this.model.entityType}/${this.model.id}/${this.name}/list/`+e.scope;e.title=this.translate(this.defs.label)+" @right "+this.translate(e.scope,"scopeNamesPlural");var t=e.viewOptions||{},i=`#${this.model.entityType}/${this.name}/${this.model.id}/`+e.scope;t.massUnlinkDisabled=!0;t.fullFormUrl=i;t.createDisabled=!0;e.viewOptions=t;super.actionViewRelatedList(e)}isBasePlus(){var e=this.getMetadata().get(`scopes.${this.model.entityType}.type`);return"BasePlus"===e}}e.default=a});define("modules/crm/views/meeting/detail",["exports","views/detail","moment"],function(e,t,a){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);a=i(a);function i(e){return e&&e.__esModule?e:{default:e}}class s extends t.default{cancellationPeriod="8 hours";setup(){super.setup();this.setupStatuses();this.addMenuItem("buttons",{name:"sendInvitations",text:this.translate("Send Invitations","labels","Meeting"),acl:"edit",hidden:!0,onClick:()=>this.actionSendInvitations()});this.addMenuItem("dropdown",{name:"sendCancellation",text:this.translate("Send Cancellation","labels","Meeting"),acl:"edit",hidden:!0,onClick:()=>this.actionSendCancellation()});this.addMenuItem("buttons",{name:"setAcceptanceStatus",text:"",hidden:!0,onClick:()=>this.actionSetAcceptanceStatus()});this.setupCancellationPeriod();this.controlSendInvitationsButton();this.controlAcceptanceStatusButton();this.controlSendCancellationButton();this.listenTo(this.model,"sync",()=>{this.controlSendInvitationsButton();this.controlSendCancellationButton()});this.listenTo(this.model,"sync",()=>this.controlAcceptanceStatusButton())}setupStatuses(){this.canceledStatusList=this.getMetadata().get(`scopes.${this.entityType}.canceledStatusList`)||[];this.notActualStatusList=[...this.getMetadata().get(`scopes.${this.entityType}.completedStatusList`)||[],...this.canceledStatusList]}setupCancellationPeriod(){this.cancellationPeriodAmount=0;this.cancellationPeriodUnits="hours";var e=this.getConfig().get("eventCancellationPeriod")||this.cancellationPeriod;if(e){e=e.split(" ");this.cancellationPeriodAmount=parseInt(e[0]);this.cancellationPeriodUnits=e[1]??"hours"}}controlAcceptanceStatusButton(){if(this.model.has("status")&&this.model.has("usersIds"))if(this.notActualStatusList.includes(this.model.get("status")))this.hideHeaderActionItem("setAcceptanceStatus");else if(this.model.getLinkMultipleIdList("users").includes(this.getUser().id)){var s=this.model.getLinkMultipleColumn("users","status",this.getUser().id);let e,t="default";if(s&&"None"!==s){e=this.getLanguage().translateOption(s,"acceptanceStatus",this.model.entityType);t=this.getMetadata().get(["entityDefs",this.model.entityType,"fields","acceptanceStatus","style",s])}else e=this.translate("Acceptance","labels","Meeting");let i="";if(t){s={success:"fas fa-check-circle",danger:"fas fa-times-circle",warning:"fas fa-question-circle"}[t];i=$("<span>").addClass(s).addClass("text-"+t).get(0).outerHTML}this.updateMenuItem("setAcceptanceStatus",{text:e,iconHtml:i,hidden:!1})}else this.hideHeaderActionItem("setAcceptanceStatus")}controlSendInvitationsButton(){let e=!0;this.notActualStatusList.includes(this.model.get("status"))&&(e=!1);e&&!this.getAcl().checkModel(this.model,"edit")&&(e=!1);if(e){var t=this.model.getLinkMultipleIdList("users"),i=this.model.getLinkMultipleIdList("contacts"),s=this.model.getLinkMultipleIdList("leads");i.length||s.length||t.length||(e=!1)}if(e){i=this.model.get("dateEnd");i&&this.getDateTime().toMoment(i).isBefore(a.default.now())&&(e=!1)}e?this.showHeaderActionItem("sendInvitations"):this.hideHeaderActionItem("sendInvitations")}controlSendCancellationButton(){let e=this.canceledStatusList.includes(this.model.get("status"));if(e){var t=this.model.get("dateEnd");t&&this.getDateTime().toMoment(t).add(this.cancellationPeriodAmount,this.cancellationPeriodUnits).isBefore(a.default.now())&&(e=!1)}if(e){var t=this.model.getLinkMultipleIdList("users"),i=this.model.getLinkMultipleIdList("contacts"),s=this.model.getLinkMultipleIdList("leads");i.length||s.length||t.length||(e=!1)}e?this.showHeaderActionItem("sendCancellation"):this.hideHeaderActionItem("sendCancellation")}actionSendInvitations(){Espo.Ui.notifyWait();this.createView("dialog","crm:views/meeting/modals/send-invitations",{model:this.model}).then(e=>{Espo.Ui.notify(!1);e.render();this.listenToOnce(e,"sent",()=>this.model.fetch())})}actionSendCancellation(){Espo.Ui.notifyWait();this.createView("dialog","crm:views/meeting/modals/send-cancellation",{model:this.model}).then(e=>{Espo.Ui.notify(!1);e.render();this.listenToOnce(e,"sent",()=>this.model.fetch())})}actionSetAcceptanceStatus(){this.createView("dialog","crm:views/meeting/modals/acceptance-status",{model:this.model},e=>{e.render();this.listenTo(e,"set-status",e=>{this.disableMenuItem("setAcceptanceStatus");Espo.Ui.notifyWait();Espo.Ajax.postRequest(this.model.entityType+"/action/setAcceptanceStatus",{id:this.model.id,status:e}).then(()=>{this.model.fetch().then(()=>{Espo.Ui.notify(!1);this.enableMenuItem("setAcceptanceStatus")})}).catch(()=>this.enableMenuItem("setAcceptanceStatus"))})})}}e.default=s});define("modules/crm/views/meeting/record/list",["exports","views/record/list"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{rowActionsView="modules/crm/views/meeting/record/row-actions/default";setup(){super.setup();if(this.getAcl().checkScope(this.entityType,"edit")&&this.getAcl().checkField(this.entityType,"status","edit")){this.massActionList.push("setHeld");this.massActionList.push("setNotHeld")}}async actionSetHeld(e){var t=e.id;if(t){t=this.collection.get(t);if(t){Espo.Ui.notify(this.translate("saving","messages"));await t.save({status:"Held"},{patch:!0});Espo.Ui.success(this.translate("Saved"))}}}async actionSetNotHeld(e){var t=e.id;if(t){t=this.collection.get(t);if(t){Espo.Ui.notify(this.translate("saving","messages"));await t.save({status:"Not Held"},{patch:!0});Espo.Ui.success(this.translate("Saved"))}}}async massActionSetHeld(){var e={};e.ids=this.checkedList;Espo.Ui.notify(this.translate("saving","messages"));await Espo.Ajax.postRequest(this.collection.entityType+"/action/massSetHeld",e);Espo.Ui.success(this.translate("Saved"));await this.collection.fetch();e.ids.forEach(e=>{this.collection.get(e)&&this.checkRecord(e)})}async massActionSetNotHeld(){var e={};e.ids=this.checkedList;Espo.Ui.notify(this.translate("saving","messages"));await Espo.Ajax.postRequest(this.collection.entityType+"/action/massSetNotHeld",e);Espo.Ui.success(this.translate("Saved"));await this.collection.fetch();e.ids.forEach(e=>{this.collection.get(e)&&this.checkRecord(e)})}}e.default=s});define("crm:views/mass-email/record/edit",["views/record/edit"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.initFieldsControl()},initFieldsControl:function(){this.listenTo(this.model,"change:smtpAccount",(e,t,i)=>{if(i.ui)if(t&&"system"!==t){var s=this.getFieldView("smtpAccount");if(s&&s.loadedOptionAddresses&&s.loadedOptionAddresses[t]){this.model.set("fromAddress",s.loadedOptionAddresses[t]);this.model.set("fromName",s.loadedOptionFromNames[t])}}else{this.model.set("fromAddress",this.getConfig().get("outboundEmailFromAddress")||"");this.model.set("fromName",this.getConfig().get("outboundEmailFromName")||"")}})}})});define("modules/crm/views/mass-email/modals/send-test",["exports","views/modal","model","views/record/edit-for-modal","views/fields/link-multiple"],function(e,t,i,s,a){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=n(t);i=n(i);s=n(s);a=n(a);function n(e){return e&&e.__esModule?e:{default:e}}class o extends t.default{templateContent=`
        <div class="record-container no-side-margin">{{{record}}}</div>
    `;recordView;formModel;constructor(e){super(e);this.model=e.model}setup(){super.setup();this.headerText=this.translate("Send Test","labels","MassEmail");var e=this.formModel=new i.default,t=(e.set("usersIds",[this.getUser().id]),{});t[this.getUser().id]=this.getUser().get("name");e.set("usersNames",t);this.recordView=new s.default({model:e,detailLayout:[{rows:[[{view:new a.default({name:"users",labelText:this.translate("users","links","TargetList"),mode:"edit",params:{entity:"User"}})},!1],[{view:new a.default({name:"contacts",labelText:this.translate("contacts","links","TargetList"),mode:"edit",params:{entity:"Contact"}})},!1],[{view:new a.default({name:"leads",labelText:this.translate("leads","links","TargetList"),mode:"edit",params:{entity:"Lead"}})},!1],[{view:new a.default({name:"accounts",labelText:this.translate("accounts","links","TargetList"),mode:"edit",params:{entity:"Account"}})},!1]]}]});this.assignView("record",this.recordView);this.buttonList.push({name:"sendTest",label:"Send Test",style:"danger",onClick:()=>this.actionSendTest()});this.buttonList.push({name:"cancel",label:"Cancel",onClick:()=>this.actionClose()})}actionSendTest(){let t=[];Array.isArray(this.formModel.attributes.usersIds)&&this.formModel.attributes.usersIds.forEach(e=>{t.push({id:e,type:"User"})});Array.isArray(this.formModel.attributes.contactsIds)&&this.formModel.attributes.contactsIds.forEach(e=>{t.push({id:e,type:"Contact"})});Array.isArray(this.formModel.attributes.leadsIds)&&this.formModel.attributes.leadsIds.forEach(e=>{t.push({id:e,type:"Lead"})});Array.isArray(this.formModel.attributes.accountsIds)&&this.formModel.attributes.accountsIds.forEach(e=>{t.push({id:e,type:"Account"})});if(0===t.length)Espo.Ui.error(this.translate("selectAtLeastOneTarget","messages","MassEmail"));else{this.disableButton("sendTest");Espo.Ui.notifyWait();Espo.Ajax.postRequest("MassEmail/action/sendTest",{id:this.model.id,targetList:t}).then(()=>{Espo.Ui.success(this.translate("testSent","messages","MassEmail"));this.close()}).catch(()=>{this.enableButton("sendTest")})}}}e.default=o});define("crm:views/dashlets/options/chart",["views/dashlets/options/base"],function(e){return e.extend({setupBeforeFinal:function(){this.listenTo(this.model,"change:dateFilter",this.controlDateFilter);this.controlDateFilter()},controlDateFilter:function(){if("between"===this.model.get("dateFilter")){this.showField("dateFrom");this.showField("dateTo")}else{this.hideField("dateFrom");this.hideField("dateTo")}}})});define("crm:views/contact/record/detail",["views/record/detail"],function(e){return e.extend({})});define("modules/crm/views/call/record/list",["exports","views/record/list"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{rowActionsView="modules/crm/views/call/record/row-actions/default";setup(){super.setup();if(this.getAcl().checkScope(this.entityType,"edit")&&this.getAcl().checkField(this.entityType,"status","edit")){this.massActionList.push("setHeld");this.massActionList.push("setNotHeld")}}async actionSetHeld(e){var t=e.id;if(t){t=this.collection.get(t);if(t){Espo.Ui.notify(this.translate("saving","messages"));await t.save({status:"Held"},{patch:!0});Espo.Ui.success(this.translate("Saved"))}}}async actionSetNotHeld(e){var t=e.id;if(t){t=this.collection.get(t);if(t){Espo.Ui.notify(this.translate("saving","messages"));await t.save({status:"Not Held"},{patch:!0});Espo.Ui.success(this.translate("Saved"))}}}async massActionSetHeld(){var e={};e.ids=this.checkedList;Espo.Ui.notify(this.translate("saving","messages"));await Espo.Ajax.postRequest(this.collection.entityType+"/action/massSetHeld",e);Espo.Ui.success(this.translate("Saved"));await this.collection.fetch();e.ids.forEach(e=>{this.collection.get(e)&&this.checkRecord(e)})}async massActionSetNotHeld(){var e={};e.ids=this.checkedList;Espo.Ui.notify(this.translate("saving","messages"));await Espo.Ajax.postRequest(this.collection.entityType+"/action/massSetNotHeld",e);Espo.Ui.success(this.translate("Saved"));await this.collection.fetch();e.ids.forEach(e=>{this.collection.get(e)&&this.checkRecord(e)})}}e.default=s});define("modules/crm/views/call/fields/contacts",["exports","modules/crm/views/meeting/fields/attendees"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{getAttributeList(){return[...super.getAttributeList(),"phoneNumbersMap"]}getDetailLinkHtml(e,t){var i=super.getDetailLinkHtml(e,t),s=this.foreignScope+"_"+e,a=this.model.get("phoneNumbersMap")||{};if(!(s in a))return i;a=a[s],s=$(i);s.append(" ",$("<span>").addClass("text-muted middle-dot")," ",$("<a>").attr("href","tel:"+a).attr("data-phone-number",a).attr("data-action","dial").addClass("small").text(a));return $("<div>").append(s).get(0).outerHTML}}e.default=s});define("modules/crm/views/calendar/modals/edit-view",["exports","views/modal","model","views/record/edit-for-modal","views/fields/enum","views/fields/varchar","crm:views/calendar/fields/teams"],function(e,t,a,n,o,l,d){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);a=i(a);n=i(n);o=i(o);l=i(l);d=i(d);function i(e){return e&&e.__esModule?e:{default:e}}class s extends t.default{templateContent=`
        <div class="record-container no-side-margin">{{{record}}}</div>
    `;className="dialog dialog-record";recordView;constructor(e){super();this.options=e}setup(){let i=this.options.id;this.buttonList=[{name:"cancel",label:"Cancel",onClick:()=>this.actionCancel()}];this.isNew=!i;var e=this.getPreferences().get("calendarViewDataList")||[];if(this.isNew)this.buttonList.unshift({name:"save",label:"Create",style:"danger",onClick:()=>this.actionSave()});else{this.dropdownItemList.push({name:"remove",label:"Remove",onClick:()=>this.actionRemove()});this.buttonList.unshift({name:"save",label:"Save",style:"primary",onClick:()=>this.actionSave()})}var t=new a.default;t.name="CalendarView";let s={};if(this.isNew){s.name=this.translate("Shared","labels","Calendar");let t=0;e.forEach(e=>{0===e.name.indexOf(s.name)&&t++});t&&(s.name+=" "+t);s.id=i;s.teamsIds=this.getUser().get("teamsIds")||[];s.teamsNames=this.getUser().get("teamsNames")||{}}else e.forEach(e=>{if(i===e.id){s.teamsIds=e.teamIdList||[];s.teamsNames=e.teamNames||{};s.id=e.id;s.name=e.name;s.mode=e.mode}});t.set(s);this.recordView=new n.default({model:t,detailLayout:[{rows:[[{view:new l.default({name:"name",labelText:this.translate("name","fields"),params:{required:!0}})},{view:new o.default({name:"mode",labelText:this.translate("mode","fields","DashletOptions"),params:{translation:"DashletOptions.options.mode",options:this.getMetadata().get("clientDefs.Calendar.sharedViewModeList")||[]}})}],[{view:new d.default({name:"teams",labelText:this.translate("teams","fields"),params:{required:!0}})},!1]]}]});this.assignView("record",this.recordView);this.isNew?this.headerText=this.translate("Create Shared View","labels","Calendar"):this.headerText=this.translate("Edit Shared View","labels","Calendar")+" · "+s.name}async actionSave(){if(!this.recordView.validate()){var e=this.recordView.fetch();let i=this.getPreferences().get("calendarViewDataList")||[],s={name:e.name,teamIdList:e.teamsIds,teamNames:e.teamsNames,mode:e.mode,id:void 0};if(this.isNew){s.id=Math.random().toString(36).substring(2,12);i.push(s)}else{s.id=this.getView("record").model.id;i.forEach((e,t)=>{e.id===s.id&&(i[t]=s)})}Espo.Ui.notify(this.translate("saving","messages"));this.disableButton("save");this.disableButton("remove");try{await this.getPreferences().save({calendarViewDataList:i},{patch:!0})}catch(e){this.enableButton("remove");this.enableButton("save");return}Espo.Ui.notify();this.trigger("after:save",s);this.options.afterSave&&this.options.afterSave(s);this.close()}}async actionRemove(){await this.confirm(this.translate("confirmation","messages"));this.disableButton("save");this.disableButton("remove");let i=this.options.id;if(i){let t=[];var e=this.getPreferences().get("calendarViewDataList")||[];e.forEach(e=>{e.id!==i&&t.push(e)});Espo.Ui.notifyWait();try{await this.getPreferences().save({calendarViewDataList:t},{patch:!0})}catch(e){this.enableButton("remove");this.enableButton("save");return}Espo.Ui.notify();this.trigger("after:remove");this.options.afterRemove&&this.options.afterRemove();this.close()}}}e.default=s});define("modules/crm/views/calendar/fields/users",["exports","views/fields/link-multiple"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{foreignScope="User";sortable=!0;getSelectBoolFilterList(){if("team"===this.getAcl().getPermissionLevel("userCalendar"))return["onlyMyTeam"]}getSelectPrimaryFilterName(){return"active"}}e.default=s});define("modules/crm/acl/meeting",["exports","acl"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkModelRead(e,t,i){return this._checkModelCustom("read",e,t,i)}checkModelStream(e,t,i){return this._checkModelCustom("stream",e,t,i)}_checkModelCustom(e,t,i,s){var a=this.checkModel(t,i,e,s);if(a)return!0;if(!1===i)return!1;var n=i||{};if("no"===n[e])return!1;if(t.has("usersIds")){if(~(t.get("usersIds")||[]).indexOf(this.getUser().id))return!0}else if(s)return null;return a}}e.default=s});define("crm:views/user/record/panels/tasks",["crm:views/record/panels/tasks"],function(t){return t.extend({listLayout:{rows:[[{name:"name",link:!0},{name:"isOverdue"}],[{name:"status"},{name:"dateEnd"}]]},setup:function(){t.prototype.setup.call(this);if(this.getMetadata().get(["entityDefs","Task","fields","assignedUsers"])){var e=this.getMetadata().get(["entityDefs","Task","links","assignedUsers","foreign"]);e&&(this.link=e)}}})});define("modules/crm/views/task/list",["exports","views/list"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/task/detail",["exports","views/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("crm:views/task/record/list-expanded",["views/record/list-expanded","crm:views/task/record/list"],function(e,t){return e.extend({rowActionsView:"crm:views/task/record/row-actions/default",actionSetCompleted:function(e){t.prototype.actionSetCompleted.call(this,e)}})});define("crm:views/task/record/detail",["views/record/detail"],function(t){return t.extend({duplicateAction:!0,setupActionItems:function(){t.prototype.setupActionItems.call(this);if(this.getAcl().checkModel(this.model,"edit")){!~["Completed","Canceled"].indexOf(this.model.get("status"))&&this.getAcl().checkField(this.entityType,"status","edit")&&this.dropdownItemList.push({label:"Complete",name:"setCompleted"});this.listenToOnce(this.model,"sync",function(){~["Completed","Canceled"].indexOf(this.model.get("status"))&&this.removeButton("setCompleted")},this)}},manageAccessEdit:function(e){t.prototype.manageAccessEdit.call(this,e);!e||this.getAcl().checkModel(this.model,"edit",!0)||this.hideActionItem("setCompleted")},actionSetCompleted:function(){this.model.save({status:"Completed"},{patch:!0}).then(()=>Espo.Ui.success(this.translate("Saved")))}})});define("crm:views/task/record/row-actions/default",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);this.options.acl.edit&&!~["Completed","Canceled"].indexOf(this.model.get("status"))&&e.push({action:"setCompleted",label:"Complete",data:{id:this.model.id},groupIndex:1});this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/task/record/row-actions/dashlet",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);this.options.acl.edit&&!~["Completed","Canceled"].indexOf(this.model.get("status"))&&e.push({action:"setCompleted",label:"Complete",data:{id:this.model.id},groupIndex:1});this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/task/modals/detail",["views/modals/detail"],function(e){return e.extend({})});define("modules/crm/views/task/fields/tasks",["exports","views/fields/link-multiple-with-status"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{setup(){super.setup();this.canceledStatusList=this.getMetadata().get("scopes.Task.canceledStatusList")||[]}}e.default=s});define("crm:views/task/fields/priority-for-dashlet",["views/fields/enum"],function(t){return t.extend({data:function(){var e=t.prototype.data.call(this);e.style&&"default"!==e.style||(e.isNotEmpty=!1);return e}})});define("modules/crm/views/task/fields/is-overdue",["exports","views/fields/base","moment"],function(e,t,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);s=i(s);function i(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{readOnly=!0;templateContent=`
        {{~#if isOverdue}}
        <span class="label label-danger">{{translate "overdue" scope="Task"}}</span>
        {{/if~}}
    `;data(){let e=!1;if(-1===["Completed","Canceled"].indexOf(this.model.get("status"))&&this.model.has("dateEnd"))if(this.isDate()){var t=this.model.get("dateEndDate");if(t){var t=s.default.utc(t+" 23:59",this.getDateTime().internalDateTimeFormat),i=this.getDateTime().getNowMoment();t.unix()<i.unix()&&(e=!0)}}else{t=this.model.get("dateEnd");if(t){i=this.getDateTime().toMoment(t),t=s.default.tz(this.getDateTime().timeZone||"UTC");i.unix()<t.unix()&&(e=!0)}}return{isOverdue:e}}setup(){this.mode="detail"}isDate(){var e=this.model.get("dateEnd");return!!e}}e.default=a});define("modules/crm/views/task/fields/date-end",["exports","views/fields/datetime-optional","moment"],function(e,t,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);s=i(s);function i(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{isEnd=!0;data(){var e=super.data(),t=this.model.attributes.status;if(t&&!this.notActualStatusList.includes(t)){this.mode!==this.MODE_DETAIL&&this.mode!==this.MODE_LIST||(this.isDateInPast()?e.isOverdue=!0:this.isDateToday()&&(e.style="warning"));e.isOverdue&&(e.style="danger")}return e}setup(){super.setup();this.notActualStatusList=[...this.getMetadata().get(`scopes.${this.entityType}.completedStatusList`)||[],...this.getMetadata().get(`scopes.${this.entityType}.canceledStatusList`)||[]];(this.isEditMode()||this.isDetailMode())&&this.on("change",()=>{!this.model.get("dateEnd")&&this.model.get("reminders")&&this.model.set("reminders",[])})}isDateInPast(){if(this.isDate()){let e=this.model.get(this.nameDate);if(e){var t=s.default.tz(e+" 23:59",this.getDateTime().getTimeZone()),i=this.getDateTime().getNowMoment();if(t.unix()<i.unix())return!0}}let e=this.model.get(this.name);if(e){t=this.getDateTime().toMoment(e),i=(0,s.default)().tz(this.getDateTime().timeZone||"UTC");if(t.unix()<i.unix())return!0}return!1}isDateToday(){return!!this.isDate()&&this.getDateTime().getToday()===this.model.attributes[this.nameDate]}}e.default=a});define("crm:views/target-list/record/detail",["views/record/detail"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.listenTo(this.model,"after:relate",()=>{this.model.fetch()});this.listenTo(this.model,"after:unrelate",()=>{this.model.fetch()})}})});define("crm:views/target-list/record/row-actions/opted-out",["views/record/row-actions/default"],function(e){return e.extend({getActionList:function(){return[{action:"cancelOptOut",text:this.translate("Cancel Opt-Out","labels","TargetList"),data:{id:this.model.id,type:this.model.entityType}}]}})});define("crm:views/target-list/record/row-actions/default",["views/record/row-actions/relationship"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);this.options.acl.edit&&(this.model.get("targetListIsOptedOut")?e.push({action:"cancelOptOut",text:this.translate("Cancel Opt-Out","labels","TargetList"),data:{id:this.model.id,type:this.model.entityType}}):e.push({action:"optOut",text:this.translate("Opt-Out","labels","TargetList"),data:{id:this.model.id,type:this.model.entityType}}));return e}})});define("crm:views/target-list/record/panels/relationship",["views/record/panels/relationship"],function(e){return e.extend({fetchOnModelAfterRelate:!0,actionOptOut:function(e){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest("TargetList/action/optOut",{id:this.model.id,targetId:e.id,targetType:e.type}).then(()=>{this.collection.fetch();Espo.Ui.success(this.translate("Done"));this.model.trigger("opt-out")})})},actionCancelOptOut:function(e){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest("TargetList/action/cancelOptOut",{id:this.model.id,targetId:e.id,targetType:e.type}).then(()=>{this.collection.fetch();Espo.Ui.success(this.translate("Done"));this.collection.fetch();this.model.trigger("cancel-opt-out")})})}})});define("crm:views/target-list/record/panels/opted-out",["views/record/panels/relationship","multi-collection"],function(e,t){return e.extend({name:"optedOut",template:"crm:target-list/record/panels/opted-out",scopeList:["Contact","Lead","User","Account"],data:function(){return{currentTab:this.currentTab,scopeList:this.scopeList}},getStorageKey:function(){return"target-list-opted-out-"+this.model.entityType+"-"+this.name},setup:function(){this.seeds={};var e=this.getMetadata().get(["scopes","TargetList","targetLinkList"])||[];this.scopeList=[];e.forEach(e=>{var t=this.getMetadata().get(["entityDefs","TargetList","links",e,"entity"]);t&&this.scopeList.push(t)});this.listLayout={};this.scopeList.forEach(e=>{this.listLayout[e]={rows:[[{name:"name",link:!0}]]}});if(this.scopeList.length){this.wait(!0);var i=0;this.scopeList.forEach(t=>{this.getModelFactory().create(t,e=>{this.seeds[t]=e;i++;i===this.scopeList.length&&this.wait(!1)})})}this.listenTo(this.model,"opt-out",()=>{this.actionRefresh()});this.listenTo(this.model,"cancel-opt-out",()=>{this.actionRefresh()})},afterRender:function(){var e="TargetList/"+this.model.id+"/"+this.name;this.collection=new t;this.collection.seeds=this.seeds;this.collection.url=e;this.collection.maxSize=this.getConfig().get("recordsPerPageSmall")||5;this.listenToOnce(this.collection,"sync",()=>{this.createView("list","views/record/list-expanded",{selector:"> .list-container",pagination:!1,type:"listRelationship",rowActionsView:"crm:views/target-list/record/row-actions/opted-out",checkboxes:!1,collection:this.collection,listLayout:this.listLayout},e=>{e.render()})});this.collection.fetch()},actionRefresh:function(){this.collection.fetch()},actionCancelOptOut:function(e){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest("TargetList/action/cancelOptOut",{id:this.model.id,targetId:e.id,targetType:e.type}).then(()=>{this.collection.fetch()})})}})});define("crm:views/target-list/fields/target-status",["views/fields/base"],function(e){return e.extend({getValueForDisplay:function(){return this.model.get("isOptedOut")?this.getLanguage().translateOption("Opted Out","targetStatus","TargetList"):this.getLanguage().translateOption("Listed","targetStatus","TargetList")}})});define("crm:views/target-list/fields/including-action-list",["views/fields/multi-enum"],function(e){return e.extend({setupOptions:function(){this.params.options=this.getMetadata().get("entityDefs.CampaignLogRecord.fields.action.options")||[];this.translatedOptions={};this.params.options.forEach(e=>{this.translatedOptions[e]=this.getLanguage().translateOption(e,"action","CampaignLogRecord")})}})});define("modules/crm/views/stream/notes/event-confirmation",["exports","views/stream/note"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{templateContent=`
        {{#unless noEdit}}
        <div class="pull-right right-container cell-buttons">
        {{{right}}}
        </div>
        {{/unless}}

        <div class="stream-head-container">
            <div class="pull-left">
                {{{avatar}}}
            </div>
            <div class="stream-head-text-container">
                {{#if iconHtml}}{{{iconHtml}}}{{/if}}
                <span class="text-muted message">{{{message}}}</span>
            </div>
        </div>
        <div class="stream-post-container">
            <span class="{{statusIconClass}} text-{{style}}"></span>
        </div>
        <div class="stream-date-container">
            <a class="text-muted small" href="#Note/view/{{model.id}}">{{{createdAt}}}</a>
        </div>
    `;data(){var e={success:"fas fa-check fa-sm",danger:"fas fa-times fa-sm",warning:"fas fa-question fa-sm"}[this.style]||"";return{...super.data(),statusText:this.statusText,style:this.style,statusIconClass:e,iconHtml:this.getIconHtml()}}init(){this.getUser().isAdmin()&&(this.isRemovable=!0);super.init()}setup(){this.inviteeType=this.model.get("relatedType");this.inviteeId=this.model.get("relatedId");this.inviteeName=this.model.get("relatedName");var e=this.model.get("data")||{},t=e.status||"Tentative";this.style=e.style||"default";this.statusText=this.getLanguage().translateOption(t,"acceptanceStatus","Meeting");this.messageName="eventConfirmation"+t;this.isThis&&(this.messageName+="This");this.messageData.invitee=$("<a>").attr("href","#"+this.inviteeType+"/view/"+this.inviteeId).attr("data-id",this.inviteeId).attr("data-scope",this.inviteeType).text(this.inviteeName);this.createMessage()}}e.default=s});define("crm:views/record/list-activities-dashlet",["views/record/list-expanded","crm:views/meeting/record/list","crm:views/task/record/list"],function(e,t,i){return e.extend({actionSetHeld:function(e){t.prototype.actionSetHeld.call(this,e)},actionSetNotHeld:function(e){t.prototype.actionSetNotHeld.call(this,e)},actionSetCompleted:function(e){i.prototype.actionSetCompleted.call(this,e)}})});define("crm:views/record/row-actions/tasks",["views/record/row-actions/relationship-no-unlink"],function(e){return e.extend({getActionList:function(){var e=[{action:"quickView",label:"View",data:{id:this.model.id},link:"#"+this.model.entityType+"/view/"+this.model.id,groupIndex:0}];if(this.options.acl.edit){e.push({action:"quickEdit",label:"Edit",data:{id:this.model.id},link:"#"+this.model.entityType+"/edit/"+this.model.id,groupIndex:0});~["Completed","Canceled"].indexOf(this.model.get("status"))||e.push({action:"Complete",text:this.translate("Complete","labels","Task"),data:{id:this.model.id},groupIndex:1})}this.options.acl.delete&&e.push({action:"removeRelated",label:"Remove",data:{id:this.model.id},groupIndex:0});return e}})});define("crm:views/record/row-actions/relationship-target",["views/record/row-actions/relationship-unlink-only"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);this.options.acl.edit&&(this.model.get("isOptedOut")?e.push({action:"cancelOptOut",text:this.translate("Cancel Opt-Out","labels","TargetList"),data:{id:this.model.id},groupIndex:1}):e.push({action:"optOut",text:this.translate("Opt-Out","labels","TargetList"),data:{id:this.model.id},groupIndex:1}));return e}})});define("crm:views/record/row-actions/history",["views/record/row-actions/relationship"],function(e){return e.extend({getActionList:function(){var e=[{action:"quickView",label:"View",data:{id:this.model.id},link:"#"+this.model.entityType+"/view/"+this.model.id,groupIndex:0}];"Email"===this.model.entityType&&e.push({action:"reply",text:this.translate("Reply","labels","Email"),data:{id:this.model.id},groupIndex:1});this.options.acl.edit&&(e=e.concat([{action:"quickEdit",label:"Edit",data:{id:this.model.id},link:"#"+this.model.entityType+"/edit/"+this.model.id,groupIndex:0}]));this.options.acl.delete&&e.push({action:"removeRelated",label:"Remove",data:{id:this.model.id},groupIndex:0});return e}})});define("crm:views/record/row-actions/activities",["views/record/row-actions/relationship"],function(e){return e.extend({getActionList:function(){var e=[{action:"quickView",label:"View",data:{id:this.model.id},link:"#"+this.model.entityType+"/view/"+this.model.id,groupIndex:0}];if(this.options.acl.edit){e.push({action:"quickEdit",label:"Edit",data:{id:this.model.id},link:"#"+this.model.entityType+"/edit/"+this.model.id,groupIndex:0});if("Meeting"===this.model.entityType||"Call"===this.model.entityType){e.push({action:"setHeld",text:this.translate("Set Held","labels","Meeting"),data:{id:this.model.id},groupIndex:1});e.push({action:"setNotHeld",text:this.translate("Set Not Held","labels","Meeting"),data:{id:this.model.id},groupIndex:1})}}this.options.acl.delete&&e.push({action:"removeRelated",label:"Remove",data:{id:this.model.id},groupIndex:0});return e}})});define("crm:views/record/row-actions/activities-dashlet",["views/record/row-actions/view-and-edit"],function(i){return i.extend({getActionList:function(){var e=i.prototype.getActionList.call(this),t=this.model.entityType;e.forEach(function(e){e.data=e.data||{};e.data.scope=this.model.entityType},this);if("Task"===t)this.options.acl.edit&&!~["Completed","Canceled"].indexOf(this.model.get("status"))&&e.push({action:"setCompleted",label:"Complete",data:{id:this.model.id},groupIndex:1});else if(this.options.acl.edit&&!~["Held","Not Held"].indexOf(this.model.get("status"))){e.push({action:"setHeld",label:"Set Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1});e.push({action:"setNotHeld",label:"Set Not Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1})}this.options.acl.edit&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/record/panels/target-lists",["views/record/panels/relationship"],function(e){return e.extend({actionOptOut:function(e){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest("TargetList/action/optOut",{id:e.id,targetId:this.model.id,targetType:this.model.entityType}).then(()=>{this.collection.fetch();Espo.Ui.success(this.translate("Done"));this.model.trigger("opt-out")})})},actionCancelOptOut:function(e){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest("TargetList/action/cancelOptOut",{id:e.id,targetId:this.model.id,targetType:this.model.entityType}).then(()=>{this.collection.fetch();Espo.Ui.success(this.translate("Done"));this.model.trigger("cancel-opt-out")})})}})});define("modules/crm/views/record/panels/history",["exports","crm:views/record/panels/activities","email-helper","helpers/record-modal"],function(e,t,i,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=a(t);i=a(i);s=a(s);function a(e){return e&&e.__esModule?e:{default:e}}class n extends t.default{name="history";orderBy="dateStart";orderDirection="desc";rowActionsView="crm:views/record/row-actions/history";actionList=[];listLayout={Email:{rows:[[{name:"ico",view:"crm:views/fields/ico"},{name:"name",link:!0}],[{name:"dateSent",soft:!0},{name:"status"},{name:"hasAttachment",view:"views/email/fields/has-attachment"}]]}};where={scope:!1};setupActionList(){super.setupActionList();this.actionList.push({action:"archiveEmail",label:"Archive Email",acl:"create",aclScope:"Email"})}getArchiveEmailAttributes(e,t,i){var s={dateSent:this.getDateTime().getNow(15),status:"Archived",from:this.model.get("emailAddress"),to:this.getUser().get("emailAddress")};if("Contact"===this.model.entityType){if(this.getConfig().get("b2cMode")){s.parentType="Contact";s.parentName=this.model.get("name");s.parentId=this.model.id}else if(this.model.get("accountId")){s.parentType="Account";s.parentId=this.model.get("accountId");s.parentName=this.model.get("accountName")}}else if("Lead"===this.model.entityType){s.parentType="Lead";s.parentId=this.model.id;s.parentName=this.model.get("name")}s.nameHash={};s.nameHash[this.model.get("emailAddress")]=this.model.get("name");if(e)if(s.parentId){if(s.parentType&&!this.checkParentTypeAvailability(e,s.parentType)){s.parentType=null;s.parentId=null;s.parentName=null}}else if(this.checkParentTypeAvailability(e,this.model.entityType)){s.parentType=this.model.entityType;s.parentId=this.model.id;s.parentName=this.model.get("name")}i.call(this,s)}actionArchiveEmail(e){let i=null;this.model.hasLink("emails")&&(i={model:this.model,link:this.model.getLinkParam("emails","foreign")});this.getArchiveEmailAttributes("Email",e,e=>{var t=new s.default;t.showCreate(this,{entityType:"Email",attributes:e,relate:i,afterSave:()=>{this.collection.fetch();this.model.trigger("after:relate")}})})}actionReply(a){let e=a.id;if(e){let s=new i.default;Espo.Ui.notifyWait();this.getModelFactory().create("Email").then(i=>{i.id=e;i.fetch().then(()=>{var e=s.getReplyAttributes(i,a,this.getPreferences().get("emailReplyToAllByDefault")),t=this.getMetadata().get("clientDefs.Email.modalViews.compose")||"views/modals/compose-email";return this.createView("quickCreate",t,{attributes:e,focusForCreate:!0})}).then(e=>{e.render();this.listenToOnce(e,"after:save",()=>{this.collection.fetch();this.model.trigger("after:relate")});Espo.Ui.notify(!1)})})}}}e.default=n});define("crm:views/opportunity/detail",["views/detail"],function(e){return e.extend({})});define("crm:views/opportunity/record/list",["views/record/list"],function(e){return e.extend({})});define("crm:views/opportunity/record/kanban",["views/record/kanban"],function(e){return e.extend({handleAttributesOnGroupChange:function(e,t,i){if("stage"===this.statusField){var s=this.getMetadata().get(["entityDefs","Opportunity","fields","stage","probabilityMap",i]),s=parseInt(s);t.probability=s}}})});define("modules/crm/views/opportunity/record/edit",["exports","views/record/edit"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/opportunity/record/edit-small",["exports","views/record/edit-small"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("crm:views/opportunity/record/panels/activities",["crm:views/record/panels/activities"],function(s){return s.extend({getComposeEmailAttributes:function(e,t,i){t=t||{};Espo.Ui.notifyWait();s.prototype.getComposeEmailAttributes.call(this,e,t,t=>{Espo.Ajax.getRequest("Opportunity/action/emailAddressList?id="+this.model.id).then(e=>{t.to="";t.cc="";t.nameHash={};e.forEach(e=>{t.to+=e.emailAddress+";";t.nameHash[e.emailAddress]=e.name});Espo.Ui.notify(!1);i.call(this,t)})})}})});define("crm:views/opportunity/fields/stage",["views/fields/enum"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.probabilityMap=this.getMetadata().get("entityDefs.Opportunity.fields.stage.probabilityMap")||{};"list"!==this.mode&&this.on("change",()=>{var e=this.probabilityMap[this.model.get(this.name)];null!=e&&this.model.set("probability",e)})}})});define("crm:views/opportunity/fields/lead-source",["views/fields/enum"],function(e){return e.extend({})});define("modules/crm/views/opportunity/fields/last-stage",["exports","views/fields/enum"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{setup(){var e=this.getMetadata().get("entityDefs.Opportunity.fields.stage.options",[]);let t=this.getMetadata().get("entityDefs.Opportunity.fields.stage.probabilityMap",{});this.params.options=[];e.forEach(e=>{t[e]&&100!==t[e]&&this.params.options.push(e)});this.params.translation="Opportunity.options.stage";super.setup()}}e.default=s});define("crm:views/opportunity/fields/contacts",["views/fields/link-multiple-with-columns-with-primary"],function(e){return e.extend({})});define("crm:views/opportunity/fields/contact-role",["views/fields/enum"],function(e){return e.extend({searchTypeList:["anyOf","noneOf"]})});define("modules/crm/views/opportunity/admin/field-manager/fields/probability-map",["exports","views/fields/base","jquery"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{editTemplateContent=`
        <div class="list-group link-container no-input">
            {{#each stageList}}
                <div class="list-group-item form-inline">
                    <div style="display: inline-block; width: 100%;">
                        <input
                            class="role form-control input-sm pull-right"
                            data-name="{{./this}}" value="{{prop ../values this}}"
                        >
                        <div>{{./this}}</div>
                    </div>
                    <br class="clear: both;">
                </div>
            {{/each}}
        </div>
    `;setup(){super.setup();this.listenTo(this.model,"change:options",function(e,t,i){let s=this.model.get("probabilityMap")||{};if(i.ui){(this.model.get("options")||[]).forEach(e=>{e in s||(s[e]=50)});this.model.set("probabilityMap",s)}this.reRender()})}data(){var e={},t=this.model.get("probabilityMap")||{};e.stageList=this.model.get("options")||[];e.values=t;return e}fetch(){let t={probabilityMap:{}};(this.model.get("options")||[]).forEach(e=>{t.probabilityMap[e]=parseInt((0,i.default)(this.element).find(`input[data-name="${e}"]`).val())});return t}afterRender(){(0,i.default)(this.element).find("input").on("change",()=>{this.trigger("change")})}}e.default=a});define("crm:views/notification/items/event-attendee",["views/notification/items/base"],function(e){return e.extend({messageName:"eventAttendee",templateContent:`
            <div class="stream-head-container">
                <div class="pull-left">{{{avatar}}}</div>
                <div class="stream-head-text-container">
                    <span class="text-muted message">{{{message}}}</span>
                </div>
            </div>
            <div class="stream-date-container">
                <span class="text-muted small">{{{createdAt}}}</span>
            </div>
        `,setup:function(){var e=this.model.get("data")||{};this.userId=e.userId;this.messageData.entityType=this.translateEntityType(e.entityType);this.messageData.entity=$("<a>").attr("href","#"+e.entityType+"/view/"+e.entityId).attr("data-id",e.entityId).attr("data-scope",e.entityType).text(e.entityName);this.messageData.user=$("<a>").attr("href","#User/view/"+e.userId).attr("data-id",e.userId).attr("data-scope","User").text(e.userName);this.createMessage()}})});define("modules/crm/views/meeting/popup-notification",["exports","views/popup-notification"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{template="crm:meeting/popup-notification";type="event";style="primary";closeButton=!0;setup(){if(this.notificationData.entityType){var e=this.getModelFactory().create(this.notificationData.entityType,e=>{var t=this.notificationData.dateField,i=e.getFieldParam(t,"type")||"base",i=this.getFieldManager().getViewName(i);e.set(this.notificationData.attributes);this.createView("date",i,{model:e,mode:"detail",selector:`.field[data-name="${t}"]`,name:t,readOnly:!0})});this.wait(e)}}data(){return{header:this.translate(this.notificationData.entityType,"scopeNames"),dateField:this.notificationData.dateField,...super.data()}}onCancel(){Espo.Ajax.postRequest("Activities/action/removePopupNotification",{id:this.notificationId})}}e.default=s});define("crm:views/meeting/record/list-expanded",["views/record/list-expanded","crm:views/meeting/record/list"],function(e,t){return e.extend({actionSetHeld:function(e){t.prototype.actionSetHeld.call(this,e)},actionSetNotHeld:function(e){t.prototype.actionSetNotHeld.call(this,e)}})});define("modules/crm/views/meeting/record/edit-small",["exports","views/record/edit"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/meeting/record/detail",["exports","views/record/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{duplicateAction=!0;setupActionItems(){super.setupActionItems();if(this.getAcl().checkModel(this.model,"edit")&&!["Held","Not Held"].includes(this.model.get("status"))&&this.getAcl().checkField(this.entityType,"status","edit")){var e=this.getMetadata().get(`scopes.${this.entityType}.historyStatusList`)||[];if(e.includes("Held")&&e.includes("Not Held")){this.dropdownItemList.push({label:"Set Held",name:"setHeld",onClick:()=>this.actionSetHeld()});this.dropdownItemList.push({label:"Set Not Held",name:"setNotHeld",onClick:()=>this.actionSetNotHeld()})}}}manageAccessEdit(e){super.manageAccessEdit(e);if(e&&!this.getAcl().checkModel(this.model,"edit",!0)){this.hideActionItem("setHeld");this.hideActionItem("setNotHeld")}}actionSetHeld(){this.model.save({status:"Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved"));this.removeActionItem("setHeld");this.removeActionItem("setNotHeld")})}actionSetNotHeld(){this.model.save({status:"Not Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved"));this.removeActionItem("setHeld");this.removeActionItem("setNotHeld")})}}e.default=s});define("crm:views/meeting/record/row-actions/default",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);e.forEach(e=>{e.data=e.data||{};e.data.scope=this.model.entityType});if(this.options.acl.edit&&!["Held","Not Held"].includes(this.model.get("status"))&&this.getAcl().checkField(this.model.entityType,"status","edit")){e.push({action:"setHeld",label:"Set Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1});e.push({action:"setNotHeld",label:"Set Not Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1})}this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/meeting/record/row-actions/dashlet",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);e.forEach(e=>{e.data=e.data||{};e.data.scope=this.model.entityType});if(this.options.acl.edit&&!["Held","Not Held"].includes(this.model.get("status"))&&this.getAcl().checkField(this.model.entityType,"status","edit")){e.push({action:"setHeld",label:"Set Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1});e.push({action:"setNotHeld",label:"Set Not Held",data:{id:this.model.id,scope:this.model.entityType},groupIndex:1})}this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/meeting/record/panels/scheduler",["views/record/panels/bottom"],function(t){return t.extend({templateContent:'<div class="scheduler-container no-margin">{{{scheduler}}}</div>',setup:function(){t.prototype.setup.call(this);var e=this.getMetadata().get(["clientDefs",this.scope,"schedulerView"])||"crm:views/scheduler/scheduler";this.createView("scheduler",e,{selector:".scheduler-container",notToRender:!0,model:this.model});this.once("after:render",()=>{if(!this.disabled){this.getView("scheduler").render();this.getView("scheduler").notToRender=!1}});this.defs.disabled&&this.once("show",()=>{this.getView("scheduler").render();this.getView("scheduler").notToRender=!1})},actionRefresh:function(){this.getView("scheduler").reRender()}})});define("crm:views/meeting/record/panels/attendees",["views/record/panels/side"],function(e){return e.extend({setupFields:function(){this.fieldList=[];this.fieldList.push("users");this.getAcl().check("Contact")&&!this.getMetadata().get("scopes.Contact.disabled")&&this.fieldList.push("contacts");this.getAcl().check("Lead")&&!this.getMetadata().get("scopes.Lead.disabled")&&this.fieldList.push("leads")}})});define("crm:views/meeting/modals/send-invitations",["views/modal","collection"],function(e,t){return e.extend({backdrop:!0,templateContent:`
            <div class="margin-bottom">
                <p>{{message}}</p>
            </div>
            <div class="list-container">{{{list}}}</div>
        `,data:function(){return{message:this.translate("sendInvitationsToSelectedAttendees","messages","Meeting")}},setup:function(){e.prototype.setup.call(this);this.shortcutKeys={};this.shortcutKeys["Control+Enter"]=e=>{if(this.hasAvailableActionItem("send")){e.preventDefault();this.actionSend()}};this.$header=$("<span>").append($("<span>").text(this.translate(this.model.entityType,"scopeNames")),' <span class="chevron-right"></span> ',$("<span>").text(this.model.get("name")),' <span class="chevron-right"></span> ',$("<span>").text(this.translate("Send Invitations","labels","Meeting")));this.addButton({label:"Send",name:"send",style:"danger",disabled:!0});this.addButton({label:"Cancel",name:"cancel"});this.collection=new t;this.collection.url=this.model.entityType+`/${this.model.id}/attendees`;this.wait(this.collection.fetch().then(()=>{Espo.Utils.clone(this.collection.models).forEach(e=>{e.entityType=e.get("_scope");e.get("emailAddress")||this.collection.remove(e.id)});return this.createView("list","views/record/list",{selector:".list-container",collection:this.collection,rowActionsDisabled:!0,massActionsDisabled:!0,checkAllResultDisabled:!0,selectable:!0,buttonsDisabled:!0,listLayout:[{name:"name",customLabel:this.translate("name","fields"),notSortable:!0},{name:"acceptanceStatus",width:40,customLabel:this.translate("acceptanceStatus","fields","Meeting"),notSortable:!0,view:"views/fields/enum",params:{options:this.model.getFieldParam("acceptanceStatus","options"),style:this.model.getFieldParam("acceptanceStatus","style")}}]})}).then(e=>{this.collection.models.filter(e=>{var t=e.get("acceptanceStatus");return!t||"None"===t}).forEach(e=>{this.getListView().checkRecord(e.id)});this.listenTo(e,"check",()=>this.controlSendButton());this.controlSendButton()}))},controlSendButton:function(){this.getListView().checkedList.length?this.enableButton("send"):this.disableButton("send")},getListView:function(){return this.getView("list")},actionSend:function(){this.disableButton("send");Espo.Ui.notifyWait();var e=this.getListView().checkedList.map(e=>({entityType:this.collection.get(e).entityType,id:e}));Espo.Ajax.postRequest(this.model.entityType+"/action/sendInvitations",{id:this.model.id,targets:e}).then(e=>{e?Espo.Ui.success(this.translate("Sent")):Espo.Ui.warning(this.translate("nothingHasBeenSent","messages","Meeting"));this.trigger("sent");this.close()}).catch(()=>{this.enableButton("send")})}})});define("crm:views/meeting/modals/send-cancellation",["views/modal","collection"],function(e,t){return e.extend({backdrop:!0,templateContent:`
            <div class="margin-bottom">
                <p>{{message}}</p>
            </div>
            <div class="list-container">{{{list}}}</div>
        `,data:function(){return{message:this.translate("sendCancellationsToSelectedAttendees","messages","Meeting")}},setup:function(){e.prototype.setup.call(this);this.shortcutKeys={};this.shortcutKeys["Control+Enter"]=e=>{if(this.hasAvailableActionItem("send")){e.preventDefault();this.actionSend()}};this.$header=$("<span>").append($("<span>").text(this.translate(this.model.entityType,"scopeNames")),' <span class="chevron-right"></span> ',$("<span>").text(this.model.get("name")),' <span class="chevron-right"></span> ',$("<span>").text(this.translate("Send Cancellation","labels","Meeting")));this.addButton({label:"Send",name:"send",style:"danger",disabled:!0});this.addButton({label:"Cancel",name:"cancel"});this.collection=new t;this.collection.url=this.model.entityType+`/${this.model.id}/attendees`;this.wait(this.collection.fetch().then(()=>{Espo.Utils.clone(this.collection.models).forEach(e=>{e.entityType=e.get("_scope");e.get("emailAddress")||this.collection.remove(e.id)});return this.createView("list","views/record/list",{selector:".list-container",collection:this.collection,rowActionsDisabled:!0,massActionsDisabled:!0,checkAllResultDisabled:!0,selectable:!0,buttonsDisabled:!0,listLayout:[{name:"name",customLabel:this.translate("name","fields"),notSortable:!0},{name:"acceptanceStatus",width:40,customLabel:this.translate("acceptanceStatus","fields","Meeting"),notSortable:!0,view:"views/fields/enum",params:{options:this.model.getFieldParam("acceptanceStatus","options"),style:this.model.getFieldParam("acceptanceStatus","style")}}]})}).then(e=>{this.collection.models.filter(e=>e.id!==this.getUser().id||"User"!==e.entityType).forEach(e=>{this.getListView().checkRecord(e.id)});this.listenTo(e,"check",()=>this.controlSendButton());this.controlSendButton()}))},controlSendButton:function(){this.getListView().checkedList.length?this.enableButton("send"):this.disableButton("send")},getListView:function(){return this.getView("list")},actionSend:function(){this.disableButton("send");Espo.Ui.notifyWait();var e=this.getListView().checkedList.map(e=>({entityType:this.collection.get(e).entityType,id:e}));Espo.Ajax.postRequest(this.model.entityType+"/action/sendCancellation",{id:this.model.id,targets:e}).then(e=>{e?Espo.Ui.success(this.translate("Sent")):Espo.Ui.warning(this.translate("nothingHasBeenSent","messages","Meeting"));this.trigger("sent");this.close()}).catch(()=>{this.enableButton("send")})}})});define("modules/crm/views/meeting/modals/detail",["exports","moment","views/modals/detail"],function(e,s,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;s=i(s);t=i(t);function i(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{duplicateAction=!0;setup(){super.setup();this.setupStatuses()}setupStatuses(){this.notActualStatusList||(this.notActualStatusList=[...this.getMetadata().get(`scopes.${this.entityType}.completedStatusList`)||[],...this.getMetadata().get(`scopes.${this.entityType}.canceledStatusList`)||[]])}setupAfterModelCreated(){super.setupAfterModelCreated();var e=this.getAcceptanceButtonData();this.addButton({name:"setAcceptanceStatus",html:e.html,hidden:this.hasAcceptanceStatusButton(),style:e.style,className:"btn-text",pullLeft:!0,onClick:()=>this.actionSetAcceptanceStatus()},"cancel");if(!this.getAcl().getScopeForbiddenFieldList(this.model.entityType).includes("status")){this.addDropdownItem({name:"setHeld",text:this.translate("Set Held","labels",this.model.entityType),hidden:!0});this.addDropdownItem({name:"setNotHeld",text:this.translate("Set Not Held","labels",this.model.entityType),hidden:!0})}this.addDropdownItem({name:"sendInvitations",text:this.translate("Send Invitations","labels","Meeting"),hidden:!this.isSendInvitationsToBeDisplayed(),onClick:()=>this.actionSendInvitations()});this.initAcceptanceStatus();this.on("switch-model",(e,t)=>{this.stopListening(t,"sync");this.initAcceptanceStatus()});this.on("after:save",()=>{this.hasAcceptanceStatusButton()?this.showAcceptanceButton():this.hideAcceptanceButton();this.isSendInvitationsToBeDisplayed()?this.showActionItem("sendInvitations"):this.hideActionItem("sendInvitations")});this.listenTo(this.model,"sync",()=>{this.isSendInvitationsToBeDisplayed()?this.showActionItem("sendInvitations"):this.hideActionItem("sendInvitations")});this.listenTo(this.model,"after:save",()=>{this.isSendInvitationsToBeDisplayed()?this.showActionItem("sendInvitations"):this.hideActionItem("sendInvitations")})}controlRecordButtonsVisibility(){super.controlRecordButtonsVisibility();this.controlStatusActionVisibility()}controlStatusActionVisibility(){this.setupStatuses();if(this.getAcl().check(this.model,"edit")&&!this.notActualStatusList.includes(this.model.get("status"))){this.showActionItem("setHeld");this.showActionItem("setNotHeld")}else{this.hideActionItem("setHeld");this.hideActionItem("setNotHeld")}}initAcceptanceStatus(){this.hasAcceptanceStatusButton()?this.showAcceptanceButton():this.hideAcceptanceButton();this.listenTo(this.model,"sync",()=>{this.hasAcceptanceStatusButton()?this.showAcceptanceButton():this.hideAcceptanceButton()})}getAcceptanceButtonData(){var e=this.model.getLinkMultipleColumn("users","status",this.getUser().id);let t,i="default",s=null;if(e&&"None"!==e){t=this.getLanguage().translateOption(e,"acceptanceStatus",this.model.entityType);i=this.getMetadata().get(["entityDefs",this.model.entityType,"fields","acceptanceStatus","style",e]);if(i){var a={success:"fas fa-check-circle",danger:"fas fa-times-circle",warning:"fas fa-question-circle"}[i];s=$("<span>").addClass(a).addClass("text-"+i).get(0).outerHTML}}else t=void 0!==e?this.translate("Acceptance","labels","Meeting"):" ";let n=this.getHelper().escapeString(t);s&&(n=s+" "+n);return{style:i,text:t,html:n}}showAcceptanceButton(){this.showActionItem("setAcceptanceStatus");if(this.isRendered()){var e=this.getAcceptanceButtonData(),t=this.$el.find('.modal-footer [data-name="setAcceptanceStatus"]');t.html(e.html);t.removeClass("btn-default");t.removeClass("btn-success");t.removeClass("btn-warning");t.removeClass("btn-info");t.removeClass("btn-primary");t.removeClass("btn-danger");t.addClass("btn-"+e.style)}else this.once("after:render",this.showAcceptanceButton,this)}hideAcceptanceButton(){this.hideActionItem("setAcceptanceStatus")}hasAcceptanceStatusButton(){return!(!this.model.has("status")||!this.model.has("usersIds")||this.notActualStatusList.includes(this.model.get("status"))||!~this.model.getLinkMultipleIdList("users").indexOf(this.getUser().id))}actionSetAcceptanceStatus(){this.createView("dialog","crm:views/meeting/modals/acceptance-status",{model:this.model},e=>{e.render();this.listenTo(e,"set-status",e=>{this.hideAcceptanceButton();Espo.Ui.notifyWait();Espo.Ajax.postRequest(this.model.entityType+"/action/setAcceptanceStatus",{id:this.model.id,status:e}).then(()=>{this.model.fetch().then(()=>{Espo.Ui.notify(!1);setTimeout(()=>{this.$el.find('button[data-name="setAcceptanceStatus"]').focus()},50)})})})})}actionSetHeld(){this.model.save({status:"Held"});this.trigger("after:save",this.model)}actionSetNotHeld(){this.model.save({status:"Not Held"});this.trigger("after:save",this.model)}isSendInvitationsToBeDisplayed(){var e,t,i;return!(this.notActualStatusList.includes(this.model.get("status"))||(e=this.model.get("dateEnd"),e&&this.getDateTime().toMoment(e).isBefore(s.default.now()))||!this.getAcl().checkModel(this.model,"edit")||(e=this.model.getLinkMultipleIdList("users"),t=this.model.getLinkMultipleIdList("contacts"),i=this.model.getLinkMultipleIdList("leads"),!(t.length||i.length||e.length)))}actionSendInvitations(){Espo.Ui.notifyWait();this.createView("dialog","crm:views/meeting/modals/send-invitations",{model:this.model}).then(e=>{Espo.Ui.notify(!1);e.render();this.listenToOnce(e,"sent",()=>this.model.fetch())})}}e.default=a});define("crm:views/meeting/modals/acceptance-status",["views/modal"],function(t){return t.extend({backdrop:!0,templateContent:`
            <div class="margin-bottom">
            <p>{{viewObject.message}}</p>
            </div>
            <div>
                {{#each viewObject.statusDataList}}
                <div class="margin-bottom">
                    <div>
                        <button
                            class="action btn btn-{{style}} btn-x-wide"
                            type="button"
                            data-action="setStatus"
                            data-status="{{name}}"
                        >
                        {{label}}
                        </button>
                        {{#if selected}}<span class="check-icon fas fa-check" style="vertical-align: middle; margin: 0 10px;"></span>{{/if}}
                    </div>
                </div>
                {{/each}}
            </div>
        `,setup:function(){t.prototype.setup.call(this);this.$header=$("<span>").append($("<span>").text(this.translate(this.model.entityType,"scopeNames")),' <span class="chevron-right"></span> ',$("<span>").text(this.model.get("name")),' <span class="chevron-right"></span> ',$("<span>").text(this.translate("Acceptance","labels","Meeting")));var e=this.getMetadata().get(["entityDefs",this.model.entityType,"fields","acceptanceStatus","options"])||[];this.statusDataList=[];e.filter(e=>"None"!==e).forEach(e=>{var t={name:e,style:this.getMetadata().get(["entityDefs",this.model.entityType,"fields","acceptanceStatus","style",e])||"default",label:this.getLanguage().translateOption(e,"acceptanceStatus",this.model.entityType),selected:this.model.getLinkMultipleColumn("users","status",this.getUser().id)===e};this.statusDataList.push(t)});this.message=this.translate("selectAcceptanceStatus","messages","Meeting")},actionSetStatus:function(e){this.trigger("set-status",e.status);this.close()}})});define("modules/crm/views/meeting/fields/users",["exports","modules/crm/views/meeting/fields/attendees"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{selectPrimaryFilterName="active";init(){this.assignmentPermission=this.getAcl().getPermissionLevel("assignmentPermission");"no"===this.assignmentPermission&&(this.readOnly=!0);super.init()}getSelectBoolFilterList(){if("team"===this.assignmentPermission)return["onlyMyTeam"]}getIconHtml(e){return this.getHelper().getAvatarHtml(e,"small",18,"avatar-link")}prepareEditItemElement(e,t){var i=super.prepareEditItemElement(e,t),s=this.getHelper().getAvatarHtml(e,"small",18,"avatar-link");if(s){var s=(new DOMParser).parseFromString(s,"text/html").body.childNodes[0],a=i.children[0].querySelector(".link-item-name");a&&a.prepend(s)}return i}}e.default=s});define("modules/crm/views/meeting/fields/reminders",["exports","ui/select","moment","views/fields/base"],function(e,o,i,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;o=s(o);i=s(i);t=s(t);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{dateField="dateStart";detailTemplate="crm:meeting/fields/reminders/detail";listTemplate="crm:meeting/fields/reminders/detail";editTemplate="crm:meeting/fields/reminders/edit";events={'click [data-action="addReminder"]':function(){var e=this.getMetadata().get("entityDefs.Reminder.fields.type.default"),t=this.getMetadata().get("entityDefs.Reminder.fields.seconds.default")||0,e={type:e,seconds:t};this.reminderList.push(e);this.addItemHtml(e);this.trigger("change");this.focusOnButton()},'click [data-action="removeReminder"]':function(e){var t=$(e.currentTarget).closest(".reminder"),i=t.index();t.remove();this.reminderList.splice(i,1);this.focusOnButton()}};getAttributeList(){return[this.name]}setup(){this.setupReminderList();this.listenTo(this.model,"change:"+this.name,()=>{this.reminderList=Espo.Utils.cloneDeep(this.model.get(this.name)||[])});this.typeList=Espo.Utils.clone(this.getMetadata().get("entityDefs.Reminder.fields.type.options")||[]);this.secondsList=Espo.Utils.clone(this.getMetadata().get("entityDefs.Reminder.fields.seconds.options")||[]);this.dateField=this.model.getFieldParam(this.name,"dateField")||this.dateField;this.listenTo(this.model,"change:"+this.dateField,()=>{this.isEditMode()&&this.reRender()})}setupReminderList(){if(this.model.isNew()&&!this.model.get(this.name)&&"Preferences"!==this.model.entityType){let e="defaultReminders";"Task"===this.model.entityType&&(e="defaultRemindersTask");this.reminderList=this.getPreferences().get(e)||[]}else this.reminderList=this.model.get(this.name)||[];this.reminderList=Espo.Utils.cloneDeep(this.reminderList)}afterRender(){if(this.isEditMode()){this.$container=this.$el.find(".reminders-container");this.reminderList.forEach(e=>{this.addItemHtml(e)})}}focusOnButton(){this.$el.find('button[data-action="addReminder"]').get(0).focus({preventScroll:!0})}updateType(e,t){this.reminderList[t].type=e;this.trigger("change")}updateSeconds(e,t){this.reminderList[t].seconds=e;this.trigger("change")}addItemHtml(t){var e=$("<div>").addClass("input-group").addClass("reminder");let i=$("<select>").attr("name","type").attr("data-name","type").addClass("form-control");this.typeList.forEach(e=>{var t=$("<option>").attr("value",e).text(this.getLanguage().translateOption(e,"reminderTypes"));i.append(t)});i.val(t.type).addClass("radius-left");i.on("change",()=>{this.updateType(i.val(),i.closest(".reminder").index())});let s=$("<select>").attr("name","seconds").attr("data-name","seconds").addClass("form-control radius-right"),n=this.model.get(this.dateField)?this.getDateTime().toMoment(this.model.get(this.dateField)):null;var a=Espo.Utils.clone(this.secondsList);a.includes(t.seconds)||a.push(t.seconds);a.filter(e=>e===t.seconds||!n||this.isBefore(e,n)).sort((e,t)=>e-t).forEach(e=>{var t=$("<option>").attr("value",e).text(this.stringifySeconds(e));s.append(t)});s.val(t.seconds);s.on("change",()=>{var e=parseInt(s.val()),t=s.closest(".reminder").index();this.updateSeconds(e,t)});a=$("<button>").addClass("btn").addClass("btn-link").css("margin-left","5px").attr("type","button").attr("data-action","removeReminder").html('<span class="fas fa-times"></span>');e.append($('<div class="input-group-item">').append(i)).append($('<div class="input-group-item">').append(s)).append($('<div class="input-group-btn">').append(a));this.$container.append(e);o.default.init(i,{});o.default.init(s,{sortBy:"$score",sortDirection:"desc",score:(e,t)=>{var i,s=parseInt(t.value),a=parseInt(e);return!isNaN(a)&&(i=Number.MAX_SAFE_INTEGER-s,0===a&&0===s||60*a===s||60*a*60===s||60*a*60*24===s)?i:0},load:(e,t)=>{var i=parseInt(e);if(!(isNaN(i)||i<0||59<i)){var s=[],a=60*i;if(this.isBefore(a,n)){s.push({value:a.toString(),text:this.stringifySeconds(a)});if(i<=24){a=3600*i;this.isBefore(a,n)&&s.push({value:a.toString(),text:this.stringifySeconds(a)})}if(i<=30){a=3600*i*24;this.isBefore(a,n)&&s.push({value:a.toString(),text:this.stringifySeconds(a)})}t(s)}}}})}isBefore(e,t){return i.default.utc().add(e,"seconds").isBefore(t)}stringifySeconds(e){if(!e)return this.translate("on time","labels","Meeting");var t=e,i=Math.floor(t/86400),s=(t%=86400,Math.floor(t/3600)),a=(t%=3600,Math.floor(t/60)),t=t%60,n=[];i&&n.push(i+""+this.getLanguage().translate("d","durationUnits"));s&&n.push(s+""+this.getLanguage().translate("h","durationUnits"));a&&n.push(a+""+this.getLanguage().translate("m","durationUnits"));t&&n.push(t+""+this.getLanguage().translate("s","durationUnits"));return n.join(" ")+" "+this.translate("before","labels","Meeting")}getDetailItemHtml(e){return $("<div>").append($("<span>").text(this.getLanguage().translateOption(e.type,"reminderTypes"))," ",$("<span>").text(this.stringifySeconds(e.seconds))).get(0).outerHTML}getValueForDisplay(){if(this.isDetailMode()||this.isListMode()){let t="";this.reminderList.forEach(e=>{t+=this.getDetailItemHtml(e)});return t}}fetch(){var e={};e[this.name]=Espo.Utils.cloneDeep(this.reminderList);return e}}e.default=a});define("modules/crm/views/meeting/fields/date-start",["exports","views/fields/datetime-optional","moment"],function(e,t,n){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);n=i(n);function i(e){return e&&e.__esModule?e:{default:e}}class s extends t.default{emptyTimeInInlineEditDisabled=!0;setup(){super.setup();this.noneOption=this.translate("All-Day","labels","Meeting");this.notActualStatusList=[...this.getMetadata().get(`scopes.${this.entityType}.completedStatusList`)||[],...this.getMetadata().get(`scopes.${this.entityType}.canceledStatusList`)||[]]}getAttributeList(){return[...super.getAttributeList(),"dateEnd","dateEndDate","status"]}data(){let e;var t=this.model.get("status");!t||this.notActualStatusList.includes(t)||this.mode!==this.MODE_DETAIL&&this.mode!==this.MODE_LIST||(this.isDateInPast("dateEnd")?e="danger":this.isDateInPast("dateStart",!0)&&(e="warning"));return{...super.data(),style:e}}isDateInPast(t,i){if(this.isDate()){let e=this.model.get(t+"Date");if(e){var s=i?e+" 00:00":e+" 23:59",s=n.default.tz(s,this.getDateTime().getTimeZone()),a=this.getDateTime().getNowMoment();if(s.unix()<a.unix())return!0}}else{let e=this.model.get(t);if(e){s=this.getDateTime().toMoment(e),a=(0,n.default)().tz(this.getDateTime().timeZone||"UTC");if(s.unix()<a.unix())return!0}}return!1}afterRender(){super.afterRender();this.isEditMode()&&this.controlTimePartVisibility()}fetch(){var e=super.fetch();e[this.nameDate]?e.isAllDay=!0:e.isAllDay=!1;return e}controlTimePartVisibility(){if(this.isEditMode()&&this.isInlineEditMode())if(this.model.get("isAllDay")){this.$time.addClass("hidden");this.$el.find(".time-picker-btn").addClass("hidden")}else{this.$time.removeClass("hidden");this.$el.find(".time-picker-btn").removeClass("hidden")}}}e.default=s});define("crm:views/meeting/fields/date-end",["views/fields/datetime-optional"],function(e){return e.extend({validateAfterAllowSameDay:!0,emptyTimeInInlineEditDisabled:!0,noneOptionIsHidden:!0,isEnd:!0,setup:function(){e.prototype.setup.call(this);this.isAllDayValue=this.model.get("isAllDay");this.listenTo(this.model,"change:isAllDay",(e,t,i)=>{if(i.ui&&this.isEditMode())if(void 0!==this.isAllDayValue||t){this.isAllDayValue=t;if(t)this.$time.val(this.noneOption);else{let e=this.model.get("dateStart");e=e||this.getDateTime().getNow(5);var s=this.getDateTime().toMoment(e),s=(e=s.format(this.getDateTime().getDateTimeFormat()),e.indexOf(" ")),s=e.substring(s+1);this.model.get("dateEnd")&&this.$time.val(s)}this.trigger("change");this.controlTimePartVisibility()}else this.isAllDayValue=t})},afterRender:function(){e.prototype.afterRender.call(this);this.isEditMode()&&this.controlTimePartVisibility()},controlTimePartVisibility:function(){if(this.isEditMode())if(this.model.get("isAllDay")){this.$time.addClass("hidden");this.$el.find(".time-picker-btn").addClass("hidden")}else{this.$time.removeClass("hidden");this.$el.find(".time-picker-btn").removeClass("hidden")}}})});define("modules/crm/views/meeting/fields/contacts",["exports","modules/crm/views/meeting/fields/attendees"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/meeting/fields/acceptance-status",["exports","views/fields/enum"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{searchTypeList=["anyOf","noneOf"];fetchSearch(){var e=super.fetchSearch();e&&"noneOf"===e.data.type&&e.value&&1<e.value.length&&(e.value=[e.value[0]]);return e}}e.default=s});define("modules/crm/views/mass-email/detail",["exports","views/detail","crm:views/mass-email/modals/send-test"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{setup(){super.setup();["Draft","Pending"].includes(this.model.attributes.status)&&this.getAcl().checkModel(this.model,"edit")&&this.addMenuItem("buttons",{label:"Send Test",action:"sendTest",acl:"edit",onClick:()=>this.actionSendTest()})}async actionSendTest(){var e=new i.default({model:this.model});await this.assignView("modal",e);await e.render()}}e.default=a});define("modules/crm/views/mass-email/record/list-for-campaign",["exports","views/record/list","crm:views/mass-email/modals/send-test"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{async actionSendTest(e){var t=e.id,t=this.collection.get(t);if(t){t=new i.default({model:t});await this.assignView("modal",t);await t.render()}}}e.default=a});define("crm:views/mass-email/record/edit-small",["views/record/edit-small","crm:views/mass-email/record/edit"],function(e,t){return e.extend({setup:function(){e.prototype.setup.call(this);t.prototype.initFieldsControl.call(this)}})});define("crm:views/mass-email/record/detail",["views/record/detail"],function(e){return e.extend({duplicateAction:!0,bottomView:"crm:views/mass-email/record/detail-bottom"})});define("crm:views/mass-email/record/detail-bottom",["views/record/detail-bottom"],function(e){return e.extend({setupPanels:function(){e.prototype.setupPanels.call(this);this.panelList.unshift({name:"queueItems",label:this.translate("queueItems","links","MassEmail"),view:"views/record/panels/relationship",select:!1,create:!1,layout:"listForMassEmail",rowActionsView:"views/record/row-actions/empty",filterList:["all","pending","sent","failed"]})},afterRender:function(){e.prototype.setupPanels.call(this)}})});define("crm:views/mass-email/record/row-actions/for-campaign",["views/record/row-actions/relationship-no-unlink"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);this.options.acl.edit&&!~["Complete"].indexOf(this.model.get("status"))&&e.unshift({action:"sendTest",label:"Send Test",data:{id:this.model.id}});return e}})});define("crm:views/mass-email/fields/smtp-account",["views/lead-capture/fields/smtp-account"],function(e){return e.extend({dataUrl:"MassEmail/action/smtpAccountDataList"})});define("crm:views/mass-email/fields/from-address",["views/fields/varchar"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.model.isNew()&&!this.model.has("fromAddress")&&this.model.set("fromAddress",this.getConfig().get("outboundEmailFromAddress"));this.model.isNew()&&!this.model.has("fromName")&&this.model.set("fromName",this.getConfig().get("outboundEmailFromName"))}})});define("crm:views/mass-email/fields/email-template",["views/fields/link"],function(e){return e.extend({getCreateAttributes:function(){return{oneOff:!0}}})});define("modules/crm/views/lead/detail",["exports","views/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{setup(){super.setup();this.addMenuItem("buttons",{name:"convert",action:"convert",label:"Convert",acl:"edit",hidden:!this.isConvertable(),onClick:()=>this.actionConvert()});this.listenTo(this.model,"sync",()=>{this.isConvertable()?this.showHeaderActionItem("convert"):this.hideHeaderActionItem("convert")})}isConvertable(){var e=[...this.getMetadata().get("entityDefs.Lead.fields.status.notActualOptions")||[],"Converted"];return!e.includes(this.model.get("status"))&&this.model.has("status")}actionConvert(){this.getRouter().navigate(this.model.entityType+"/convert/"+this.model.id,{trigger:!0})}}e.default=s});define("modules/crm/views/lead/convert",["exports","views/main"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{template="crm:lead/convert";data(){return{scopeList:this.scopeList,scope:this.scope}}setup(){this.scope="Lead";this.addHandler("change","input.check-scope",(e,t)=>{var i=t.dataset.scope,i=this.$el.find(".edit-container-"+Espo.Utils.toDom(i));t.checked?i.removeClass("hide"):i.addClass("hide")});this.addActionHandler("convert",()=>this.convert());this.addActionHandler("cancel",()=>{this.getRouter().navigate("#Lead/view/"+this.id,{trigger:!0})});this.createView("header","views/header",{model:this.model,fullSelector:"#main > .header",scope:this.scope,fontSizeFlexible:!0});this.wait(!0);this.id=this.options.id;Espo.Ui.notifyWait();this.getModelFactory().create("Lead",e=>{this.model=e;e.id=this.id;this.listenToOnce(e,"sync",()=>this.build());e.fetch()})}build(){let a=this.scopeList=[],n=((this.getMetadata().get("entityDefs.Lead.convertEntityList")||[]).forEach(e=>{"Account"===e&&this.getConfig().get("b2cMode")||this.getMetadata().get(["scopes",e,"disabled"])||this.getAcl().check(e,"create")&&a.push(e)}),0);0===a.length?this.wait(!1):Espo.Ajax.postRequest("Lead/action/getConvertAttributes",{id:this.model.id}).then(s=>{a.forEach(i=>{this.getModelFactory().create(i,e=>{e.populateDefaults();e.set(s[i]||{},{silent:!0});var t=this.getMetadata().get(["clientDefs",i,"recordViews","edit"])||"views/record/edit";this.createView(i,t,{model:e,fullSelector:"#main .edit-container-"+Espo.Utils.toDom(i),buttonsPosition:!1,buttonsDisabled:!0,layoutName:"detailConvert",exit:()=>{}},()=>{n++;if(n===a.length){this.wait(!1);Espo.Ui.notify(!1)}})})})})}convert(){let a=[];this.scopeList.forEach(e=>{var t=this.$el.find(`input[data-scope="${e}"]`).get(0);t&&t.checked&&a.push(e)});if(0===a.length)Espo.Ui.error(this.translate("selectAtLeastOneRecord","messages"));else{this.getRouter().confirmLeaveOut=!1;let i=!1,t=(a.forEach(e=>{var t=this.getView(e);t.setConfirmLeaveOut(!1);t.model.set(t.fetch());i=t.validate()||i}),{id:this.model.id,records:{}}),s=(a.forEach(e=>{t.records[e]=this.getView(e).model.attributes}),i=>{this.$el.find('[data-action="convert"]').addClass("disabled");Espo.Ui.notifyWait();Espo.Ajax.postRequest("Lead/action/convert",i).then(()=>{this.getRouter().confirmLeaveOut=!1;this.getRouter().navigate("#Lead/view/"+this.model.id,{trigger:!0});Espo.Ui.notify(this.translate("Converted","labels","Lead"))}).catch(t=>{Espo.Ui.notify(!1);this.$el.find('[data-action="convert"]').removeClass("disabled");if(409===t.status&&"duplicate"===t.getResponseHeader("X-Status-Reason")){let e=null;try{e=JSON.parse(t.responseText)}catch(e){console.error("Could not parse response header.");return}t.errorIsHandled=!0;this.createView("duplicate","views/modals/duplicate",{duplicates:e},e=>{e.render();this.listenToOnce(e,"save",()=>{i.skipDuplicateCheck=!0;s(i)})})}})});i?Espo.Ui.error(this.translate("Not valid")):s(t)}}getHeader(){var e=this.getHeaderIconHtml(),t=this.getLanguage().translate(this.model.entityType,"scopeNamesPlural"),t=$("<span>").append($("<a>").attr("href","#Lead").text(t)),e=(e&&t.prepend(e),this.model.get("name")||this.model.id),i=`#${this.model.entityType}/view/`+this.model.id,i=$("<a>").attr("href",i).addClass("action").append($("<span>").text(e));return this.buildHeaderHtml([t,i,$("<span>").text(this.translate("convert","labels","Lead"))])}}e.default=s});define("modules/crm/views/lead/record/detail",["exports","views/record/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{selfAssignAction=!0;sideView="crm:views/lead/record/detail-side";getSelfAssignAttributes(){if("New"===this.model.attributes.status){var e=this.getMetadata().get(["entityDefs","Lead","fields","status","options"])||[];if(e.includes("Assigned"))return{status:"Assigned"}}return{}}}e.default=s});define("crm:views/lead/record/detail-side",["views/record/detail-side"],function(e){return e.extend({setupPanels:function(){}})});define("crm:views/lead/record/panels/converted-to",["views/record/panels/side"],function(e){return e.extend({setupFields:function(){this.fieldList=[];this.getAcl().check("Account")&&!this.getMetadata().get("scopes.Account.disabled")&&this.fieldList.push("createdAccount");this.getAcl().check("Contact")&&!this.getMetadata().get("scopes.Contact.disabled")&&this.fieldList.push("createdContact");this.getAcl().check("Opportunity")&&!this.getMetadata().get("scopes.Opportunity.disabled")&&this.fieldList.push("createdOpportunity")}})});define("crm:views/lead/fields/industry",["views/fields/enum"],function(e){return e.extend({})});define("crm:views/lead/fields/created-opportunity",["views/fields/link"],function(e){return e.extend({getSelectFilters:function(){if(this.model.get("createdAccountId"))return{account:{type:"equals",attribute:"accountId",value:this.model.get("createdAccountId"),data:{type:"is",nameValue:this.model.get("createdAccountName")}}}}})});define("crm:views/lead/fields/created-contact",["views/fields/link"],function(e){return e.extend({getSelectFilters:function(){if(this.model.get("createdAccountId"))return{account:{type:"equals",attribute:"accountId",value:this.model.get("createdAccountId"),data:{type:"is",nameValue:this.model.get("createdAccountName")}}}}})});define("crm:views/lead/fields/acceptance-status",["views/fields/enum-column"],function(e){return e.extend({searchTypeList:["anyOf","noneOf"],setup:function(){this.params.options=this.getMetadata().get("entityDefs.Meeting.fields.acceptanceStatus.options");this.params.translation="Meeting.options.acceptanceStatus";e.prototype.setup.call(this)}})});define("crm:views/knowledge-base-article/list",["views/list-with-categories"],function(e){return e.extend({categoryScope:"KnowledgeBaseCategory"})});define("crm:views/knowledge-base-article/record/list",["views/record/list"],function(e){return e.extend({})});define("crm:views/knowledge-base-article/record/edit",["views/record/edit"],function(e){return e.extend({saveAndContinueEditingAction:!0})});define("crm:views/knowledge-base-article/record/edit-quick",["views/record/edit-small"],function(e){return e.extend({isWide:!0,sideView:!1})});define("modules/crm/views/knowledge-base-article/record/detail",["exports","modules/crm/knowledge-base-helper","views/record/detail"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends i.default{saveAndContinueEditingAction=!0;setup(){super.setup();this.getUser().isPortal()&&(this.sideDisabled=!0);this.getAcl().checkScope("Email","create")&&this.dropdownItemList.push({label:"Send in Email",name:"sendInEmail"});if(this.getUser().isPortal()&&!this.getAcl().checkScope(this.scope,"edit")&&!this.model.getLinkMultipleIdList("attachments").length){this.hideField("attachments");this.listenToOnce(this.model,"sync",()=>{this.model.getLinkMultipleIdList("attachments").length&&this.showField("attachments")})}}actionSendInEmail(){Espo.Ui.notifyWait();var e=new t.default(this.getLanguage());e.getAttributesForEmail(this.model,{},e=>{var t=this.getMetadata().get("clientDefs.Email.modalViews.compose")||"views/modals/compose-email";this.createView("composeEmail",t,{attributes:e,selectTemplateDisabled:!0,signatureDisabled:!0},e=>{Espo.Ui.notify(!1);e.render()})})}}e.default=a});define("crm:views/knowledge-base-article/record/detail-quick",["views/record/detail-small"],function(e){return e.extend({isWide:!0,sideView:!1})});define("crm:views/knowledge-base-article/modals/select-records",["views/modals/select-records-with-categories"],function(e){return e.extend({categoryScope:"KnowledgeBaseCategory"})});define("crm:views/knowledge-base-article/fields/status",["views/fields/enum"],function(t){return t.extend({setup:function(){t.prototype.setup.call(this);var e=!1;this.on("change",()=>{if("Published"===this.model.get("status")){if(!this.model.get("publishDate")){e=!0;this.model.set("publishDate",this.getDateTime().getToday())}}else e&&this.model.set("publishDate",null)})}})});define("crm:views/knowledge-base-article/fields/language",["views/fields/enum"],function(e){return e.extend({setupOptions:function(){this.params.options=Espo.Utils.clone(this.getMetadata().get(["app","language","list"])||[]);this.params.options.unshift("");this.translatedOptions=Espo.Utils.clone(this.getLanguage().translate("language","options")||{});this.translatedOptions[""]=this.translate("Any","labels","KnowledgeBaseArticle")}})});define("crm:views/fields/ico",["views/fields/base"],function(e){return e.extend({templateContent:`{{! ~}}
            <span
                class="{{iconClass}} text-muted action icon"
                style="cursor: pointer"
                title="{{viewLabel}}"
                data-action="quickView"
                data-id="{{id}}"
                {{#if notRelationship}}data-scope="{{scope}}"{{/if}}
            ></span>
        {{~!}}`,data:function(){return{notRelationship:this.params.notRelationship,viewLabel:this.translate("View"),id:this.model.id,scope:this.model.entityType,iconClass:this.getMetadata().get(["clientDefs",this.model.entityType,"iconClass"])||"far fa-calendar-times"}}})});define("crm:views/event-confirmation/confirmation",["view"],function(e){return e.extend({template:"crm:event-confirmation/confirmation",data:function(){var e=this.actionData.style||"default";return{actionData:this.actionData,style:e,dateStart:this.actionData.dateStart?this.convertDateTime(this.actionData.dateStart):null,sentDateStart:this.actionData.sentDateStart?this.convertDateTime(this.actionData.sentDateStart):null,dateStartChanged:this.actionData.sentDateStart&&this.actionData.dateStart!==this.actionData.sentDateStart,actionDataList:this.getActionDataList()}},setup:function(){this.actionData=this.options.actionData},getActionDataList:function(){let i={Accepted:"accept",Declined:"decline",Tentative:"tentative"};var e=["Accepted","Tentative","Declined"];if(!e.includes(this.actionData.status))return null;let s=window.location.href.replace("action="+i[this.actionData.status],"action={action}");return e.map(e=>{var t=e===this.actionData.status;return{active:t,link:t?"":s.replace("{action}",i[e]),label:this.actionData.statusTranslation[e]}})},convertDateTime:function(e){var t=this.getConfig().get("timeZone"),t=this.getDateTime().toMoment(e).tz(t);return t.format(this.getDateTime().getDateTimeFormat())+" "+t.format("Z z")}})});define("crm:views/email-queue-item/list",["views/list"],function(e){return e.extend({createButton:!1})});define("crm:views/email-queue-item/record/list",["views/record/list"],function(e){return e.extend({rowActionsView:"views/record/row-actions/remove-only"})});define("crm:views/document/list",["views/list-with-categories"],function(e){return e.extend({categoryScope:"DocumentFolder"})});define("crm:views/document/modals/select-records",["views/modals/select-records-with-categories"],function(e){return e.extend({categoryScope:"DocumentFolder"})});define("crm:views/document/fields/name",["views/fields/varchar"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.model.isNew()&&this.listenTo(this.model,"change:fileName",()=>{this.model.set("name",this.model.get("fileName"))})}})});define("crm:views/document/fields/file",["views/fields/file"],function(i){return i.extend({getValueForDisplay:function(){var e,t;return this.isListMode()?(e=this.model.get(this.nameName),t=this.model.get(this.idName),t?$("<a>").attr("title",e).attr("href",this.getBasePath()+"?entryPoint=download&id="+t).attr("target","_BLANK").append($("<span>").addClass("fas fa-paperclip small")).get(0).outerHTML:""):i.prototype.getValueForDisplay.call(this)}})});define("crm:views/dashlets/tasks",["views/dashlets/abstract/record-list"],function(e){return e.extend({listView:"crm:views/task/record/list-expanded",rowActionsView:"crm:views/task/record/row-actions/dashlet"})});define("crm:views/dashlets/meetings",["views/dashlets/abstract/record-list"],function(e){return e.extend({name:"Meetings",scope:"Meeting",listView:"crm:views/meeting/record/list-expanded",rowActionsView:"crm:views/meeting/record/row-actions/dashlet"})});define("crm:views/dashlets/calls",["views/dashlets/abstract/record-list"],function(e){return e.extend({name:"Calls",scope:"Call",listView:"crm:views/call/record/list-expanded",rowActionsView:"crm:views/call/record/row-actions/dashlet"})});define("modules/crm/views/dashlets/calendar",["exports","views/dashlets/abstract/base"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{name="Calendar";noPadding=!0;templateContent='<div class="calendar-container">{{{calendar}}}</div>';afterRender(){var i=this.getOption("mode");if("timeline"===i){let t=[];var s=this.getOption("usersIds")||[];let i=this.getOption("usersNames")||{},e=(s.forEach(e=>{t.push({id:e,name:i[e]||e})}),this.getMetadata().get(["clientDefs","Calendar","timelineView"])||"crm:views/calendar/timeline");this.createView("calendar",e,{selector:"> .calendar-container",header:!1,calendarType:"shared",userList:t,enabledScopeList:this.getOption("enabledScopeList"),noFetchLoadingMessage:!0},e=>{e.render()})}else{let e=null,t=(["basicWeek","month","basicDay"].includes(i)&&(e=this.getOption("teamsIds")),this.getMetadata().get(["clientDefs","Calendar","calendarView"])||"crm:views/calendar/calendar");this.createView("calendar",t,{mode:i,selector:"> .calendar-container",header:!1,enabledScopeList:this.getOption("enabledScopeList"),containerSelector:this.getSelector(),teamIdList:e,scrollToNowSlots:3,suppressLoadingAlert:!0},i=>{this.listenTo(i,"view",()=>{if("month"===this.getOption("mode")){var e=this.getOption("title"),e=$("<span>").append($("<span>").text(e),' <span class="chevron-right"></span> ',$("<span>").text(i.getTitle())),t=this.$el.closest(".panel").find(".panel-heading > .panel-title > span");t.html(e.get(0).innerHTML)}});i.render();this.on("resize",()=>{setTimeout(()=>i.adjustSize(),50)})})}}setupActionList(){this.actionList.unshift({name:"viewCalendar",text:this.translate("View Calendar","labels","Calendar"),url:"#Calendar",iconHtml:'<span class="far fa-calendar-alt"></span>',onClick:()=>this.actionViewCalendar()})}setupButtonList(){if("timeline"!==this.getOption("mode")){this.buttonList.push({name:"previous",html:'<span class="fas fa-chevron-left"></span>',onClick:()=>this.actionPrevious()});this.buttonList.push({name:"next",html:'<span class="fas fa-chevron-right"></span>',onClick:()=>this.actionNext()})}}getCalendarView(){return this.getView("calendar")}actionRefresh(){var e=this.getCalendarView();e&&e.actionRefresh()}autoRefresh(){var e=this.getCalendarView();e&&e.actionRefresh({suppressLoadingAlert:!0})}actionNext(){var e=this.getCalendarView();e&&e.actionNext()}actionPrevious(){var e=this.getCalendarView();e&&e.actionPrevious()}actionViewCalendar(){this.getRouter().navigate("#Calendar",{trigger:!0})}}e.default=s});define("modules/crm/views/dashlets/activities",["exports","views/dashlets/abstract/base","multi-collection","helpers/record-modal"],function(e,t,i,a){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);a=s(a);function s(e){return e&&e.__esModule?e:{default:e}}class n extends t.default{name="Activities";templateContent='<div class="list-container">{{{list}}}</div>';rowActionsView="crm:views/record/row-actions/activities-dashlet";defaultListLayout={rows:[[{name:"ico",view:"crm:views/fields/ico",params:{notRelationship:!0}},{name:"name",link:!0}],[{name:"dateStart",soft:!0},{name:"parent"}]]};listLayoutEntityTypeMap={Task:{rows:[[{name:"ico",view:"crm:views/fields/ico",params:{notRelationship:!0}},{name:"name",link:!0}],[{name:"status"},{name:"dateEnd",soft:!0},{name:"priority",view:"crm:views/task/fields/priority-for-dashlet"},{name:"parent"}]]}};setup(){this.seeds={};this.scopeList=this.getOption("enabledScopeList")||[];this.listLayout={};this.scopeList.forEach(e=>{e in this.listLayoutEntityTypeMap?this.listLayout[e]=this.listLayoutEntityTypeMap[e]:this.listLayout[e]=this.defaultListLayout});this.wait(!0);let i=0;this.scopeList.forEach(t=>{this.getModelFactory().create(t,e=>{this.seeds[t]=e;i++;i===this.scopeList.length&&this.wait(!1)})});this.scopeList.slice(0).reverse().forEach(e=>{this.getAcl().checkScope(e,"create")&&this.actionList.unshift({name:"createActivity",text:this.translate("Create "+e,"labels",e),iconHtml:'<span class="fas fa-plus"></span>',url:"#"+e+"/create",data:{scope:e}})})}afterRender(){this.collection=new i.default;this.collection.seeds=this.seeds;this.collection.url="Activities/upcoming";this.collection.maxSize=this.getOption("displayRecords")||this.getConfig().get("recordsPerPageSmall")||5;this.collection.data.entityTypeList=this.scopeList;this.collection.data.futureDays=this.getOption("futureDays");this.getOption("includeShared")&&(this.collection.data.includeShared=!0);this.listenToOnce(this.collection,"sync",()=>{this.createView("list","crm:views/record/list-activities-dashlet",{selector:"> .list-container",pagination:!1,type:"list",rowActionsView:this.rowActionsView,checkboxes:!1,collection:this.collection,listLayout:this.listLayout},e=>{e.render()})});this.collection.fetch()}actionRefresh(){this.refreshInternal()}autoRefresh(){this.refreshInternal({skipNotify:!0})}async refreshInternal(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};e.skipNotify||Espo.Ui.notifyWait();await this.collection.fetch({previousTotal:this.collection.total,previousDataList:this.collection.models.map(e=>Espo.Utils.cloneDeep(e.attributes))});e.skipNotify||Espo.Ui.notify()}actionCreateActivity(e){var t=e.scope,i={},s=(this.populateAttributesAssignedUser(t,i),new a.default);s.showCreate(this,{entityType:t,attributes:i,afterSave:()=>{this.actionRefresh()}})}actionCreateMeeting(){var e={},t=(this.populateAttributesAssignedUser("Meeting",e),new a.default);t.showCreate(this,{entityType:"Meeting",attributes:e,afterSave:()=>{this.actionRefresh()}})}actionCreateCall(){var e={},t=(this.populateAttributesAssignedUser("Call",e),new a.default);t.showCreate(this,{entityType:"Call",attributes:e,afterSave:()=>{this.actionRefresh()}})}populateAttributesAssignedUser(e,t){if(this.getMetadata().get(["entityDefs",e,"fields","assignedUsers"])){t.assignedUsersIds=[this.getUser().id];t.assignedUsersNames={};t.assignedUsersNames[this.getUser().id]=this.getUser().get("name")}else{t.assignedUserId=this.getUser().id;t.assignedUserName=this.getUser().get("name")}}}e.default=n});define("crm:views/dashlets/options/sales-pipeline",["crm:views/dashlets/options/chart"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);"own"===this.getAcl().getLevel("Opportunity","read")&&this.hideField("team")}})});define("crm:views/dashlets/options/calendar",["views/dashlets/options/base"],function(e){return e.extend({setup:function(){e.prototype.setup.call(this);this.manageFields();this.listenTo(this.model,"change:mode",this.manageFields,this)},init:function(){e.prototype.init.call(this);this.fields.enabledScopeList.options=this.getConfig().get("calendarEntityList")||[]},manageFields:function(e,t,i){"timeline"===this.model.get("mode")?this.showField("users"):this.hideField("users");if("no"!==this.getAcl().getPermissionLevel("userCalendar")&&~["basicWeek","month","basicDay"].indexOf(this.model.get("mode")))this.showField("teams");else{i&&i.ui&&this.model.set("teamsIds",[]);this.hideField("teams")}}})});define("crm:views/dashlets/options/activities",["views/dashlets/options/base"],function(i){return i.extend({init:function(){i.prototype.init.call(this);var t=[],e=Espo.Utils.clone(this.getConfig().get("activitiesEntityList")||[]);e.push("Task");e.forEach(e=>{this.getMetadata().get(["scopes",e,"disabled"])||this.getAcl().checkScope(e)&&t.push(e)});this.fields.enabledScopeList.options=t}})});define("crm:views/dashlets/options/sales-pipeline/fields/team",["views/fields/link"],function(e){return e.extend({getSelectBoolFilterList:function(){if("team"===this.getAcl().getLevel("Opportunity","read"))return["onlyMy"]}})});define("modules/crm/views/contact/detail",["exports","views/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("crm:views/contact/record/detail-small",["views/record/detail-small","crm:views/contact/record/detail"],function(e,t){return e.extend({})});define("modules/crm/views/contact/modals/select-for-portal-user",["exports","views/modals/select-records"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{constructor(e){super(e);this.options=e}setup(){super.setup();this.buttonList.unshift({name:"skip",text:this.translate("Proceed w/o Contact","labels","User"),onClick:()=>this.actionSkip()})}actionSkip(){this.options.onSkip();this.close()}}e.default=s});define("crm:views/contact/fields/title",["views/fields/varchar"],function(e){return e.extend({setupOptions:function(){this.params.options=Espo.Utils.clone(this.getMetadata().get("entityDefs.Account.fields.contactRole.options")||[])}})});define("crm:views/contact/fields/opportunity-role",["views/fields/enum"],function(e){return e.extend({searchTypeList:["anyOf","noneOf"]})});define("crm:views/contact/fields/name-for-account",["views/fields/person-name"],function(e){return e.extend({afterRender:function(){e.prototype.afterRender.call(this);"listLink"===this.mode&&this.model.get("accountIsInactive")&&this.$el.find("a").css("text-decoration","line-through")},getAttributeList:function(){return["name","accountIsInactive"]}})});define("modules/crm/views/contact/fields/accounts",["exports","views/fields/link-multiple-with-columns"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{getAttributeList(){var e=super.getAttributeList();e.push("accountId");e.push("accountName");e.push("title");return e}setup(){super.setup();this.events['click [data-action="switchPrimary"]']=e=>{var t=$(e.currentTarget),i=t.data("id");if(!t.hasClass("active")){this.$el.find('button[data-action="switchPrimary"]').removeClass("active").children().addClass("text-muted");t.addClass("active").children().removeClass("text-muted");this.setPrimaryId(i)}};this.primaryIdFieldName="accountId";this.primaryNameFieldName="accountName";this.primaryRoleFieldName="title";this.primaryId=this.model.get(this.primaryIdFieldName);this.primaryName=this.model.get(this.primaryNameFieldName);this.listenTo(this.model,"change:"+this.primaryIdFieldName,()=>{this.primaryId=this.model.get(this.primaryIdFieldName);this.primaryName=this.model.get(this.primaryNameFieldName)});(this.isEditMode()||this.isDetailMode())&&(this.events['click a[data-action="setPrimary"]']=e=>{var t=$(e.currentTarget).data("id");this.setPrimaryId(t);this.reRender()})}setPrimaryId(e){this.primaryId=e;this.primaryName=e?this.nameHash[e]:null;this.trigger("change")}renderLinks(){this.primaryId&&this.addLinkHtml(this.primaryId,this.primaryName);this.ids.forEach(e=>{e!==this.primaryId&&this.addLinkHtml(e,this.nameHash[e])})}getValueForDisplay(){if(this.isDetailMode()||this.isListMode()){let t=[];this.primaryId&&t.push(this.getDetailLinkHtml(this.primaryId,this.primaryName));this.ids.forEach(e=>{e!==this.primaryId&&t.push(this.getDetailLinkHtml(e))});return t.map(e=>$("<div>").addClass("link-multiple-item").html(e).get(0).outerHTML).join("")}}getDetailLinkHtml(e,t){var i=super.getDetailLinkHtml(e,t);if(this.getColumnValue(e,"isInactive")){var s=$("<div>").html(i);s.find("a").css("text-decoration","line-through");return s.get(0).innerHTML}return i}afterAddLink(e){super.afterAddLink(e);if(1===this.ids.length){this.primaryId=e;this.primaryName=this.nameHash[e]}this.controlPrimaryAppearance()}afterDeleteLink(e){super.afterDeleteLink(e);if(0===this.ids.length){this.primaryId=null;this.primaryName=null}else{if(e===this.primaryId){this.primaryId=this.ids[0];this.primaryName=this.nameHash[this.primaryId]}this.controlPrimaryAppearance()}}controlPrimaryAppearance(){this.$el.find("li.set-primary-list-item").removeClass("hidden");this.primaryId&&this.$el.find('li.set-primary-list-item[data-id="'+this.primaryId+'"]').addClass("hidden")}addLinkHtml(e,t){t=t||e;if(this.isSearchMode())return super.addLinkHtml(e,t);var i=super.addLinkHtml(e,t),s=e===this.primaryId,a=$("<a>").attr("role","button").attr("tabindex","0").attr("data-action","setPrimary").attr("data-id",e).text(this.translate("Set Primary","labels","Account")),a=$("<li>").addClass("set-primary-list-item").attr("data-id",e).append(a);!s&&1!==this.ids.length||a.addClass("hidden");i.find("ul.dropdown-menu").append(a);this.getColumnValue(e,"isInactive")&&i.find("div.link-item-name").css("text-decoration","line-through")}fetch(){var e=super.fetch();e[this.primaryIdFieldName]=this.primaryId;e[this.primaryNameFieldName]=this.primaryName;e[this.primaryRoleFieldName]=(this.columns[this.primaryId]||{}).role||null;e.accountIsInactive=(this.columns[this.primaryId]||{}).isInactive||!1;if(!this.primaryId){e[this.primaryRoleFieldName]=null;e.accountIsInactive=null}return e}}e.default=s});define("crm:views/contact/fields/account",["views/fields/link"],function(t){return t.extend({getAttributeList:function(){var e=t.prototype.getAttributeList.call(this);e.push("accountIsInactive");return e},afterRender:function(){t.prototype.afterRender.call(this);"list"!==this.mode&&"detail"!==this.mode||this.model.get("accountIsInactive")&&this.$el.find("a").css("textDecoration","line-through")}})});define("crm:views/contact/fields/account-role",["views/fields/varchar"],function(t){return t.extend({detailTemplate:"crm:contact/fields/account-role/detail",listTemplate:"crm:contact/fields/account-role/detail",setup:function(){t.prototype.setup.call(this);this.listenTo(this.model,"change:title",()=>{this.model.set("accountRole",this.model.get("title"))})},getAttributeList:function(){var e=t.prototype.getAttributeList.call(this);e.push("title");e.push("accountIsInactive");return e},data:function(){var e=t.prototype.data.call(this);this.model.has("accountIsInactive")&&(e.accountIsInactive=this.model.get("accountIsInactive"));return e}})});define("modules/crm/views/case/record/detail",["exports","views/record/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{selfAssignAction=!0;getSelfAssignAttributes(){if("New"===this.model.attributes.status){var e=this.getMetadata().get(["entityDefs","Case","fields","status","options"])||[];if(e.includes("Assigned"))return{status:"Assigned"}}return{}}}e.default=s});define("crm:views/case/record/panels/activities",["crm:views/record/panels/activities"],function(i){return i.extend({getComposeEmailAttributes:function(e,t,s){t=t||{};Espo.Ui.notifyWait();i.prototype.getComposeEmailAttributes.call(this,e,t,i=>{i.name="[#"+this.model.get("number")+"] "+this.model.get("name");Espo.Ajax.getRequest("Case/action/emailAddressList?id="+this.model.id).then(e=>{i.to="";i.cc="";i.nameHash={};e.forEach((e,t)=>{0===t?i.to+=e.emailAddress+";":i.cc+=e.emailAddress+";";i.nameHash[e.emailAddress]=e.name});Espo.Ui.notify(!1);s.call(this,i)})})}})});define("crm:views/campaign-tracking-url/record/edit",["views/record/edit"],function(e){return e.extend({})});define("crm:views/campaign-tracking-url/record/edit-small",["views/record/edit-small"],function(e){return e.extend({})});define("crm:views/campaign-log-record/fields/data",["views/fields/base"],function(e){return e.extend({listTemplate:"crm:campaign-log-record/fields/data/detail",getValueForDisplay:function(){var e=this.model.get("action");switch(e){case"Sent":case"Opened":case"Clicked":return this.model.get("objectId")&&this.model.get("objectType")&&this.model.get("objectName")?$("<a>").attr("href","#"+this.model.get("objectType")+"/view/"+this.model.get("objectId")).text(this.model.get("objectName")).get(0).outerHTML:$("<span>").text(this.model.get("stringData")||"").get(0).outerHTML;case"Opted Out":return $("<span>").text(this.model.get("stringData")||"").addClass("text-danger").get(0).outerHTML;case"Bounced":var t=this.model.get("stringData"),i=this.model.get("stringAdditionalData"),s="Hard"===i?this.translate("hard","labels","Campaign"):this.translate("soft","labels","Campaign");return $("<span>").append($("<span>").addClass("label label-default").text(s)," ",$("<s>").text(t).addClass("Hard"===i?"text-danger":"")).get(0).outerHTML}return""}})});define("modules/crm/views/campaign/unsubscribe",["exports","view"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{template="crm:campaign/unsubscribe";data(){return{isSubscribed:this.isSubscribed,inProcess:this.inProcess}}setup(){super.setup();this.actionData=this.options.actionData;this.isSubscribed=this.actionData.isSubscribed;this.inProcess=!1;let e=this.actionData.hash&&this.actionData.emailAddress?`Campaign/unsubscribe/${this.actionData.emailAddress}/`+this.actionData.hash:"Campaign/unsubscribe/"+this.actionData.queueItemId;this.addActionHandler("subscribe",()=>{Espo.Ui.notifyWait();this.inProcess=!0;this.reRender();Espo.Ajax.deleteRequest(e).then(()=>{this.isSubscribed=!0;this.inProcess=!1;this.reRender().then(()=>{var e=this.translate("subscribedAgain","messages","Campaign");Espo.Ui.notify(e,"success",0,{closeButton:!0})})}).catch(()=>{this.inProcess=!1;this.reRender()})});this.addActionHandler("unsubscribe",()=>{Espo.Ui.notifyWait();this.inProcess=!0;this.reRender();Espo.Ajax.postRequest(e).then(()=>{Espo.Ui.success(this.translate("unsubscribed","messages","Campaign"),{closeButton:!0});this.isSubscribed=!1;this.inProcess=!1;this.reRender().then(()=>{var e=this.translate("unsubscribed","messages","Campaign");Espo.Ui.notify(e,"success",0,{closeButton:!0})})}).catch(()=>{this.inProcess=!1;this.reRender()})})}}e.default=s});define("modules/crm/views/campaign/tracking-url",["exports","view"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{templateContent=`
        <div class="container content">
            <div class="block-center-md">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <div class="complex-text">{{complexText message}}</div>
                    </div>
                </div>
            </div>
        </div>
    `;data(){return{message:this.options.message}}}e.default=s});define("modules/crm/views/campaign/detail",["exports","views/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("crm:views/campaign/record/detail",["views/record/detail"],function(e){return e.extend({duplicateAction:!0,setupActionItems:function(){e.prototype.setupActionItems.call(this);this.dropdownItemList.push({label:"Generate Mail Merge PDF",name:"generateMailMergePdf",hidden:!this.isMailMergeAvailable()});this.listenTo(this.model,"change",function(){this.isMailMergeAvailable()?this.showActionItem("generateMailMergePdf"):this.hideActionItem("generateMailMergePdf")},this)},afterRender:function(){e.prototype.afterRender.call(this)},isMailMergeAvailable:function(){return"Mail"===this.model.get("type")&&!(!this.model.get("targetListsIds")||!this.model.get("targetListsIds").length||!(this.model.get("leadsTemplateId")||this.model.get("contactsTemplateId")||this.model.get("accountsTemplateId")||this.model.get("usersTemplateId")))},actionGenerateMailMergePdf:function(){this.createView("dialog","crm:views/campaign/modals/mail-merge-pdf",{model:this.model},function(e){e.render();this.listenToOnce(e,"proceed",e=>{this.clearView("dialog");Espo.Ui.notifyWait();Espo.Ajax.postRequest(`Campaign/${this.model.id}/generateMailMerge`,{link:e}).then(e=>{Espo.Ui.notify(!1);window.open("?entryPoint=download&id="+e.id,"_blank")})})})}})});define("modules/crm/views/campaign/record/panels/campaign-stats",["exports","views/record/panels/side"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{controlStatsFields(){var e=this.model.attributes.type;let t;switch(e){case"Email":case"Newsletter":t=["sentCount","openedCount","clickedCount","optedOutCount","bouncedCount","leadCreatedCount","optedInCount","revenue"];break;case"Informational Email":t=["sentCount","bouncedCount"];break;case"Web":t=["leadCreatedCount","optedInCount","revenue"];break;case"Television":case"Radio":t=["leadCreatedCount","revenue"];break;case"Mail":t=["sentCount","leadCreatedCount","optedInCount","revenue"];break;default:t=["leadCreatedCount","revenue"]}if(!this.getConfig().get("massEmailOpenTracking")){e=t.indexOf("openedCount");-1<e&&t.splice(e,1)}this.statsFieldList.forEach(e=>{this.options.recordViewObject.hideField(e)});t.forEach(e=>{this.options.recordViewObject.showField(e)});this.getAcl().checkScope("Lead")||this.options.recordViewObject.hideField("leadCreatedCount",!0);this.getAcl().checkScope("Opportunity")||this.options.recordViewObject.hideField("revenue",!0)}setupFields(){this.fieldList=["sentCount","openedCount","clickedCount","optedOutCount","bouncedCount","leadCreatedCount","optedInCount","revenue"];this.statsFieldList=this.fieldList}setup(){super.setup();this.controlStatsFields();this.listenTo(this.model,"change:type",()=>this.controlStatsFields())}}e.default=s});define("modules/crm/views/campaign/record/panels/campaign-log-records",["exports","views/record/panels/relationship","helpers/record-modal"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{filterList=["all","sent","opened","optedOut","bounced","clicked","optedIn","leadCreated"];setup(){this.getAcl().checkScope("TargetList","create")&&this.actionList.push({action:"createTargetList",label:"Create Target List"});this.filterList=Espo.Utils.clone(this.filterList);if(!this.getConfig().get("massEmailOpenTracking")){var e=this.filterList.indexOf("opened");0<=e&&this.filterList.splice(e,1)}super.setup()}actionCreateTargetList(){var e={sourceCampaignId:this.model.id,sourceCampaignName:this.model.attributes.name};if(this.collection.data.primaryFilter){var t=Espo.Utils.upperCaseFirst(this.collection.data.primaryFilter).replace(/([A-Z])/g," $1");e.includingActionList=[t]}else e.includingActionList=[];t=new i.default;t.showCreate(this,{entityType:"TargetList",attributes:e,fullFormDisabled:!0,layoutName:"createFromCampaignLog",afterSave:()=>{Espo.Ui.success(this.translate("Done"))},beforeRender:e=>{e.getRecordView().setFieldRequired("includingActionList")}})}}e.default=a});define("crm:views/campaign/modals/mail-merge-pdf",["views/modal","ui/select"],function(e,t){return e.extend({template:"crm:campaign/modals/mail-merge-pdf",data:function(){return{linkList:this.linkList}},setup:function(){e.prototype.setup.call(this);this.headerText=this.translate("Generate Mail Merge PDF","labels","Campaign");this.linkList=[];["contacts","leads","accounts","users"].forEach(e=>{if(this.model.get(e+"TemplateId")){var t=this.getMetadata().get(["entityDefs","TargetList","links",e,"entity"]);this.getAcl().checkScope(t)&&this.linkList.push(e)}});this.buttonList.push({name:"proceed",label:"Proceed",style:"danger"});this.buttonList.push({name:"cancel",label:"Cancel"})},afterRender:function(){t.init(this.$el.find('.field[data-name="link"] select'))},actionProceed:function(){var e=this.$el.find('.field[data-name="link"] select').val();this.trigger("proceed",e)}})});define("crm:views/campaign/fields/template",["views/fields/link"],function(e){return e.extend({createDisabled:!0,getSelectFilters:function(){return{entityType:{type:"in",value:[this.getMetadata().get(["entityDefs","Campaign","fields",this.name,"targetEntityType"])]}}}})});define("crm:views/campaign/fields/int-with-percentage",["views/fields/int"],function(e){return e.extend({getValueForDisplay:function(){var e=this.name.substr(0,this.name.length-5)+"Percentage",t=this.model.get(this.name),i=this.model.get(e);null!=i&&i&&(t+=" ("+this.model.get(e)+"%)");return t}})});define("modules/crm/views/call/detail",["exports","modules/crm/views/meeting/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("crm:views/call/record/list-expanded",["views/record/list-expanded","crm:views/call/record/list"],function(e,t){return e.extend({actionSetHeld:function(e){t.prototype.actionSetHeld.call(this,e)},actionSetNotHeld:function(e){t.prototype.actionSetNotHeld.call(this,e)}})});define("modules/crm/views/call/record/edit-small",["exports","views/record/edit"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/call/record/detail",["exports","views/record/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{duplicateAction=!0;setupActionItems(){super.setupActionItems();if(this.getAcl().checkModel(this.model,"edit")&&this.getAcl().checkField(this.entityType,"status","edit")&&!["Held","Not Held"].includes(this.model.attributes.status)){this.dropdownItemList.push({label:"Set Held",name:"setHeld",onClick:()=>this.actionSetHeld()});this.dropdownItemList.push({label:"Set Not Held",name:"setNotHeld",onClick:()=>this.actionSetNotHeld()})}}manageAccessEdit(e){super.manageAccessEdit(e);if(e&&!this.getAcl().checkModel(this.model,"edit",!0)){this.hideActionItem("setHeld");this.hideActionItem("setNotHeld")}}actionSetHeld(){this.model.save({status:"Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved"));this.removeActionItem("setHeld");this.removeActionItem("setNotHeld")})}actionSetNotHeld(){this.model.save({status:"Not Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved"));this.removeActionItem("setHeld");this.removeActionItem("setNotHeld")})}}e.default=s});define("crm:views/call/record/row-actions/default",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);if(this.options.acl.edit&&!["Held","Not Held"].includes(this.model.get("status"))&&this.getAcl().checkField(this.model.entityType,"status","edit")){e.push({action:"setHeld",label:"Set Held",data:{id:this.model.id},groupIndex:1});e.push({action:"setNotHeld",label:"Set Not Held",data:{id:this.model.id},groupIndex:1})}this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("crm:views/call/record/row-actions/dashlet",["views/record/row-actions/view-and-edit"],function(t){return t.extend({getActionList:function(){var e=t.prototype.getActionList.call(this);if(this.options.acl.edit&&!["Held","Not Held"].includes(this.model.get("status"))&&this.getAcl().checkField(this.model.entityType,"status","edit")){e.push({action:"setHeld",label:"Set Held",data:{id:this.model.id},groupIndex:1});e.push({action:"setNotHeld",label:"Set Not Held",data:{id:this.model.id},groupIndex:1})}this.options.acl.delete&&e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id,scope:this.model.entityType},groupIndex:0});return e}})});define("modules/crm/views/call/fields/leads",["exports","modules/crm/views/call/fields/contacts"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/call/fields/date-start",["exports","views/fields/datetime","moment"],function(e,t,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);s=i(s);function i(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{setup(){super.setup();this.notActualStatusList=[...this.getMetadata().get(`scopes.${this.entityType}.completedStatusList`)||[],...this.getMetadata().get(`scopes.${this.entityType}.canceledStatusList`)||[]]}getAttributeList(){return[...super.getAttributeList(),"dateEnd","status"]}data(){let e;var t=this.model.get("status");!t||this.notActualStatusList.includes(t)||this.mode!==this.MODE_DETAIL&&this.mode!==this.MODE_LIST||(this.isDateInPast("dateEnd")?e="danger":this.isDateInPast("dateStart")&&(e="warning"));return{...super.data(),style:e}}isDateInPast(e){var t=this.model.get(e);if(t){var t=this.getDateTime().toMoment(t),i=(0,s.default)().tz(this.getDateTime().timeZone||"UTC");if(t.unix()<i.unix())return!0}return!1}}e.default=a});define("modules/crm/views/calendar/mode-buttons",["exports","view"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{template="crm:calendar/mode-buttons";visibleModeListCount=3;data(){var e=Espo.Utils.clone(this.scopeList);e.unshift("all");let i=[];this.scopeList.forEach(e=>{var t={scope:e};this.getCalendarParentView().enabledScopeList.includes(e)||(t.disabled=!0);i.push(t)});return{mode:this.mode,visibleModeDataList:this.getVisibleModeDataList(),hiddenModeDataList:this.getHiddenModeDataList(),scopeFilterDataList:i,isCustomViewAvailable:this.isCustomViewAvailable,hasMoreItems:this.isCustomViewAvailable,hasWorkingTimeCalendarLink:this.getAcl().checkScope("WorkingTimeCalendar")}}getCalendarParentView(){return this.getParentView()}setup(){this.isCustomViewAvailable=this.options.isCustomViewAvailable;this.modeList=this.options.modeList;this.scopeList=this.options.scopeList;this.mode=this.options.mode}getModeDataList(e){let s=[];this.modeList.forEach(e=>{var t={mode:e,label:this.translate(e,"modes","Calendar"),labelShort:this.translate(e,"modes","Calendar").substring(0,2)};s.push(t)});this.isCustomViewAvailable&&(this.getPreferences().get("calendarViewDataList")||[]).forEach(e=>{e=Espo.Utils.clone(e);e.mode="view-"+e.id;e.label=e.name;e.labelShort=(e.name||"").substring(0,2);s.push(e)});if(!e){let i;s.forEach((e,t)=>{e.mode===this.mode&&(i=t)})}return s}getVisibleModeDataList(){var e=this.getModeDataList(),t=e.find(e=>e.mode===this.mode),e=e.slice(0,this.visibleModeListCount);t&&!e.find(e=>e.mode===this.mode)&&e.push(t);return e}getHiddenModeDataList(){var e=this.getModeDataList();let i=[];e.forEach((e,t)=>{t<this.visibleModeListCount||i.push(e)});return i}}e.default=s});define("modules/crm/views/calendar/calendar-page",["exports","view","crm:views/calendar/modals/edit-view"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=s(t);i=s(i);function s(e){return e&&e.__esModule?e:{default:e}}class a extends t.default{template="crm:calendar/calendar-page";el="#main";fullCalendarModeList=["month","agendaWeek","agendaDay","basicWeek","basicDay","listWeek"];events={'click [data-action="createCustomView"]':function(){this.createCustomView()},'click [data-action="editCustomView"]':function(){this.editCustomView()}};shortcutKeys={Home:function(e){this.handleShortcutKeyHome(e)},Numpad7:function(e){this.handleShortcutKeyHome(e)},Numpad4:function(e){this.handleShortcutKeyArrowLeft(e)},Numpad6:function(e){this.handleShortcutKeyArrowRight(e)},ArrowLeft:function(e){this.handleShortcutKeyArrowLeft(e)},ArrowRight:function(e){this.handleShortcutKeyArrowRight(e)},Minus:function(e){this.handleShortcutKeyMinus(e)},Equal:function(e){this.handleShortcutKeyPlus(e)},NumpadSubtract:function(e){this.handleShortcutKeyMinus(e)},NumpadAdd:function(e){this.handleShortcutKeyPlus(e)},Digit1:function(e){this.handleShortcutKeyDigit(e,1)},Digit2:function(e){this.handleShortcutKeyDigit(e,2)},Digit3:function(e){this.handleShortcutKeyDigit(e,3)},Digit4:function(e){this.handleShortcutKeyDigit(e,4)},Digit5:function(e){this.handleShortcutKeyDigit(e,5)},Digit6:function(e){this.handleShortcutKeyDigit(e,6)},"Control+Space":function(e){this.handleShortcutKeyControlSpace(e)}};setup(){this.mode=this.mode||this.options.mode||null;this.date=this.date||this.options.date||null;if(!this.mode){this.mode=this.getStorage().get("state","calendarMode")||null;if(this.mode&&0===this.mode.indexOf("view-")){let t=this.mode.slice(5);var e=this.getPreferences().get("calendarViewDataList")||[];let i=!1;e.forEach(e=>{e.id===t&&(i=!0)});i||(this.mode=null);this.options.userId&&(this.mode=null)}}this.events["keydown.main"]=e=>{var t=Espo.Utils.getKeyFromKeyEvent(e);"function"==typeof this.shortcutKeys[t]&&this.shortcutKeys[t].call(this,e.originalEvent)};!this.mode||~this.fullCalendarModeList.indexOf(this.mode)||0===this.mode.indexOf("view-")?this.setupCalendar():"timeline"===this.mode&&this.setupTimeline()}afterRender(){this.$el.focus()}updateUrl(e){let t="#Calendar/show";(this.mode||this.date)&&(t+="/");this.mode&&(t+="mode="+this.mode);this.date&&(t+="&date="+this.date);if(this.options.userId){t+="&userId="+this.options.userId;this.options.userName&&(t+="&userName="+encodeURIComponent(this.options.userName))}this.getRouter().navigate(t,{trigger:e})}setupCalendar(){var e=this.getMetadata().get(["clientDefs","Calendar","calendarView"])||"crm:views/calendar/calendar";this.createView("calendar",e,{date:this.date,userId:this.options.userId,userName:this.options.userName,mode:this.mode,fullSelector:"#main > .calendar-container"},e=>{let i=!0;this.listenTo(e,"view",(e,t)=>{this.date=e;this.mode=t;i||this.updateUrl();i=!1});this.listenTo(e,"change:mode",(e,t)=>{this.mode=e;this.options.userId||this.getStorage().set("state","calendarMode",e);if(t)this.updateUrl(!0);else{~this.fullCalendarModeList.indexOf(e)||this.updateUrl(!0);this.$el.focus()}})})}setupTimeline(){var e=this.getMetadata().get(["clientDefs","Calendar","timelineView"])||"crm:views/calendar/timeline";this.createView("calendar",e,{date:this.date,userId:this.options.userId,userName:this.options.userName,fullSelector:"#main > .calendar-container"},e=>{let i=!0;this.listenTo(e,"view",(e,t)=>{this.date=e;this.mode=t;i||this.updateUrl();i=!1});this.listenTo(e,"change:mode",e=>{this.mode=e;this.options.userId||this.getStorage().set("state","calendarMode",e);this.updateUrl(!0)})})}updatePageTitle(){this.setPageTitle(this.translate("Calendar","scopeNames"))}async createCustomView(){var e=new i.default({afterSave:e=>{this.mode="view-"+e.id;this.date=null;this.updateUrl(!0)}});await this.assignView("modal",e);await e.render()}async editCustomView(){var e=this.getCalendarView().viewId;if(e){e=new i.default({id:e,afterSave:()=>{this.getCalendarView().setupMode();this.getCalendarView().reRender()},afterRemove:()=>{this.mode=null;this.date=null;this.updateUrl(!0)}});await this.assignView("modal",e);await e.render()}}getCalendarView(){return this.getView("calendar")}handleShortcutKeyHome(e){e.preventDefault();this.getCalendarView().actionToday()}handleShortcutKeyArrowLeft(e){e.preventDefault();this.getCalendarView().actionPrevious()}handleShortcutKeyArrowRight(e){e.preventDefault();this.getCalendarView().actionNext()}handleShortcutKeyMinus(e){if(this.getCalendarView().actionZoomOut){e.preventDefault();this.getCalendarView().actionZoomOut()}}handleShortcutKeyPlus(e){if(this.getCalendarView().actionZoomIn){e.preventDefault();this.getCalendarView().actionZoomIn()}}handleShortcutKeyDigit(e,t){var i=this.getCalendarView().hasView("modeButtons")?this.getCalendarView().getModeButtonsView().getModeDataList(!0).map(e=>e.mode):this.getCalendarView().modeList,i=i[t-1];if(i){e.preventDefault();i===this.mode?this.getCalendarView().actionRefresh():this.getCalendarView().selectMode(i)}}handleShortcutKeyControlSpace(e){if(this.getCalendarView().createEvent){e.preventDefault();this.getCalendarView().createEvent()}}}e.default=a});define("modules/crm/views/calendar/modals/shared-options",["exports","views/modal","model","views/record/edit-for-modal","crm:views/calendar/fields/users"],function(e,t,s,a,n){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=i(t);s=i(s);a=i(a);n=i(n);function i(e){return e&&e.__esModule?e:{default:e}}class o extends t.default{className="dialog dialog-record";templateContent=`
        <div class="record-container no-side-margin">{{{record}}}</div>
    `;recordView;constructor(e){super(e);this.options=e}setup(){this.buttonList=[{name:"save",label:"Save",style:"primary",onClick:()=>this.actionSave()},{name:"cancel",label:"Cancel",onClick:()=>this.actionClose()}];this.headerText=this.translate("timeline","modes","Calendar")+" · "+this.translate("Shared Mode Options","labels","Calendar");var e=this.options.users;let t=[],i={};e.forEach(e=>{t.push(e.id);i[e.id]=e.name});this.model=new s.default({usersIds:t,usersNames:i});this.recordView=new a.default({model:this.model,detailLayout:[{rows:[[{view:new n.default({name:"users"})},!1]]}]});this.assignView("record",this.recordView)}actionSave(){let i=this.recordView.processFetch();if(!this.recordView.validate()){let t=[];var e=this.model.attributes.usersIds||[];e.forEach(e=>{t.push({id:e,name:(i.usersNames||{})[e]||e})});this.options.onApply({users:t});this.close()}}}e.default=o});define("modules/crm/views/calendar/modals/edit",["exports","views/modals/edit"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{template="crm:calendar/modals/edit";scopeList=["Meeting","Call","Task"];data(){return{scopeList:this.scopeList,scope:this.scope,isNew:!this.id}}additionalEvents={'change .scope-switcher input[name="scope"]':function(){Espo.Ui.notifyWait();let i=this.scope,s=$('.scope-switcher input[name="scope"]:checked').val();this.scope=s;this.getModelFactory().create(this.scope,e=>{e.populateDefaults();var t=this.getRecordView().fetch(),t={...t,...this.getRecordView().model.getClonedAttributes()};this.filterAttributesForEntityType(t,s,i);e.set(t);this.model=e;this.createRecordView(e,e=>{e.render();e.notify(!1)});this.handleAccess(e)})}};filterAttributesForEntityType(s,a,e){"Task"!==a&&"Task"!==e||delete s.reminders;this.getHelper().fieldManager.getEntityTypeFieldList(a,{type:"enum"}).forEach(e=>{if(e in s){var t=this.getMetadata().get(["entityDefs",a,"fields",e,"options"])||[],i=s[e];~t.indexOf(i)||delete s[e]}})}createRecordView(e,t){if(!this.id&&!this.dateIsChanged){if(this.options.dateStart&&this.options.dateEnd){this.model.set("dateStart",this.options.dateStart);this.model.set("dateEnd",this.options.dateEnd)}if(this.options.allDay){var i=this.getMetadata().get("clientDefs.Calendar.allDayScopeList")||[];if(~i.indexOf(this.scope)){this.model.set("dateStart",null);this.model.set("dateEnd",null);this.model.set("dateStartDate",null);this.model.set("dateEndDate",this.options.dateEndDate);this.options.dateEndDate!==this.options.dateStartDate&&this.model.set("dateStartDate",this.options.dateStartDate)}else if(this.getMetadata().get(["entityDefs",this.scope,"fields","dateStartDate"])){this.model.set("dateStart",null);this.model.set("dateEnd",null);this.model.set("dateStartDate",this.options.dateStartDate);this.model.set("dateEndDate",this.options.dateEndDate);this.model.set("isAllDay",!0)}else{this.model.set("isAllDay",!1);this.model.set("dateStartDate",null);this.model.set("dateEndDate",null)}}}this.listenTo(this.model,"change:dateStart",(e,t,i)=>{i.ui&&(this.dateIsChanged=!0)});this.listenTo(this.model,"change:dateEnd",(e,t,i)=>{(i.ui||i.updatedByDuration)&&(this.dateIsChanged=!0)});super.createRecordView(e,t)}handleAccess(e){if(this.id&&!this.getAcl().checkModel(e,"edit")||!this.id&&!this.getAcl().checkModel(e,"create")){this.hideButton("save");this.hideButton("fullForm");this.$el.find('button[data-name="save"]').addClass("hidden");this.$el.find('button[data-name="fullForm"]').addClass("hidden")}else{this.showButton("save");this.showButton("fullForm")}this.getAcl().checkModel(e,"delete")?this.showButton("remove"):this.hideButton("remove")}afterRender(){super.afterRender();if(this.hasView("edit")){var e=this.getView("edit").model;e&&this.handleAccess(e)}}setup(){this.events={...this.additionalEvents,...this.events};this.scopeList=Espo.Utils.clone(this.options.scopeList||this.scopeList);this.enabledScopeList=this.options.enabledScopeList||this.scopeList;if(!this.options.id&&!this.options.scope){let t=[];this.scopeList.forEach(e=>{this.getAcl().check(e,"create")&&~this.enabledScopeList.indexOf(e)&&t.push(e)});this.scopeList=t;var e=t[0];e&&~this.scopeList.indexOf(e)?this.options.scope=e:this.options.scope=this.scopeList[0]||null;if(0===this.scopeList.length)return this.remove(),void 0}super.setup();this.id||(this.$header=$("<a>").attr("title",this.translate("Full Form")).attr("role","button").attr("data-action","fullForm").addClass("action").text(this.translate("Create","labels","Calendar")));this.id&&this.buttonList.splice(1,0,{name:"remove",text:this.translate("Remove"),onClick:()=>this.actionRemove()});this.once("after:save",()=>{this.$el.find(".scope-switcher").remove()})}actionRemove(){let t=this.getView("edit").model;this.confirm(this.translate("removeRecordConfirmation","messages"),()=>{let e=this.dialog.$el.find(".modal-footer button");e.addClass("disabled");t.destroy().then(()=>{this.trigger("after:destroy",t);this.dialog.close()}).catch(()=>{e.removeClass("disabled")})})}}e.default=s});define("modules/crm/views/admin/entity-manager/fields/status-list",["exports","views/fields/multi-enum"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{setupOptions(){var e=this.model.get("name"),t=this.getMetadata().get(["entityDefs",e,"fields","status","options"])||[];this.params.options=[...t];this.params.translation=e+".options.status"}}e.default=s});define("modules/crm/views/activities/list",["exports","views/list-related"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{createButton=!1;unlinkDisabled=!0;filtersDisabled=!0;setup(){this.rowActionsView="views/record/row-actions/default";super.setup();this.type=this.options.type}getHeader(){var e=this.model.get("name")||this.model.id,t=`#${this.scope}/view/`+this.model.id,t=$("<a>").attr("href",t).addClass("font-size-flexible title").text(e).css("user-select","none"),e=(this.model.get("deleted")&&t.css("text-decoration","line-through"),this.getHelper().getScopeColorIconHtml(this.foreignScope)),i=this.getLanguage().translate(this.scope,"scopeNamesPlural");let s=$("<span>").text(i);this.rootLinkDisabled||(s=$("<span>").append($("<a>").attr("href","#"+this.scope).addClass("action").attr("data-action","navigateToRoot").text(i)));s.css("user-select","none");e&&s.prepend(e);i="history"===this.type?this.translate("History"):this.translate("Activities"),e=$("<span>").text(i),i=(e.css("user-select","none"),$("<span>").text(this.translate(this.foreignScope,"scopeNamesPlural")));i.css("user-select","none").css("cursor","pointer").attr("data-action","fullRefresh").attr("title",this.translate("clickToRefresh","messages"));return this.buildHeaderHtml([s,t,e,i])}updatePageTitle(){this.setPageTitle(this.translate(this.foreignScope,"scopeNamesPlural"))}}e.default=s});define("modules/crm/views/account/detail",["exports","views/detail"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});define("modules/crm/views/account/fields/shipping-address",["exports","views/fields/address"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{copyFrom="billingAddress";setup(){super.setup();this.addActionHandler("copyFromBilling",()=>this.copy());this.attributePartList=this.getMetadata().get(["fields","address","actualFields"])||[];this.allAddressAttributeList=[];this.attributePartList.forEach(e=>{this.allAddressAttributeList.push(this.copyFrom+Espo.Utils.upperCaseFirst(e));this.allAddressAttributeList.push(this.name+Espo.Utils.upperCaseFirst(e))});this.listenTo(this.model,"change",()=>{let e=!1;for(var t of this.allAddressAttributeList)if(this.model.hasChanged(t)){e=!0;break}e&&this.isEditMode()&&this.isRendered()&&this.copyButtonElement&&(this.toShowCopyButton()?this.copyButtonElement.classList.remove("hidden"):this.copyButtonElement.classList.add("hidden"))})}afterRender(){super.afterRender();if(this.mode===this.MODE_EDIT&&this.element){var e=this.translate("Copy Billing","labels","Account"),t=this.copyButtonElement=document.createElement("button");t.classList.add("btn","btn-default","btn-sm","action");t.textContent=e;t.setAttribute("data-action","copyFromBilling");this.toShowCopyButton()||t.classList.add("hidden");this.element.append(t)}}copy(){let s=this.copyFrom;Object.keys(this.getMetadata().get("fields.address.fields")||{}).forEach(e=>{var t=this.name+Espo.Utils.upperCaseFirst(e),i=s+Espo.Utils.upperCaseFirst(e);this.model.set(t,this.model.get(i))})}toShowCopyButton(){let i=!1,s=!1;this.attributePartList.forEach(e=>{var t=this.copyFrom+Espo.Utils.upperCaseFirst(e),t=(this.model.get(t)&&(i=!0),this.name+Espo.Utils.upperCaseFirst(e));this.model.get(t)&&(s=!0)});return i&&!s}}e.default=s});define("modules/crm/view-setup-handlers/document/record-list-drag-n-drop",["exports","underscore","bullbone"],function(e,t,i){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(s=t,s&&s.__esModule?s:{default:s});var s;function a(e){this.view=e}t.default.extend(a.prototype,{process:function(){this.listenTo(this.view,"after:render",()=>this.initDragDrop());this.listenTo(this.view,"remove",()=>this.disable())},disable:function(){var e=this.view.$el.parent(),t=e.get(0);e.off("drop");if(t&&this.onDragoverBind){t.removeEventListener("dragover",this.onDragoverBind);t.removeEventListener("dragenter",this.onDragenterBind);t.removeEventListener("dragleave",this.onDragleaveBind)}},initDragDrop:function(){this.disable();let t=this.view.$el.parent();var e=t.get(0);t.on("drop",e=>{e.preventDefault();e.stopPropagation();e=e.originalEvent;if(e.dataTransfer&&e.dataTransfer.files&&1===e.dataTransfer.files.length&&this.dropEntered){this.removeDrop();this.create(e.dataTransfer.files[0])}else this.removeDrop(t)});this.dropEntered=!1;this.onDragoverBind=this.onDragover.bind(this);this.onDragenterBind=this.onDragenter.bind(this);this.onDragleaveBind=this.onDragleave.bind(this);e.addEventListener("dragover",this.onDragoverBind);e.addEventListener("dragenter",this.onDragenterBind);e.addEventListener("dragleave",this.onDragleaveBind)},renderDrop:function(){this.dropEntered=!0;var e=$('<div class="dd-backdrop">').css("pointer-events","none").append('<span class="fas fa-paperclip"></span>').append(" ").append($("<span>").text(this.view.getLanguage().translate("Create Document","labels","Document")));this.view.$el.append(e)},removeDrop:function(){this.view.$el.find("> .dd-backdrop").remove();this.dropEntered=!1},create:function(s){this.view.actionQuickCreate().then(e=>{let t=e.getRecordView().getFieldView("file");if(t)t.isRendered()?t.uploadFile(s):this.listenToOnce(t,"after:render",()=>{t.uploadFile(s)});else{var i="No 'file' field on the layout.";Espo.Ui.error(i);console.error(i)}})},onDragover:function(e){e.preventDefault()},onDragenter:function(e){e.preventDefault();e.dataTransfer.types&&e.dataTransfer.types.length&&~e.dataTransfer.types.indexOf("Files")&&!this.dropEntered&&this.renderDrop()},onDragleave:function(e){e.preventDefault();if(this.dropEntered){var t=e.fromElement||e.relatedTarget;t&&$.contains(this.view.$el.parent().get(0),t)||t&&t.parentNode&&"[object ShadowRoot]"===t.parentNode.toString()||this.removeDrop()}}});Object.assign(a.prototype,i.Events);e.default=a});define("modules/crm/handlers/task/reminders-handler",["exports","bullbone"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;class i{constructor(e){this.view=e;this.model=e.model;this.user=this.view.getUser();this.ignoreStatusList=[...this.view.getMetadata().get(["scopes",this.view.entityType,"completedStatusList"])||[],...this.view.getMetadata().get(["scopes",this.view.entityType,"canceledStatusList"])||[]]}process(){this.control();this.listenTo(this.model,"change",()=>{(this.model.hasChanged("assignedUserId")||this.model.hasChanged("assignedUsersIds")||this.model.hasChanged("dateEnd")||this.model.hasChanged("dateEndDate")||this.model.hasChanged("status"))&&this.control()})}control(){if(this.model.attributes.dateEnd||this.model.attributes.dateEndDate){var e=this.model.attributes.assignedUsersIds||[];this.ignoreStatusList.includes(this.model.attributes.status)||this.model.attributes.assignedUserId!==this.user.id&&!e.includes(this.user.id)?this.view.hideField("reminders"):this.view.showField("reminders")}else this.view.hideField("reminders")}}Object.assign(i.prototype,t.Events);e.default=i});define("modules/crm/handlers/task/menu",["exports","action-handler"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{complete(){var e=this.view.model;e.save({status:"Completed"},{patch:!0}).then(()=>{Espo.Ui.success(this.view.getLanguage().translateOption("Completed","status","Task"))})}isCompleteAvailable(){var e=this.view.model.get("status"),t=this.view;return!t.getRecordView().isEditMode()&&(t=this.view.getMetadata().get("entityDefs.Task.fields.status.notActualOptions")||[],!t.includes(e))}}e.default=s});define("modules/crm/handlers/task/detail-actions",["exports","action-handler"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{complete(){var e=this.view.model;e.save({status:"Completed"},{patch:!0}).then(()=>{Espo.Ui.success(this.view.getLanguage().translateOption("Completed","status","Task"))})}isCompleteAvailable(){var e=this.view.model.get("status"),t=this.view.getMetadata().get("entityDefs.Task.fields.status.notActualOptions")||[];return!t.includes(e)}}e.default=s});define("modules/crm/handlers/opportunity/defaults-preparator",["exports","handlers/model/defaults-preparator","metadata","di"],function(e,t,i,s){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=o(t);i=o(i);let a,n;function o(e){return e&&e.__esModule?e:{default:e}}function l(r,t,c,i,h,e){var I,k,u,m,p,s,a=Symbol.metadata||Symbol.for("Symbol.metadata"),D=Object.defineProperty,n=Object.create,_=[n(null),n(null)],o=t.length;function O(s,a,n){return function(e,t){a&&(t=e,e=r);for(var i=0;i<s.length;i++)t=s[i].apply(e,n?[t]:[]);return n?t:e}}function N(e,t,i,s){if("function"==typeof e||!s&&void 0===e)return e;throw new TypeError(t+" must "+(i||"be")+" a function"+(s?"":" or undefined"))}function f(n,t,e,i,s,a,o,l,d,r,c){function h(e){if(!c(e))throw new TypeError("Attempted to access private element on non-instance")}var u=[].concat(t[0]),m=t[3],p=!o,f=1===s,g=3===s,v=4===s,y=2===s;function w(i,s,a){return function(e,t){return s&&(t=e,e=n),a&&a(e),b[i].call(e,t)}}if(!p){var b={},L=[],A=g?"get":v||f?"set":"value";if(d?(r||f?b={get:P(function(){return m(this)},i,"get"),set:function(e){t[4](this,e)}}:b[A]=m,r||P(b[A],i,y?"":A)):r||(b=Object.getOwnPropertyDescriptor(n,i)),!r&&!d){if((k=_[+l][i])&&7!=(k^s))throw Error("Decorating two elements with the same name ("+b[A].name+") is not supported yet");_[+l][i]=s<3?1:s}}for(var T=n,x=u.length-1;0<=x;x-=e?2:1){var M=N(u[x],"A decorator","be",!0),C=e?u[x-1]:void 0,S={},E={kind:["field","accessor","method","getter","setter","class"][s],name:i,metadata:I,addInitializer:function(e,t){if(e.v)throw new TypeError("attempted to call addInitializer after decoration was finished");N(t,"An initializer","be",!0),a.push(t)}.bind(null,S)};if(p)k=M.call(C,T,E),S.v=1,N(k,"class decorators","return")&&(T=k);else if(E.static=l,E.private=d,k=E.access={has:d?c.bind():function(e){return i in e}},v||(k.get=d?y?function(e){return h(e),b.value}:w("get",0,h):function(e){return e[i]}),y||g||(k.set=d?w("set",0,h):function(e,t){e[i]=t}),T=M.call(C,f?{get:b.get,set:b.set}:b[A],E),S.v=1,f){if("object"==typeof T&&T)(k=N(T.get,"accessor.get"))&&(b.get=k),(k=N(T.set,"accessor.set"))&&(b.set=k),(k=N(T.init,"accessor.init"))&&L.unshift(k);else if(void 0!==T)throw new TypeError("accessor decorators must return an object with get, set, or init properties or undefined")}else N(T,(r?"field":"method")+" decorators","return")&&(r?L.unshift(T):b[A]=T)}return s<2&&o.push(O(L,l,1),O(a,l,0)),r||p||(d?f?o.splice(-1,0,w("get",l),w("set",l)):o.push(y?b[A]:N.call.bind(b[A])):D(n,i,b)),T}function l(e){return D(e,a,{configurable:!0,enumerable:!0,value:I})}return void 0!==e&&(I=e[a]),I=n(null==I?null:I),p=[],n=function(e){e&&p.push(O(e))},s=function(e,t){for(var i,s=0;s<c.length;s++){var a=c[s],n=a[1],o=7&n;if((8&n)==e&&!o==t){var l=a[2],d=!!a[3],n=16&n;f(e?r:r.prototype,a,n,d?"#"+l:(i=l,a=void 0,a=((e,t)=>{if("object"!=typeof e||!e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);i=i.call(e,t||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")})(i,"string"),"symbol"==typeof a?a:a+""),o,o<2?[]:e?m=m||[]:u=u||[],p,!!e,d,t,e&&d?function(e){return(e=>{if(Object(e)!==e)throw TypeError("right-hand side of 'in' should be an object, got "+(null!==e?typeof e:"null"));return e})(e)===r}:h)}}},s(8,0),s(0,0),s(8,1),s(0,1),n(u),n(m),k=p,o||l(r),{e:k,get c(){var e=[];return o&&[l(r=f(r,[t],i,r.name,5,e)),O(e,1)]}}}function P(e,t,i){"symbol"==typeof t&&(t=(t=t.description)?"["+t+"]":"");try{Object.defineProperty(e,"name",{configurable:!0,value:i?i+" "+t:t})}catch(e){}return e}class d extends t.default{static#_=[a,n]=l(this,[],[[(0,s.inject)(i.default),0,"metadata"]],0,void 0,t.default).e;constructor(){super(...arguments);n(this)}metadata=a(this);prepare(e){var t=this.metadata.get("entityDefs.Opportunity.fields.stage.probabilityMap")||{},i=e.attributes.stage,s={};i in t&&(s.probability=t[i]);return Promise.resolve(s)}}e.default=d});define("modules/crm/handlers/opportunity/contacts-create",["exports","handlers/create-related"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{getAttributes(e){var t={};e.get("accountId")&&(t.accountsIds=[e.get("accountId")]);return Promise.resolve(t)}}e.default=s});define("modules/crm/handlers/knowledge-base-article/send-in-email",["exports","handlers/row-action"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{isAvailable(e,t){return this.view.getAcl().checkScope("Email","create")}process(s,e){let a=this.view.getParentView().model,t=this.view.getModelFactory(),n=this.view.getCollectionFactory();Espo.Ui.notifyWait();s.fetch().then(()=>new Promise(i=>{a.get("contactsIds")&&a.get("contactsIds").length?n.create("Contact",e=>{let t=[];e.url="Case/"+a.id+"/contacts";e.fetch().then(()=>{e.forEach(e=>{e.id===a.get("contactId")?t.unshift(e):t.push(e)});i(t)})}):a.get("accountId")?t.create("Account",e=>{e.id=a.get("accountId");e.fetch().then(()=>i([e]))}):a.get("leadId")?t.create("Lead",e=>{e.id=a.get("leadId");e.fetch().then(()=>i([e]))}):i([])})).then(e=>{let i={parentType:"Case",parentId:a.id,parentName:a.get("name"),name:"[#"+a.get("number")+"]",to:"",cc:"",nameHash:{}};e.forEach((e,t)=>{if(e.get("emailAddress")){0===t?i.to+=e.get("emailAddress")+";":i.cc+=e.get("emailAddress")+";";i.nameHash[e.get("emailAddress")]=e.get("name")}});Espo.loader.require("crm:knowledge-base-helper",e=>{var t=new e(this.view.getLanguage());t.getAttributesForEmail(s,i,e=>{var t=this.view.getMetadata().get("clientDefs.Email.modalViews.compose")||"views/modals/compose-email";this.view.createView("composeEmail",t,{attributes:e,selectTemplateDisabled:!0,signatureDisabled:!0},e=>{Espo.Ui.notify(!1);e.render();this.view.listenToOnce(e,"after:send",()=>{a.trigger("after:relate")})})})})}).catch(()=>{Espo.Ui.notify(!1)})}}e.default=s});define("modules/crm/handlers/knowledge-base-article/move",["exports","handlers/row-action"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{isAvailable(e,t){return e.collection&&"order"===e.collection.orderBy&&"asc"===e.collection.order}process(e,t){"moveToTop"===t?this.moveToTop(e):"moveToBottom"===t?this.moveToBottom(e):"moveUp"===t?this.moveUp(e):"moveDown"===t&&this.moveDown(e)}moveToTop(e){var t=this.collection.indexOf(e);if(0!==t){Espo.Ui.notifyWait();Espo.Ajax.postRequest("KnowledgeBaseArticle/action/moveToTop",{id:e.id,whereGroup:this.collection.getWhere()}).then(()=>{this.collection.fetch().then(()=>Espo.Ui.notify(!1))})}}moveUp(e){var t=this.collection.indexOf(e);if(0!==t){Espo.Ui.notifyWait();Espo.Ajax.postRequest("KnowledgeBaseArticle/action/moveUp",{id:e.id,whereGroup:this.collection.getWhere()}).then(()=>{this.collection.fetch().then(()=>Espo.Ui.notify(!1))})}}moveDown(e){var t=this.collection.indexOf(e);if(t!==this.collection.length-1||this.collection.length!==this.collection.total){Espo.Ui.notifyWait();Espo.Ajax.postRequest("KnowledgeBaseArticle/action/moveDown",{id:e.id,whereGroup:this.collection.getWhere()}).then(()=>{this.collection.fetch().then(()=>Espo.Ui.notify(!1))})}}moveToBottom(e){var t=this.collection.indexOf(e);if(t!==this.collection.length-1||this.collection.length!==this.collection.total){Espo.Ui.notifyWait();Espo.Ajax.postRequest("KnowledgeBaseArticle/action/moveToBottom",{id:e.id,whereGroup:this.collection.getWhere()}).then(()=>{this.collection.fetch().then(()=>Espo.Ui.notify(!1))})}}}e.default=s});define("modules/crm/handlers/event/reminders-handler",["exports","bullbone"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;class i{constructor(e){this.view=e;this.model=e.model;this.user=this.view.getUser();this.ignoreStatusList=[...this.view.getMetadata().get(["scopes",this.view.entityType,"completedStatusList"])||[],...this.view.getMetadata().get(["scopes",this.view.entityType,"canceledStatusList"])||[]]}process(){this.control();this.listenTo(this.model,"change",()=>{(this.model.hasChanged("assignedUserId")||this.model.hasChanged("usersIds")||this.model.hasChanged("assignedUsersIds")||this.model.hasChanged("status"))&&this.control()})}control(){var e=this.model.get("usersIds")||[],t=this.model.get("assignedUsersIds")||[];this.ignoreStatusList.includes(this.model.get("status"))||this.model.get("assignedUserId")!==this.user.id&&!e.includes(this.user.id)&&!t.includes(this.user.id)?this.view.hideField("reminders"):this.view.showField("reminders")}}Object.assign(i.prototype,t.Events);e.default=i});define("modules/crm/handlers/case/detail-actions",["exports","action-handler"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{close(){var e=this.view.model;e.save({status:"Closed"},{patch:!0}).then(()=>{Espo.Ui.success(this.view.translate("Closed","labels","Case"))})}reject(){var e=this.view.model;e.save({status:"Rejected"},{patch:!0}).then(()=>{Espo.Ui.success(this.view.translate("Rejected","labels","Case"))})}isCloseAvailable(){return this.isStatusAvailable("Closed")}isRejectAvailable(){return this.isStatusAvailable("Rejected")}isStatusAvailable(e){var t=this.view.model,i=this.view.getAcl(),s=this.view.getMetadata(),a=s.get("entityDefs.Case.fields.status.notActualOptions")||[];return!(a.includes(t.get("status"))||!i.check(t,"edit")||!i.checkField(t.entityType,"status","edit")||(a=s.get(["entityDefs","Case","fields","status","options"])||[],!a.includes(e)))}}e.default=s});define("modules/crm/handlers/campaign/mass-emails-create",["exports","handlers/create-related"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{getAttributes(e){return Promise.resolve({name:e.get("name")+" "+this.viewHelper.dateTime.getToday()})}}e.default=s});define("modules/crm/controllers/unsubscribe",["exports","controller"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{actionUnsubscribe(e){var t=e.view||"crm:views/campaign/unsubscribe";this.entire(t,{actionData:e.actionData,template:e.template},e=>{e.render()})}}e.default=s});define("modules/crm/controllers/tracking-url",["exports","controller"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{actionDisplayMessage(e){var t=e.view||"crm:views/campaign/tracking-url";this.entire(t,{message:e.message,template:e.template},e=>{e.render()})}}e.default=s});define("modules/crm/controllers/task",["exports","controllers/record"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{actionCreate(e){var t={...e.attributes};if(e.emailId){t.emailId=e.emailId;e.attributes=t}super.actionCreate(e)}}e.default=s});define("modules/crm/controllers/lead",["exports","controllers/record"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{actionConvert(e){this.main("crm:views/lead/convert",{id:e})}}e.default=s});define("modules/crm/controllers/event-confirmation",["exports","controller"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{actionConfirmEvent(e){var t=this.getMetadata().get(["clientDefs","EventConfirmation","confirmationView"])||"crm:views/event-confirmation/confirmation";this.entire(t,{actionData:e},e=>{e.render()})}}e.default=s});define("modules/crm/controllers/calendar",["exports","controller"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkAccess(){return!!this.getAcl().check("Calendar")}actionShow(e){this.actionIndex(e)}actionIndex(e){this.handleCheckAccess("");this.main("crm:views/calendar/calendar-page",{date:e.date,mode:e.mode,userId:e.userId,userName:e.userName})}}e.default=s});define("modules/crm/controllers/activities",["exports","controller"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkAccess(e){return!!this.getAcl().check("Activities")}actionActivities(e){this.processList("activities",e.entityType,e.id,e.targetEntityType)}actionHistory(e){this.processList("history",e.entityType,e.id,e.targetEntityType)}processList(t,i,s,a){let n;this.modelFactory.create(i).then(e=>{n=e;n.id=s;return n.fetch({main:!0})}).then(()=>this.collectionFactory.create(a)).then(e=>{e.url="Activities/"+n.entityType+"/"+s+"/"+t+"/list/"+a;this.main("crm:views/activities/list",{scope:i,model:n,collection:e,link:t+"_"+a,type:t})})}}e.default=s});define("modules/crm/acl-portal/document",["exports","acl-portal"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkModelEdit(e,t,i){var s=this.checkModel(e,t,"delete",i);return!!s||"account"===t.edit}}e.default=s});define("modules/crm/acl-portal/contact",["exports","acl-portal"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkIsOwnContact(e){var t=this.getUser().get("contactId");return!!t&&t===e.id}}e.default=s});define("modules/crm/acl-portal/account",["exports","acl-portal"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkInAccount(e){var t=this.getUser().getLinkMultipleIdList("accounts");return!!t.length&&!!~t.indexOf(e.id)}}e.default=s});define("modules/crm/acl/mass-email",["exports","acl"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkScope(e,t,i,s){return"create"===t?super.checkScope(e,"edit",i,s):super.checkScope(e,t,i,s)}checkIsOwner(e){return!!e.has("campaignId")||super.checkIsOwner(e)}checkInTeam(e){return!!e.has("campaignId")||super.checkInTeam(e)}}e.default=s});define("modules/crm/acl/campaign-tracking-url",["exports","acl"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{checkIsOwner(e){return!!e.has("campaignId")}checkInTeam(e){return!!e.has("campaignId")}}e.default=s});define("modules/crm/acl/call",["exports","modules/crm/acl/meeting"],function(e,t){Object.defineProperty(e,"__esModule",{value:!0});e.default=void 0;t=(i=t,i&&i.__esModule?i:{default:i});var i;class s extends t.default{}e.default=s});
//# sourceMappingURL=espo-crm.js.map