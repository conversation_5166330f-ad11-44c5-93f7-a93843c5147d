{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true, "pattern": "$noBadCharacters"}, "emailAddress": {"type": "<PERSON><PERSON><PERSON>", "required": true, "maxLength": 100, "tooltip": true, "view": "views/email-account/fields/email-address"}, "status": {"type": "enum", "options": ["Active", "Inactive"], "default": "Active"}, "host": {"type": "<PERSON><PERSON><PERSON>"}, "port": {"type": "int", "min": 0, "max": 65535, "default": 993}, "security": {"type": "enum", "default": "SSL", "options": ["", "SSL", "TLS"]}, "username": {"type": "<PERSON><PERSON><PERSON>"}, "password": {"type": "password"}, "monitoredFolders": {"type": "array", "default": ["INBOX"], "view": "views/email-account/fields/folders", "displayAsList": true, "noEmptyString": true, "duplicateIgnore": true}, "sentFolder": {"type": "<PERSON><PERSON><PERSON>", "view": "views/email-account/fields/folder", "duplicateIgnore": true}, "storeSentEmails": {"type": "bool", "tooltip": true}, "keepFetchedEmailsUnread": {"type": "bool"}, "fetchSince": {"type": "date", "validatorClassNameList": ["Espo\\Classes\\FieldValidators\\InboundEmail\\FetchSince\\Required"], "forceValidation": true}, "fetchData": {"type": "jsonObject", "readOnly": true, "duplicateIgnore": true}, "emailFolder": {"type": "link", "view": "views/email-account/fields/email-folder", "duplicateIgnore": true}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "assignedUser": {"type": "link", "required": true, "view": "views/fields/assigned-user"}, "connectedAt": {"type": "datetime", "readOnly": true}, "useImap": {"type": "bool", "default": true}, "useSmtp": {"type": "bool", "tooltip": true}, "smtpHost": {"type": "<PERSON><PERSON><PERSON>"}, "smtpPort": {"type": "int", "min": 0, "max": 65535, "default": 587}, "smtpAuth": {"type": "bool", "default": true}, "smtpSecurity": {"type": "enum", "default": "TLS", "options": ["", "SSL", "TLS"]}, "smtpUsername": {"type": "<PERSON><PERSON><PERSON>"}, "smtpPassword": {"type": "password"}, "smtpAuthMechanism": {"type": "enum", "options": ["login", "crammd5", "plain"], "default": "login"}, "imapHandler": {"type": "<PERSON><PERSON><PERSON>", "readOnly": true}, "smtpHandler": {"type": "<PERSON><PERSON><PERSON>", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true}, "modifiedBy": {"type": "link", "readOnly": true}}, "links": {"assignedUser": {"type": "belongsTo", "entity": "User"}, "createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "filters": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foreign": "parent", "entity": "<PERSON>ail<PERSON><PERSON><PERSON>"}, "emails": {"type": "hasMany", "entity": "Email", "foreign": "emailAccounts"}, "emailFolder": {"type": "belongsTo", "entity": "EmailFolder"}}, "collection": {"orderBy": "name", "order": "asc"}}