{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true}, "job": {"type": "<PERSON><PERSON><PERSON>", "required": true, "view": "views/scheduled-job/fields/job"}, "status": {"type": "enum", "options": ["Active", "Inactive"], "default": "Active", "audited": true}, "scheduling": {"type": "<PERSON><PERSON><PERSON>", "required": true, "view": "views/scheduled-job/fields/scheduling", "tooltip": true, "audited": true, "validatorClassNameList": ["Espo\\Classes\\FieldValidators\\ScheduledJob\\Scheduling\\Valid"]}, "lastRun": {"type": "datetime", "readOnly": true}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true}, "modifiedBy": {"type": "link", "readOnly": true}, "isInternal": {"type": "bool", "readOnly": true, "disabled": true, "default": false}}, "links": {"createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "log": {"type": "hasMany", "entity": "ScheduledJobLogRecord", "foreign": "<PERSON><PERSON><PERSON>"}}, "collection": {"orderBy": "name", "order": "asc"}, "jobSchedulingMap": {"CheckInboundEmails": "*/2 * * * *", "CheckEmailAccounts": "*/1 * * * *", "SendEmailReminders": "*/2 * * * *", "Cleanup": "1 1 * * 0", "AuthTokenControl": "*/6 * * * *", "SendEmailNotifications": "*/2 * * * *", "ProcessWebhookQueue": "*/2 * * * *", "SendScheduledEmails": "*/10 * * * *"}}