{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true, "pattern": "$noBadCharacters", "view": "views/inbound-email/fields/name"}, "emailAddress": {"type": "<PERSON><PERSON><PERSON>", "required": true, "maxLength": 100, "view": "views/inbound-email/fields/email-address"}, "status": {"type": "enum", "options": ["Active", "Inactive"], "default": "Active"}, "host": {"type": "<PERSON><PERSON><PERSON>"}, "port": {"type": "int", "min": 0, "max": 65535, "default": 993}, "security": {"type": "enum", "default": "SSL", "options": ["", "SSL", "TLS"]}, "username": {"type": "<PERSON><PERSON><PERSON>"}, "password": {"type": "password"}, "monitoredFolders": {"type": "array", "default": ["INBOX"], "view": "views/inbound-email/fields/folders", "displayAsList": true, "noEmptyString": true, "duplicateIgnore": true}, "fetchSince": {"type": "date", "validatorClassNameList": ["Espo\\Classes\\FieldValidators\\InboundEmail\\FetchSince\\Required"], "forceValidation": true}, "fetchData": {"type": "jsonObject", "readOnly": true, "duplicateIgnore": true}, "assignToUser": {"type": "link", "tooltip": true}, "team": {"type": "link", "tooltip": true}, "teams": {"type": "linkMultiple", "tooltip": true}, "addAllTeamUsers": {"type": "bool", "tooltip": true, "default": true}, "isSystem": {"type": "bool", "notStorable": true, "readOnly": true, "directAccessDisabled": true, "tooltip": true}, "sentFolder": {"type": "<PERSON><PERSON><PERSON>", "view": "views/inbound-email/fields/folder", "duplicateIgnore": true}, "storeSentEmails": {"type": "bool", "tooltip": true}, "keepFetchedEmailsUnread": {"type": "bool"}, "connectedAt": {"type": "datetime", "readOnly": true}, "excludeFromReply": {"type": "bool", "tooltip": true}, "useImap": {"type": "bool", "default": true}, "useSmtp": {"type": "bool", "tooltip": true}, "smtpIsShared": {"type": "bool", "tooltip": true}, "smtpIsForMassEmail": {"type": "bool", "tooltip": true}, "smtpHost": {"type": "<PERSON><PERSON><PERSON>"}, "smtpPort": {"type": "int", "min": 0, "max": 65535, "default": 587}, "smtpAuth": {"type": "bool", "default": true}, "smtpSecurity": {"type": "enum", "default": "TLS", "options": ["", "SSL", "TLS"]}, "smtpUsername": {"type": "<PERSON><PERSON><PERSON>"}, "smtpPassword": {"type": "password"}, "smtpAuthMechanism": {"type": "enum", "options": ["login", "crammd5", "plain"], "default": "login"}, "createCase": {"type": "bool", "tooltip": true}, "caseDistribution": {"type": "enum", "options": ["", "Direct-Assignment", "Round-Robin", "Least-<PERSON>y"], "default": "Direct-Assignment", "tooltip": true}, "targetUserPosition": {"type": "enum", "view": "views/inbound-email/fields/target-user-position", "tooltip": true}, "reply": {"type": "bool", "tooltip": true}, "replyEmailTemplate": {"type": "link"}, "replyFromAddress": {"type": "<PERSON><PERSON><PERSON>"}, "replyToAddress": {"type": "<PERSON><PERSON><PERSON>", "view": "views/fields/email-address", "tooltip": true}, "replyFromName": {"type": "<PERSON><PERSON><PERSON>"}, "fromName": {"type": "<PERSON><PERSON><PERSON>"}, "groupEmailFolder": {"type": "link", "tooltip": true}, "imapHandler": {"type": "<PERSON><PERSON><PERSON>", "readOnly": true}, "smtpHandler": {"type": "<PERSON><PERSON><PERSON>", "readOnly": true}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true}, "modifiedBy": {"type": "link", "readOnly": true}}, "links": {"createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "foreign": "inboundEmails"}, "assignToUser": {"type": "belongsTo", "entity": "User"}, "team": {"type": "belongsTo", "entity": "Team"}, "replyEmailTemplate": {"type": "belongsTo", "entity": "EmailTemplate"}, "filters": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foreign": "parent", "entity": "<PERSON>ail<PERSON><PERSON><PERSON>"}, "emails": {"type": "hasMany", "entity": "Email", "foreign": "inboundEmails"}, "groupEmailFolder": {"type": "belongsTo", "entity": "GroupEmailFolder"}}, "collection": {"orderBy": "name", "order": "asc"}}