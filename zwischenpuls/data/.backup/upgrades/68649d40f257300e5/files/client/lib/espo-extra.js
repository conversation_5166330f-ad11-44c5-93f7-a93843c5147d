/*! espocrm 2025-05-27 */
define("views/import/record/panels/imported",["exports","views/record/panels/relationship"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{link="imported";readOnly=!0;rowActionsView="views/record/row-actions/relationship-no-unlink";setup(){this.entityType=this.model.get("entityType"),this.title=this.title||this.translate("Imported","labels","Import"),super.setup()}}e.default=s}),define("views/email-account/record/detail",["exports","views/record/detail"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{setup(){super.setup(),this.setupFieldsBehaviour(),this.initSslFieldListening(),this.initSmtpFieldsControl(),this.getUser().isAdmin()?this.setFieldNotReadOnly("assignedUser"):this.setFieldReadOnly("assignedUser")}modifyDetailLayout(e){e.filter(e=>"$label:SMTP"===e.tabLabel).forEach(e=>{e.rows.forEach(e=>{e.forEach(e=>{var t=this.translate(e.name,"fields","EmailAccount");t&&0===t.indexOf("SMTP ")&&(e.labelText=Espo.Utils.upperCaseFirst(t.substring(5)))})})})}setupFieldsBehaviour(){this.controlStatusField(),this.listenTo(this.model,"change:status",(e,t,i)=>{i.ui&&this.controlStatusField()}),this.listenTo(this.model,"change:useImap",(e,t,i)=>{i.ui&&this.controlStatusField()}),this.wasFetched()?this.setFieldReadOnly("fetchSince"):this.setFieldNotReadOnly("fetchSince")}controlStatusField(){var e=["username","port","host","monitoredFolders"];"Active"===this.model.get("status")&&this.model.get("useImap")?e.forEach(e=>{this.setFieldRequired(e)}):e.forEach(e=>{this.setFieldNotRequired(e)})}wasFetched(){return!this.model.isNew()&&!!(this.model.get("fetchData")||{}).lastUID}initSslFieldListening(){this.listenTo(this.model,"change:security",(e,t,i)=>{i.ui&&("SSL"===t?this.model.set("port",993):this.model.set("port",143))}),this.listenTo(this.model,"change:smtpSecurity",(e,t,i)=>{i.ui&&("SSL"===t?this.model.set("smtpPort",465):"TLS"===t?this.model.set("smtpPort",587):this.model.set("smtpPort",25))})}initSmtpFieldsControl(){this.controlSmtpFields(),this.listenTo(this.model,"change:useSmtp",this.controlSmtpFields,this),this.listenTo(this.model,"change:smtpAuth",this.controlSmtpFields,this)}controlSmtpFields(){this.model.get("useSmtp")?(this.showField("smtpHost"),this.showField("smtpPort"),this.showField("smtpAuth"),this.showField("smtpSecurity"),this.showField("smtpTestSend"),this.setFieldRequired("smtpHost"),this.setFieldRequired("smtpPort"),this.controlSmtpAuthField()):(this.hideField("smtpHost"),this.hideField("smtpPort"),this.hideField("smtpAuth"),this.hideField("smtpUsername"),this.hideField("smtpPassword"),this.hideField("smtpAuthMechanism"),this.hideField("smtpSecurity"),this.hideField("smtpTestSend"),this.setFieldNotRequired("smtpHost"),this.setFieldNotRequired("smtpPort"),this.setFieldNotRequired("smtpUsername"))}controlSmtpAuthField(){this.model.get("smtpAuth")?(this.showField("smtpUsername"),this.showField("smtpPassword"),this.showField("smtpAuthMechanism"),this.setFieldRequired("smtpUsername")):(this.hideField("smtpUsername"),this.hideField("smtpPassword"),this.hideField("smtpAuthMechanism"),this.setFieldNotRequired("smtpUsername"))}}e.default=s}),define("views/personal-data/record/record",["exports","views/record/base"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{template="personal-data/record/record";additionalEvents={"click .checkbox":function(e){var t=$(e.currentTarget).data("name");e.currentTarget.checked?(~this.checkedFieldList.indexOf(t)||this.checkedFieldList.push(t),this.checkedFieldList.length===this.fieldList.length?this.$el.find(".checkbox-all").prop("checked",!0):this.$el.find(".checkbox-all").prop("checked",!1)):(~(e=this.checkedFieldList.indexOf(t))&&this.checkedFieldList.splice(e,1),this.$el.find(".checkbox-all").prop("checked",!1)),this.trigger("check",this.checkedFieldList)},"click .checkbox-all":function(e){e.currentTarget.checked?(this.checkedFieldList=Espo.Utils.clone(this.fieldList),this.$el.find(".checkbox").prop("checked",!0)):(this.checkedFieldList=[],this.$el.find(".checkbox").prop("checked",!1)),this.trigger("check",this.checkedFieldList)}};checkedFieldList;data(){var e={};return e.fieldDataList=this.getFieldDataList(),e.scope=this.scope,e.editAccess=this.editAccess,e}setup(){super.setup(),this.events={...this.additionalEvents,...this.events},this.scope=this.model.entityType,this.fieldList=[],this.checkedFieldList=[],this.editAccess=this.getAcl().check(this.model,"edit");let s=this.getMetadata().get(["entityDefs",this.scope,"fields"])||{};var e,t=[];for(e in s)s[e].isPersonalData&&t.push(e);t.forEach(e=>{var t=s[e].type,t=this.getFieldManager().getActualAttributeList(t,e);let i=!1;t.forEach(e=>{e=this.model.get(e);!e||"[object Array]"===Object.prototype.toString.call(e)&&e.length||(i=!0)});t=!this.getAcl().getScopeForbiddenFieldList(this.scope).includes(e);i&&t&&this.fieldList.push(e)}),this.fieldList=this.fieldList.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),this.fieldList.forEach(e=>{this.createField(e,null,null,"detail",!0)})}getFieldDataList(){let t=this.getAcl().getScopeForbiddenFieldList(this.scope,"edit"),i=[];return this.fieldList.forEach(e=>{i.push({name:e,key:e+"Field",editAccess:this.editAccess&&!~t.indexOf(e)})}),i}}e.default=s}),define("views/personal-data/modals/personal-data",["exports","views/modal"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{template="personal-data/modals/personal-data";className="dialog dialog-record";backdrop=!0;setup(){super.setup(),this.buttonList=[{name:"cancel",label:"Close"}],this.headerText=this.getLanguage().translate("Personal Data"),this.headerText+=": "+this.model.get("name"),this.getAcl().check(this.model,"edit")&&this.buttonList.unshift({name:"erase",label:"Erase",style:"danger",disabled:!0,onClick:()=>this.actionErase()}),this.fieldList=[],this.scope=this.model.entityType,this.createView("record","views/personal-data/record/record",{selector:".record",model:this.model},e=>{this.listenTo(e,"check",e=>{(this.fieldList=e).length?this.enableButton("erase"):this.disableButton("erase")}),e.fieldList.length||this.disableButton("export")})}actionErase(){this.confirm({message:this.translate("erasePersonalDataConfirmation","messages"),confirmText:this.translate("Erase")},()=>{this.disableButton("erase"),Espo.Ajax.postRequest("DataPrivacy/action/erase",{fieldList:this.fieldList,entityType:this.scope,id:this.model.id}).then(()=>{Espo.Ui.success(this.translate("Done")),this.trigger("erase")}).catch(()=>{this.enableButton("erase")})})}}e.default=s}),define("views/outbound-email/modals/test-send",["exports","views/modal"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{cssName="test-send";templateContent=`
        <label class="control-label">{{translate 'Email Address' scope='Email'}}</label>
        <input type="text" name="emailAddress" value="{{emailAddress}}" class="form-control">
    `;data(){return{emailAddress:this.options.emailAddress}}setup(){this.buttonList=[{name:"send",text:this.translate("Send","labels","Email"),style:"primary",onClick:()=>{var e=this.$el.find("input").val();""!==e&&this.trigger("send",e)}},{name:"cancel",label:"Cancel",onClick:e=>{e.close()}}]}}e.default=s}),define("views/import-error/fields/validation-failures",["exports","views/fields/base"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{detailTemplateContent=`
        {{#if itemList.length}}
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th style="width: 50%;">{{translate 'Field'}}</th>
                    <th>{{translateOption 'Validation' scope='ImportError' field='type'}}</th>
                </tr>
            </thead>
            <tbody>
                {{#each itemList}}
                <tr>
                    <td>{{translate field category='fields' scope=entityType}}</td>
                    <td>
                        {{translate type category='fieldValidations'}}
                        {{#if popoverText}}
                        <a
                            role="button"
                            tabindex="-1"
                            class="text-danger popover-anchor"
                            data-text="{{popoverText}}"
                        ><span class="fas fa-info-circle"></span></a>
                        {{/if}}
                    </td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{else}}
        <span class="none-value">{{translate 'None'}}</span>
        {{/if}}
    `;data(){var e=super.data();return e.itemList=this.getDataList(),e}afterRenderDetail(){this.$el.find(".popover-anchor").each((e,t)=>{var i=this.getHelper().transformMarkdownText(t.dataset.text).toString();Espo.Ui.popover($(t),{content:i},this)})}getDataList(){var e=Espo.Utils.cloneDeep(this.model.get(this.name))||[];let s=this.model.get("entityType");return Array.isArray(e)&&e.forEach(e=>{var t=this.getFieldManager(),i=this.getLanguage(),t=t.getEntityTypeFieldParam(s,e.field,"type");if(t)return t=t+"_"+e.type,i.has(t,"fieldValidationExplanations","Global")?void(e.popoverText=i.translate(t,"fieldValidationExplanations")):i.has(e.type,"fieldValidationExplanations","Global")?void(e.popoverText=i.translate(e.type,"fieldValidationExplanations")):void 0}),e}}e.default=s}),define("views/import-error/fields/line-number",["exports","views/fields/int"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{disableFormatting=!0;data(){var e=super.data();return e.valueIsSet=this.model.has(this.sourceName),e.isNotEmpty=this.model.has(this.sourceName),e}setup(){super.setup(),this.sourceName="exportLineNumber"===this.name?"exportRowIndex":"rowIndex"}getAttributeList(){return[this.sourceName]}getValueForDisplay(){var e=this.model.get(this.sourceName);return this.formatNumber(++e)}}e.default=s}),define("views/import/step2",["exports","view","ui/select"],function(e,t,i){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),i=s(i);class a extends t.default{template="import/step-2";allowedFieldList=["createdAt","createdBy"];events={'click button[data-action="back"]':function(){this.back()},'click button[data-action="next"]':function(){this.next()},'click a[data-action="addField"]':function(e){e=$(e.currentTarget).data("name");this.addField(e)},'click a[data-action="removeField"]':function(e){var e=$(e.currentTarget).data("name"),t=(this.$el.find('a[data-action="addField"]').parent().removeClass("hidden"),this.additionalFields.indexOf(e));~t&&this.additionalFields.splice(t,1),this.$el.find('.field[data-name="'+e+'"]').parent().remove()},"keyup input.add-field-quick-search-input":function(e){this.processFieldFilterQuickSearch(e.currentTarget.value)}};data(){return{scope:this.scope,fieldList:this.getFieldList()}}setup(){this.formData=this.options.formData,this.scope=this.formData.entityType;let i=[];if(this.additionalFields=[],this.formData.previewArray){let e=0;this.formData.headerRow&&(e=1),this.formData.previewArray.length>e&&this.formData.previewArray[e].forEach((e,t)=>{e={value:e};this.formData.headerRow&&(e.name=this.formData.previewArray[0][t]),i.push(e)})}this.wait(!0),this.getModelFactory().create(this.scope,e=>{this.model=e,this.formData.defaultValues&&this.model.set(this.formData.defaultValues),this.wait(!1)}),this.mapping=i,this.fieldList=this.getFieldList(),this.fieldTranslations=this.fieldList.reduce((e,t)=>(e[t]=this.translate(t,"fields",this.scope),e),{})}afterRender(){var e=$("#mapping-container"),t=$("<table>").addClass("table").addClass("table-bordered").css("table-layout","fixed");let l=$("<tbody>").appendTo(t),d=$("<tr>");if(this.formData.headerRow){let e=$("<th>").attr("width","25%").text(this.translate("Header Row Value","labels","Import"));d.append(e)}let o=$("<th>").attr("width","25%").text(this.translate("Field","labels","Import")),r=(d.append(o),o=$("<th>").text(this.translate("First Row Value","labels","Import")),d.append(o),~["update","createAndUpdate"].indexOf(this.formData.action)&&(o=$("<th>").text(this.translate("Update by","labels","Import")),d.append(o)),l.append(d),[]);this.mapping.forEach((e,t)=>{d=$("<tr>"),this.formData.headerRow&&(o=$("<td>").text(e.name),d.append(o));let i=e.name;this.formData.attributeList&&(i=this.formData.attributeList[t]||null);var s=this.getFieldDropdown(t,i);r.push(s.get(0)),o=$("<td>").append(s),d.append(o);let a=e.value||"";200<a.length&&(a=a.substring(0,200)+"..."),o=$("<td>").css("overflow","hidden").text(a),d.append(o),~["update","createAndUpdate"].indexOf(this.formData.action)&&(s=$("<input>").attr("type","checkbox").addClass("form-checkbox").attr("id","update-by-"+t.toString()).get(0),this.formData.updateBy?~this.formData.updateBy.indexOf(t)&&(s.checked=!0):"id"===e.name&&(s.checked=!0),o=$("<td>").append(s),d.append(o)),l.append(d)}),e.empty(),e.append(t),this.getDefaultFieldList().forEach(e=>{this.addField(e)}),this.$addFieldButton=this.$el.find("button.add-field"),this.$defaultFieldList=this.$el.find("ul.default-field-list"),this.$fieldQuickSearch=this.$el.find("input.add-field-quick-search-input"),this.initQuickSearchUi(),r.forEach(e=>i.default.init(e))}resetFieldFilterQuickSearch(){this.$fieldQuickSearch.val(""),this.$defaultFieldList.find("li.item").removeClass("hidden")}initQuickSearchUi(){this.$addFieldButton.parent().on("show.bs.dropdown",()=>{setTimeout(()=>{this.$fieldQuickSearch.focus();var e=this.$fieldQuickSearch.outerWidth();this.$fieldQuickSearch.css("minWidth",e)},1)}),this.$addFieldButton.parent().on("hide.bs.dropdown",()=>{this.resetFieldFilterQuickSearch(),this.$fieldQuickSearch.css("minWidth","")})}processFieldFilterQuickSearch(a){a=(a=a.trim()).toLowerCase(),console.log(a);let l=this.$defaultFieldList.find("li.item");""===a?l.removeClass("hidden"):(l.addClass("hidden"),this.fieldList.forEach(e=>{let t=this.fieldTranslations[e]||e;var i=(t=t.toLowerCase()).split(" ");let s=0===t.indexOf(a);(s=s||0<i.filter(e=>3<e.length&&0===e.indexOf(a)).length)&&l.filter(`[data-name="${e}"]`).removeClass("hidden")}))}getDefaultFieldList(){if(this.formData.defaultFieldList)return this.formData.defaultFieldList;if(!this.formData.defaultValues)return[];let t=Object.keys(this.formData.defaultValues);return this.getFieldManager().getEntityTypeFieldList(this.scope).filter(e=>-1!==this.getFieldManager().getEntityTypeFieldActualAttributeList(this.scope,e).findIndex(e=>t.includes(e)))}getFieldList(){var e,t,i=this.getMetadata().get("entityDefs."+this.scope+".fields"),s=this.getAcl().getScopeForbiddenFieldList(this.scope,"edit");let a=[];for(e in i)!~s.indexOf(e)&&(t=i[e],~this.allowedFieldList.indexOf(e)||!t.disabled&&!t.importDisabled)&&a.push(e);return a=a.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope)))}getAttributeList(){var e=this.getMetadata().get(["entityDefs",this.scope,"fields"])||{},i=this.getAcl().getScopeForbiddenFieldList(this.scope,"edit");let s=[];s.push("id");for(let t in e)if(!~i.indexOf(t)){var a=e[t];if(this.allowedFieldList.includes(t)||!(a.disabled&&!a.importNotDisabled||a.importDisabled))if("phone"===a.type)s.push(t),(this.getMetadata().get(`entityDefs.${this.scope}.fields.${t}.typeList`)||[]).map(e=>e.replace(/\s/g,"_")).forEach(e=>{s.push(t+Espo.Utils.upperCaseFirst(e))});else if("email"===a.type&&(s.push(t+"2"),s.push(t+"3"),s.push(t+"4")),"link"===a.type&&(s.push(t+"Name"),s.push(t+"Id")),"foreign"!==a.type||a.relateOnImport){"personName"===a.type&&s.push(t);a=a.type;let e=this.getFieldManager().getActualAttributeList(a,t);(e=e.length?e:[t]).forEach(e=>{-1===s.indexOf(e)&&s.push(e)})}}return s=s.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope)))}getFieldDropdown(e,s){s=s||!1;var t=this.getAttributeList();let a=$("<select>").addClass("form-control").attr("id","column-"+e.toString()),l=$("<option>").val("").text("-"+this.translate("Skip","labels","Import")+"-"),d=this.formData.entityType;return a.append(l),t.forEach(e=>{let t="";var i;this.getLanguage().has(e,"fields",d)||this.getLanguage().has(e,"fields","Global")?t=this.translate(e,"fields",d):e.indexOf("Id")===e.length-2?(i=e.substr(0,e.length-2),this.getMetadata().get(["entityDefs",d,"fields",i])&&(t=this.translate(i,"fields",d)+" ("+this.translate("id","fields")+")")):e.indexOf("Name")===e.length-4?(i=e.substr(0,e.length-4),this.getMetadata().get(["entityDefs",d,"fields",i])&&(t=this.translate(i,"fields",d)+" ("+this.translate("name","fields")+")")):e.indexOf("Type")===e.length-4?(i=e.substr(0,e.length-4),this.getMetadata().get(["entityDefs",d,"fields",i])&&(t=this.translate(i,"fields",d)+" ("+this.translate("type","fields")+")")):0===e.indexOf("phoneNumber")?(i=e.substr(11),i=this.getLanguage().translateOption(i,"phoneNumber",d),t=this.translate("phoneNumber","fields",d)+" ("+i+")"):0===e.indexOf("emailAddress")&&parseInt(e.substr(12)).toString()===e.substr(12)?(i=e.substr(12),t=this.translate("emailAddress","fields",d)+" "+i.toString()):e.indexOf("Ids")===e.length-3&&(i=e.substr(0,e.length-3),this.getMetadata().get(["entityDefs",d,"fields",i]))&&(t=this.translate(i,"fields",d)+" ("+this.translate("ids","fields")+")"),t=t||e,l=$("<option>").val(e).text(t),!s||e!==s&&s.toLowerCase().replace(/_/g,"")!==e.toLowerCase()||l.prop("selected",!0),a.append(l)}),a}addField(t){this.$el.find('[data-action="addField"][data-name="'+t+'"]').parent().addClass("hidden"),$(this.containerSelector+' button[data-name="update"]').removeClass("disabled"),Espo.Ui.notifyWait();var e=this.translate(t,"fields",this.scope),e=this.getHelper().escapeString(e),e='<div class="cell form-group">'+('<a role="button" class="pull-right" data-action="removeField" data-name="'+t+'"><span class="fas fa-times"></span></a>')+'<label class="control-label">'+e+'</label><div class="field" data-name="'+t+'"/></div>',e=($("#default-values-container").append(e),Espo.Utils.upperCaseFirst(this.model.getFieldParam(t,"type"))),e=this.getMetadata().get(["entityDefs",this.scope,"fields",t,"view"])||this.getFieldManager().getViewName(e);this.createView(t,e,{model:this.model,fullSelector:this.getSelector()+' .field[data-name="'+t+'"]',defs:{name:t},mode:"edit",readOnlyDisabled:!0},e=>{this.additionalFields.push(t),e.render(),e.notify(!1)}),this.resetFieldFilterQuickSearch()}disableButtons(){this.$el.find('button[data-action="next"]').addClass("disabled").attr("disabled","disabled"),this.$el.find('button[data-action="back"]').addClass("disabled").attr("disabled","disabled")}enableButtons(){this.$el.find('button[data-action="next"]').removeClass("disabled").removeAttr("disabled"),this.$el.find('button[data-action="back"]').removeClass("disabled").removeAttr("disabled")}getFieldView(e){return this.getView(e)}fetch(e){let t={},i=(this.additionalFields.forEach(e=>{e=this.getFieldView(e);_.extend(t,e.fetch())}),this.model.set(t),!1);if(this.additionalFields.forEach(e=>{e=this.getFieldView(e);i=e.validate()||i}),i||(this.formData.defaultValues=t),i&&!e)return!1;this.formData.defaultFieldList=Espo.Utils.clone(this.additionalFields);let s=[];if(this.mapping.forEach((e,t)=>{s.push($("#column-"+t).val())}),this.formData.attributeList=s,~["update","createAndUpdate"].indexOf(this.formData.action)){let i=[];this.mapping.forEach((e,t)=>{$("#update-by-"+t).get(0).checked&&i.push(t)}),this.formData.updateBy=i}return this.getParentIndexView().formData=this.formData,this.getParentIndexView().trigger("change"),!0}getParentIndexView(){return this.getParentView()}back(){this.fetch(!0),this.getParentIndexView().changeStep(1)}next(){this.fetch()&&(this.disableButtons(),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("Import/file",null,{timeout:0,contentType:"text/csv",data:this.getParentIndexView().fileContents}).then(e=>{e.attachmentId?this.runImport(e.attachmentId):Espo.Ui.error(this.translate("Bad response"))}))}runImport(e){this.formData.attachmentId=e,this.getRouter().confirmLeaveOut=!1,Espo.Ui.notify(this.translate("importRunning","messages","Import")),Espo.Ajax.postRequest("Import",this.formData,{timeout:0}).then(e=>{let t=e.id;this.getParentIndexView().trigger("done"),t?(this.formData.manualMode?this.createView("dialog","views/modal",{templateContent:"{{complexText viewObject.options.msg}}",headerText:" ",backdrop:"static",msg:this.translate("commandToRun","strings","Import")+":\n\n```php command.php import --id="+t+"```",buttonList:[{name:"close",label:this.translate("Close")}]},e=>{e.render(),this.listenToOnce(e,"close",()=>{this.getRouter().navigate("#Import/view/"+t,{trigger:!0})})}):this.getRouter().navigate("#Import/view/"+t,{trigger:!0}),Espo.Ui.notify(!1)):(Espo.Ui.error(this.translate("Error"),!0),this.enableButtons())}).catch(()=>this.enableButtons())}}e.default=a}),define("views/import/step1",["exports","view","model","intl-tel-input-globals"],function(e,t,h,c){function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=i(t),h=i(h),c=i(c);class s extends t.default{template="import/step-1";events={"change #import-file":function(e){e=e.currentTarget.files;e.length&&this.loadFile(e[0])},'click button[data-action="next"]':function(){this.next()},'click button[data-action="saveAsDefault"]':function(){this.saveAsDefault()}};getEntityList(){var e,t=[],i=this.getMetadata().get("scopes");for(e in i)i[e].importable&&this.getAcl().checkScope(e,"create")&&t.push(e);return t.sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),t}data(){return{entityList:this.getEntityList()}}setup(){this.attributeList=["entityType","action"],this.paramList=["headerRow","decimalMark","personNameFormat","delimiter","dateFormat","timeFormat","currency","timezone","textQualifier","silentMode","idleMode","skipDuplicateChecking","manualMode","phoneNumberCountry"],this.paramList.forEach(e=>{this.attributeList.push(e)}),this.formData=this.options.formData||{entityType:this.options.entityType||null,create:"create",headerRow:!0,delimiter:",",textQualifier:'"',dateFormat:"YYYY-MM-DD",timeFormat:"HH:mm:ss",currency:this.getConfig().get("defaultCurrency"),timezone:"UTC",decimalMark:".",personNameFormat:"f l",idleMode:!1,skipDuplicateChecking:!1,silentMode:!0,manualMode:!1};var e=Espo.Utils.cloneDeep((this.getPreferences().get("importParams")||{}).default||{});if(!this.options.formData)for(var t in e)this.formData[t]=e[t];let i=this.model=new h.default;this.attributeList.forEach(e=>{i.set(e,this.formData[e])}),this.attributeList.forEach(s=>{this.listenTo(i,"change:"+s,(e,t,i)=>{i.ui&&(this.formData[s]=this.model.get(s),this.preview())})});var s=["f l","l f","l, f"],a=(~(this.getConfig().get("personNameFormat")||"firstLast").toString().toLowerCase().indexOf("middle")&&(s.push("f m l"),s.push("l f m")),this.getDateFormatDataList()),l=this.getTimeFormatDataList();let d=[],o={},r=(a.forEach(e=>{d.push(e.key),o[e.key]=e.label}),[]),n={};l.forEach(e=>{r.push(e.key),n[e.key]=e.label}),this.createView("actionField","views/fields/enum",{selector:'.field[data-name="action"]',model:this.model,name:"action",mode:"edit",params:{options:["create","createAndUpdate","update"],translatedOptions:{create:this.translate("Create Only","labels","Import"),createAndUpdate:this.translate("Create and Update","labels","Import"),update:this.translate("Update Only","labels","Import")}}}),this.createView("entityTypeField","views/fields/enum",{selector:'.field[data-name="entityType"]',model:this.model,name:"entityType",mode:"edit",params:{options:[""].concat(this.getEntityList()),translation:"Global.scopeNamesPlural",required:!0},labelText:this.translate("Entity Type","labels","Import")}),this.createView("decimalMarkField","views/fields/varchar",{selector:'.field[data-name="decimalMark"]',model:this.model,name:"decimalMark",mode:"edit",params:{options:[".",","],maxLength:1,required:!0},labelText:this.translate("Decimal Mark","labels","Import")}),this.createView("personNameFormatField","views/fields/enum",{selector:'.field[data-name="personNameFormat"]',model:this.model,name:"personNameFormat",mode:"edit",params:{options:s,translation:"Import.options.personNameFormat"}}),this.createView("delimiterField","views/fields/enum",{selector:'.field[data-name="delimiter"]',model:this.model,name:"delimiter",mode:"edit",params:{options:[",",";","\\t","|"]}}),this.createView("textQualifierField","views/fields/enum",{selector:'.field[data-name="textQualifier"]',model:this.model,name:"textQualifier",mode:"edit",params:{options:['"',"'"],translatedOptions:{'"':this.translate("Double Quote","labels","Import"),"'":this.translate("Single Quote","labels","Import")}}}),this.createView("dateFormatField","views/fields/enum",{selector:'.field[data-name="dateFormat"]',model:this.model,name:"dateFormat",mode:"edit",params:{options:d,translatedOptions:o}}),this.createView("timeFormatField","views/fields/enum",{selector:'.field[data-name="timeFormat"]',model:this.model,name:"timeFormat",mode:"edit",params:{options:r,translatedOptions:n}}),this.createView("currencyField","views/fields/enum",{selector:'.field[data-name="currency"]',model:this.model,name:"currency",mode:"edit",params:{options:this.getConfig().get("currencyList")}}),this.createView("timezoneField","views/fields/enum",{selector:'.field[data-name="timezone"]',model:this.model,name:"timezone",mode:"edit",params:{options:this.getMetadata().get(["entityDefs","Settings","fields","timeZone","options"])}}),this.createView("headerRowField","views/fields/bool",{selector:'.field[data-name="headerRow"]',model:this.model,name:"headerRow",mode:"edit"}),this.createView("silentModeField","views/fields/bool",{selector:'.field[data-name="silentMode"]',model:this.model,name:"silentMode",mode:"edit",tooltip:!0,tooltipText:this.translate("silentMode","tooltips","Import")}),this.createView("idleModeField","views/fields/bool",{selector:'.field[data-name="idleMode"]',model:this.model,name:"idleMode",mode:"edit"}),this.createView("skipDuplicateCheckingField","views/fields/bool",{selector:'.field[data-name="skipDuplicateChecking"]',model:this.model,name:"skipDuplicateChecking",mode:"edit"}),this.createView("manualModeField","views/fields/bool",{selector:'.field[data-name="manualMode"]',model:this.model,name:"manualMode",mode:"edit",tooltip:!0,tooltipText:this.translate("manualMode","tooltips","Import")}),this.createView("phoneNumberCountryField","views/fields/enum",{selector:'.field[data-name="phoneNumberCountry"]',model:this.model,name:"phoneNumberCountry",mode:"edit",params:{options:["",...c.default.getCountryData().map(e=>e.iso2)]},translatedOptions:c.default.getCountryData().reduce((e,t)=>(e[t.iso2]=t.iso2.toUpperCase()+" +"+t.dialCode,e),{})}),this.listenTo(this.model,"change",(i,e)=>{if(e.ui){let t=!1;this.paramList.forEach(e=>{i.hasChanged(e)&&(t=!0)}),t&&this.showSaveAsDefaultButton()}}),this.listenTo(this.model,"change",()=>{this.isRendered()&&this.controlFieldVisibility()}),this.listenTo(this.model,"change:entityType",()=>{delete this.formData.defaultFieldList,delete this.formData.defaultValues,delete this.formData.attributeList,delete this.formData.updateBy}),this.listenTo(this.model,"change:action",()=>{delete this.formData.updateBy}),this.listenTo(this.model,"change",(e,t)=>{t.ui&&(this.getRouter().confirmLeaveOut=!0)})}afterRender(){this.setupFormData(),this.getParentIndexView()&&this.getParentIndexView().fileContents&&(this.setFileIsLoaded(),this.preview()),this.controlFieldVisibility()}getParentIndexView(){return this.getParentView()}showSaveAsDefaultButton(){this.$el.find('[data-action="saveAsDefault"]').removeClass("hidden")}hideSaveAsDefaultButton(){this.$el.find('[data-action="saveAsDefault"]').addClass("hidden")}getFieldView(e){return this.getView(e+"Field")}next(){this.attributeList.forEach(e=>{this.getFieldView(e).fetchToModel(),this.formData[e]=this.model.get(e)});let t=!1;this.attributeList.forEach(e=>{t|=this.getFieldView(e).validate()}),t?Espo.Ui.error(this.translate("Not valid")):(this.getParentIndexView().formData=this.formData,this.getParentIndexView().trigger("change"),this.getParentIndexView().changeStep(2))}setupFormData(){this.attributeList.forEach(e=>{this.model.set(e,this.formData[e])})}loadFile(t){var e=t.slice(0,524288),i=new FileReader,i=(i.onloadend=e=>{e.target.readyState===FileReader.DONE&&(this.formData.previewString=e.target.result,this.preview())},i.readAsText(e),new FileReader);i.onloadend=e=>{e.target.readyState===FileReader.DONE&&(this.getParentIndexView().fileContents=e.target.result,this.setFileIsLoaded(),this.getRouter().confirmLeaveOut=!0,this.setFileName(t.name))},i.readAsText(t)}setFileName(e){this.$el.find(".import-file-name").text(e),this.$el.find(".import-file-info").text("")}setFileIsLoaded(){this.$el.find('button[data-action="next"]').removeClass("hidden")}preview(){if(this.formData.previewString){var e=this.csvToArray(this.formData.previewString,this.formData.delimiter,this.formData.textQualifier),t=(this.formData.previewArray=e,$("<table>").addClass("table").addClass("table-bordered"));let i=$("<tbody>").appendTo(t);e.forEach((e,t)=>{if(!(3<=t)){let t=$("<tr>");e.forEach(e=>{e=$("<td>").html(this.getHelper().sanitizeHtml(e));t.append(e)}),i.append(t)}}),$("#import-preview").empty().append(t)}}csvToArray(e,t,i){i=i||'"',t=(t=t||",").replace(/\\t/,"\t");for(var s=new RegExp("(\\"+t+"|\\r?\\n|\\r|^)(?:"+i+"([^"+i+"]*(?:"+i+i+"[^"+i+"]*)*)"+i+"|([^"+i+"\\"+t+"\\r\\n]*))","gi"),a=[[]],l=null;l=s.exec(e);){var d=l[1];d.length&&d!==t&&a.push([]),d=l[2]?l[2].replace(new RegExp('""',"g"),'"'):l[3],a[a.length-1].push(d)}return a}saveAsDefault(){var e=this.getPreferences(),t=Espo.Utils.cloneDeep(e.get("importParams")||{});let i={};this.paramList.forEach(e=>{i[e]=this.model.get(e)}),t.default=i,e.save({importParams:t}).then(()=>{Espo.Ui.success(this.translate("Saved"))}),this.hideSaveAsDefaultButton()}controlFieldVisibility(){this.model.get("idleMode")?this.hideField("manualMode"):this.showField("manualMode"),this.model.get("manualMode")?this.hideField("idleMode"):this.showField("idleMode")}hideField(e){this.$el.find('.field[data-name="'+e+'"]').parent().addClass("hidden-cell")}showField(e){this.$el.find('.field[data-name="'+e+'"]').parent().removeClass("hidden-cell")}convertFormatToLabel(e){var t,i={YYYY:"2021",DD:"27",MM:"12",HH:"23",mm:"00",hh:"11",ss:"00",a:"pm",A:"PM"};let s=e;for(t in i){var a=i[t];s=s.replace(new RegExp(t,"g"),a)}return e+" · "+s}getDateFormatDataList(){return(this.getMetadata().get(["clientDefs","Import","dateFormatList"])||[]).map(e=>({key:e,label:this.convertFormatToLabel(e)}))}getTimeFormatDataList(){return(this.getMetadata().get(["clientDefs","Import","timeFormatList"])||[]).map(e=>({key:e,label:this.convertFormatToLabel(e)}))}}e.default=s}),define("views/import/list",["exports","views/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{createButton=!1;setup(){super.setup(),this.menu.buttons.unshift({iconHtml:'<span class="fas fa-plus fa-sm"></span>',text:this.translate("New Import","labels","Import"),link:"#Import",acl:"edit"})}}e.default=s}),define("views/import/index",["exports","view"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{template="import/index";formData=null;fileContents=null;data(){return{fromAdmin:this.options.fromAdmin}}setup(){this.entityType=this.options.entityType||null,this.startFromStep=1,(this.options.formData||this.options.fileContents)&&(this.formData=this.options.formData||{},this.fileContents=this.options.fileContents||null,this.entityType=this.formData.entityType||null,this.options.step)&&(this.startFromStep=this.options.step)}changeStep(e,t){1<(this.step=e)&&this.setConfirmLeaveOut(!0),this.createView("step","views/import/step"+e.toString(),{selector:"> .import-container",entityType:this.entityType,formData:this.formData,result:t},e=>{e.render()});let i="#Import";this.options.fromAdmin&&1===this.step&&(i="#Admin/import"),1<this.step&&(i+="/index/step="+this.step),this.getRouter().navigate(i,{trigger:!1})}afterRender(){this.changeStep(this.startFromStep)}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Import","labels","Admin"))}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}}e.default=s}),define("views/import/detail",["exports","views/detail"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{getHeader(){var e=this.getDateTime().toDisplay(this.model.get("createdAt"));return this.buildHeaderHtml([$("<a>").attr("href","#"+this.model.entityType+"/list").text(this.getLanguage().translate(this.model.entityType,"scopeNamesPlural")),$("<span>").text(e)])}setup(){super.setup(),this.setupMenu(),this.listenTo(this.model,"change",()=>{this.setupMenu(),this.isRendered()&&this.getView("header").reRender()}),this.listenTo(this.model,"sync",e=>{this.controlButtons(e)})}setupMenu(){this.addMenuItem("buttons",{label:"Remove Import Log",action:"removeImportLog",name:"removeImportLog",style:"default",acl:"delete",title:this.translate("removeImportLog","messages","Import")},!0),this.addMenuItem("buttons",{label:"Revert Import",name:"revert",action:"revert",style:"danger",acl:"edit",title:this.translate("revert","messages","Import"),hidden:!this.model.get("importedCount")},!0),this.addMenuItem("buttons",{label:"Remove Duplicates",name:"removeDuplicates",action:"removeDuplicates",style:"default",acl:"edit",title:this.translate("removeDuplicates","messages","Import"),hidden:!this.model.get("duplicateCount")},!0),this.addMenuItem("dropdown",{label:"New import with same params",name:"createWithSameParams",action:"createWithSameParams"})}controlButtons(e){e&&!e.hasChanged("importedCount")||(this.model.get("importedCount")?this.showHeaderActionItem("revert"):this.hideHeaderActionItem("revert")),e&&!e.hasChanged("duplicateCount")||(this.model.get("duplicateCount")?this.showHeaderActionItem("removeDuplicates"):this.hideHeaderActionItem("removeDuplicates"))}actionRemoveImportLog(){this.confirm(this.translate("confirmRemoveImportLog","messages","Import"),()=>{this.disableMenuItem("removeImportLog"),Espo.Ui.notify(this.translate("pleaseWait","messages")),this.model.destroy({wait:!0}).then(()=>{Espo.Ui.notify(!1);var e=this.model.collection;e&&0<e.total&&e.total--,this.getRouter().navigate("#Import/list",{trigger:!0}),this.removeMenuItem("removeImportLog",!0)})})}actionRevert(){this.confirm(this.translate("confirmRevert","messages","Import"),()=>{this.disableMenuItem("revert"),Espo.Ui.notify(this.translate("pleaseWait","messages")),Espo.Ajax.postRequest(`Import/${this.model.id}/revert`).then(()=>{this.getRouter().navigate("#Import/list",{trigger:!0})})})}actionRemoveDuplicates(){this.confirm(this.translate("confirmRemoveDuplicates","messages","Import"),()=>{this.disableMenuItem("removeDuplicates"),Espo.Ui.notify(this.translate("pleaseWait","messages")),Espo.Ajax.postRequest(`Import/${this.model.id}/removeDuplicates`).then(()=>{this.removeMenuItem("removeDuplicates",!0),this.model.fetch(),this.model.trigger("update-all"),Espo.Ui.success(this.translate("duplicatesRemoved","messages","Import"))})})}actionCreateWithSameParams(){let e=this.model.get("params")||{};e.entityType=this.model.get("entityType"),e.attributeList=this.model.get("attributeList")||[],e=Espo.Utils.cloneDeep(e),this.getRouter().navigate("#Import",{trigger:!1}),this.getRouter().dispatch("Import","index",{formData:e})}}e.default=s}),define("views/import/record/list",["exports","views/record/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{quickDetailDisabled=!0;quickEditDisabled=!0;checkAllResultDisabled=!0;massActionList=["remove"];rowActionsView="views/record/row-actions/remove-only"}e.default=s}),define("views/import/record/detail",["exports","views/record/detail"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{readOnly=!0;returnUrl="#Import/list";checkInterval=5;resultPanelFetchLimit=10;duplicateAction=!1;setup(){super.setup(),this.fetchCounter=0,this.setupChecking(),this.hideActionItem("delete")}setupChecking(){this.model.has("status")?~["In Process","Pending","Standby"].indexOf(this.model.get("status"))&&(setTimeout(this.runChecking.bind(this),1e3*this.checkInterval),this.on("remove",()=>{this.stopChecking=!0})):this.listenToOnce(this.model,"sync",this.setupChecking.bind(this))}runChecking(){this.stopChecking||(this.model.fetch().then(()=>{var e=!~["In Process","Pending","Standby"].indexOf(this.model.get("status"));this.fetchCounter<this.resultPanelFetchLimit&&!e&&this.fetchResultPanels(),e?this.fetchResultPanels():setTimeout(this.runChecking.bind(this),1e3*this.checkInterval)}),this.fetchCounter++)}fetchResultPanels(){var e,t=this.getView("bottom");t&&((e=t.getView("imported"))&&e.collection&&e.collection.fetch(),(e=t.getView("duplicates"))&&e.collection&&e.collection.fetch(),e=t.getView("updated"))&&e.collection&&e.collection.fetch()}}e.default=s}),define("views/import/record/row-actions/duplicates",["exports","views/record/row-actions/default"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{getActionList(){var e=super.getActionList();return e.push({action:"unmarkAsDuplicate",label:"Set as Not Duplicate",data:{id:this.model.id,type:this.model.entityType}}),e}}e.default=s}),define("views/import/record/panels/updated",["exports","views/import/record/panels/imported"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{link="updated";rowActionsView="views/record/row-actions/relationship-view-and-edit";setup(){this.title=this.title||this.translate("Updated","labels","Import"),super.setup()}}e.default=s}),define("views/import/record/panels/duplicates",["exports","views/import/record/panels/imported"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{link="duplicates";setup(){this.title=this.title||this.translate("Duplicates","labels","Import"),super.setup()}actionUnmarkAsDuplicate(e){let t=e.id,i=e.type;this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ajax.postRequest(`Import/${this.model.id}/unmarkDuplicates`,{entityId:t,entityType:i}).then(()=>{this.collection.fetch()})})}}e.default=s}),define("views/group-email-folder/list",["exports","views/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{quickCreate=!0;setup(){super.setup(),this.options.params.fromAdmin&&this.hideHeaderActionItem("emails")}}e.default=s}),define("views/group-email-folder/record/list",["exports","views/record/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{rowActionsView="views/email-folder/record/row-actions/default";async actionMoveUp(e){e=this.collection.get(e.id);e&&0!==this.collection.indexOf(e)&&(Espo.Ui.notifyWait(),await Espo.Ajax.postRequest("GroupEmailFolder/action/moveUp",{id:e.id}),await this.collection.fetch(),Espo.Ui.notify(!1))}async actionMoveDown(e){e=this.collection.get(e.id);!e||this.collection.indexOf(e)===this.collection.length-1&&this.collection.length===this.collection.total||(Espo.Ui.notifyWait(),await Espo.Ajax.postRequest("GroupEmailFolder/action/moveDown",{id:e.id}),await this.collection.fetch(),Espo.Ui.notify(!1))}}e.default=s}),define("views/group-email-folder/record/edit-small",["exports","views/record/edit"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{afterSave(){this.getBaseController().clearScopeStoredMainView("Email"),super.afterSave()}}e.default=s}),define("views/group-email-folder/record/row-actions/default",["exports","views/record/row-actions/default"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{getActionList(){var e=super.getActionList();return this.options.acl.edit&&(e.unshift({action:"moveDown",label:"Move Down",data:{id:this.model.id}}),e.unshift({action:"moveUp",label:"Move Up",data:{id:this.model.id}})),e}}e.default=s}),define("views/external-account/oauth2",["exports","view","model"],function(e,t,i){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),i=s(i);class a extends t.default{template="external-account/oauth2";data(){return{integration:this.integration,helpText:this.helpText,isConnected:this.isConnected}}isConnected=!1;setup(){this.addActionHandler("connect",()=>this.connect()),this.addActionHandler("save",()=>this.save()),this.addActionHandler("cancel",()=>this.getRouter().navigate("#ExternalAccount",{trigger:!0})),this.integration=this.options.integration,this.id=this.options.id,this.helpText=!1,this.getLanguage().has(this.integration,"help","ExternalAccount")&&(this.helpText=this.translate(this.integration,"help","ExternalAccount")),this.fieldList=[],this.dataFieldList=[],this.model=new i.default,this.model.id=this.id,this.model.entityType=this.model.name="ExternalAccount",this.model.urlRoot="ExternalAccount",this.model.defs={fields:{enabled:{required:!0,type:"bool"}}},this.wait(!0),this.model.populateDefaults(),this.listenToOnce(this.model,"sync",()=>{this.createFieldView("bool","enabled"),Espo.Ajax.getRequest("ExternalAccount/action/getOAuth2Info?id="+this.id).then(e=>{this.clientId=e.clientId,this.redirectUri=e.redirectUri,e.isConnected&&(this.isConnected=!0),this.wait(!1)})}),this.model.fetch()}hideField(e){this.$el.find(`label[data-name="${e}"]`).addClass("hide"),this.$el.find(`div.field[data-name="${e}"]`).addClass("hide");e=this.getView(e);e&&(e.disabled=!0)}showField(e){this.$el.find(`label[data-name="${e}"]`).removeClass("hide"),this.$el.find(`div.field[data-name="${e}"]`).removeClass("hide");e=this.getView(e);e&&(e.disabled=!1)}afterRender(){this.model.get("enabled")||this.$el.find(".data-panel").addClass("hidden"),this.listenTo(this.model,"change:enabled",()=>{this.model.get("enabled")?this.$el.find(".data-panel").removeClass("hidden"):this.$el.find(".data-panel").addClass("hidden")})}createFieldView(e,t,i,s){this.createView(t,this.getFieldManager().getViewName(e),{model:this.model,selector:'.field[data-name="'+t+'"]',defs:{name:t,params:s},mode:i?"detail":"edit",readOnly:i}),this.fieldList.push(t)}save(){this.fieldList.forEach(e=>{e=this.getView(e);e.readOnly||e.fetchToModel()});let t=!1;this.fieldList.forEach(e=>{e=this.getView(e);t=e.validate()||t}),t?Espo.Ui.error(this.translate("Not valid")):(this.listenToOnce(this.model,"sync",()=>{Espo.Ui.success(this.translate("Saved")),this.model.get("enabled")||this.setNotConnected()}),Espo.Ui.notify(this.translate("saving","messages")),this.model.save())}popup(e,t){e.windowName=e.windowName||"ConnectWithOAuth",e.windowOptions=e.windowOptions||"location=0,status=0,width=800,height=400",e.callback=e.callback||function(){window.location.reload()};let i=this;var s,a=e.path,l=[],d=e.params||{};for(s in d)d[s]&&l.push(s+"="+encodeURI(d[s]));a+="?"+l.join("&");let o=window.open(a,e.windowName,e.windowOptions),r;r=window.setInterval(()=>{var e;o.closed?window.clearInterval(r):(e=(e=>{let i=null,s=null;return(e=e.substr(e.indexOf("?")+1,e.length)).split("&").forEach(e=>{var e=e.split("="),t=decodeURI(e[0]),e=decodeURI(e[1]||"");"code"===t&&(i=e),"error"===t&&(s=e)}),i?{code:i}:s?{error:s}:void 0})(o.location.href.toString()))&&(t.call(i,e),o.close(),window.clearInterval(r))},500)}connect(){this.popup({path:this.getMetadata().get(`integrations.${this.integration}.params.endpoint`),params:{client_id:this.clientId,redirect_uri:this.redirectUri,scope:this.getMetadata().get(`integrations.${this.integration}.params.scope`),response_type:"code",access_type:"offline",approval_prompt:"force"}},e=>{e.error?Espo.Ui.notify(!1):e.code?(this.$el.find('[data-action="connect"]').addClass("disabled"),Espo.Ajax.postRequest("ExternalAccount/action/authorizationCode",{id:this.id,code:e.code}).then(e=>{Espo.Ui.notify(!1),!0===e?this.setConnected():this.setNotConnected(),this.$el.find('[data-action="connect"]').removeClass("disabled")}).catch(()=>{this.$el.find('[data-action="connect"]').removeClass("disabled")})):Espo.Ui.error(this.translate("Error occurred"))})}setConnected(){this.isConnected=!0,this.$el.find('[data-action="connect"]').addClass("hidden"),this.$el.find(".connected-label").removeClass("hidden")}setNotConnected(){this.isConnected=!1,this.$el.find('[data-action="connect"]').removeClass("hidden"),this.$el.find(".connected-label").addClass("hidden")}}e.default=a}),define("views/external-account/index",["exports","view"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{template="external-account/index";data(){return{externalAccountList:this.externalAccountList,id:this.id,externalAccountListCount:this.externalAccountList.length}}setup(){this.addHandler("click","#external-account-menu a.external-account-link",(e,t)=>{t=t.dataset.id+"__"+this.userId;this.openExternalAccount(t)}),this.externalAccountList=this.collection.models.map(e=>e.getClonedAttributes()),this.userId=this.getUser().id,this.id=this.options.id||null,this.id&&(this.userId=this.id.split("__")[1]),this.on("after:render",()=>{this.renderHeader(),this.id?this.openExternalAccount(this.id):this.renderDefaultPage()})}openExternalAccount(t){this.id=t;var e=this.integration=t.split("__")[0],i=(this.userId=t.split("__")[1],this.getRouter().navigate("#ExternalAccount/edit/"+t,{trigger:!1}),this.getMetadata().get(["integrations",e,"authMethod"])),i=this.getMetadata().get(["integrations",e,"userView"])||"views/external-account/"+Espo.Utils.camelCaseToHyphen(i);Espo.Ui.notifyWait(),this.createView("content",i,{fullSelector:"#external-account-content",id:t,integration:e},e=>{this.renderHeader(),e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0),this.controlCurrentLink(t)})}controlCurrentLink(){var e=this.integration,e=(this.element.querySelectorAll(".external-account-link").forEach(e=>{e.classList.remove("disabled","text-muted")}),this.element.querySelector(`.external-account-link[data-id="${e}"]`));e&&e.classList.add("disabled","text-muted")}renderDefaultPage(){$("#external-account-header").html("").hide(),$("#external-account-content").html("")}renderHeader(){var e=$("#external-account-header");this.id?e.show().text(this.integration):e.html("")}updatePageTitle(){this.setPageTitle(this.translate("ExternalAccount","scopeNamesPlural"))}}e.default=s}),define("views/email-account/list",["exports","views/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{keepCurrentRootUrl=!0;setup(){this.options.params=this.options.params||{};var e=this.options.params||{};this.userId=e.userId,super.setup(),this.userId&&(this.collection.where=[{type:"equals",field:"assignedUserId",value:e.userId}])}setupSearchPanel(){this.userId||!this.getUser().isAdmin()?(this.searchPanel=!1,this.searchManager.reset()):super.setupSearchPanel()}getCreateAttributes(){var e={};return this.options.params.userId&&(e.assignedUserId=this.options.params.userId,e.assignedUserName=this.options.params.userName||this.options.params.userId),e}}e.default=s}),define("views/email-account/record/list",["exports","views/record/list"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{quickDetailDisabled=!0;quickEditDisabled=!0;checkAllResultDisabled=!0;massActionList=["remove","massUpdate"]}e.default=s}),define("views/email-account/record/edit",["exports","views/record/edit","views/email-account/record/detail"],function(e,t,i){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),i=s(i);class a extends t.default{setup(){super.setup(),i.default.prototype.setupFieldsBehaviour.call(this),i.default.prototype.initSslFieldListening.call(this),i.default.prototype.initSmtpFieldsControl.call(this),this.getUser().isAdmin()?this.setFieldNotReadOnly("assignedUser"):this.setFieldReadOnly("assignedUser")}modifyDetailLayout(e){i.default.prototype.modifyDetailLayout.call(this,e)}setupFieldsBehaviour(){i.default.prototype.setupFieldsBehaviour.call(this)}controlStatusField(){i.default.prototype.controlStatusField.call(this)}controlSmtpFields(){i.default.prototype.controlSmtpFields.call(this)}controlSmtpAuthField(){i.default.prototype.controlSmtpAuthField.call(this)}wasFetched(){i.default.prototype.wasFetched.call(this)}}e.default=a}),define("views/email-account/modals/select-folder",["exports","views/modal"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{cssName="select-folder-modal";template="email-account/modals/select-folder";data(){return{folders:this.options.folders}}setup(){this.headerText=this.translate("Select"),this.addActionHandler("select",(e,t)=>{t=t.dataset.value;this.trigger("select",t)})}}e.default=s}),define("views/email-account/fields/email-folder",["exports","views/fields/link"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{createDisabled=!0;autocompleteDisabled=!0;getSelectFilters(){if(this.getUser().isAdmin()&&this.model.get("assignedUserId"))return{assignedUser:{type:"equals",attribute:"assignedUserId",value:this.model.get("assignedUserId"),data:{type:"is",nameValue:this.model.get("assignedUserName")}}}}setup(){super.setup(),this.listenTo(this.model,"change:assignedUserId",(e,t,i)=>{i.ui&&this.model.set({emailFolderId:null,emailFolderName:null})})}}e.default=s}),define("views/email-account/fields/email-address",["exports","views/fields/email-address"],function(e,t){var i;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(i=t)&&i.__esModule?i:{default:i};class s extends t.default{setup(){super.setup(),this.on("change",()=>{var e=this.model.get("emailAddress");this.model.set("name",e)});var e=this.model.get("assignedUserId");this.getUser().isAdmin()&&e!==this.getUser().id&&Espo.Ajax.getRequest("User/"+e).then(t=>{let i=[];t.emailAddress&&(i.push(t.emailAddress),this.params.options=i,t.emailAddressData&&t.emailAddressData.forEach(e=>{e.emailAddress!==t.emailAddress&&i.push(e.emailAddress)}),this.reRender())})}setupOptions(){this.model.get("assignedUserId")===this.getUser().id&&(this.params.options=this.getUser().get("userEmailAddressList"))}}e.default=s});
//# sourceMappingURL=espo-extra.js.map