/*! espocrm 2025-05-27 */
define("views/admin/layouts/base",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{scope;type;events={'click button[data-action="save"]':function(){this.actionSave()},'click button[data-action="cancel"]':function(){this.cancel()},'click button[data-action="resetToDefault"]':function(){this.confirm(this.translate("confirmation","messages"),()=>{this.resetToDefault()})},'click button[data-action="remove"]':function(){this.actionDelete()}};buttonList=[{name:"save",label:"Save",style:"primary"},{name:"cancel",label:"Cancel"}];dataAttributes=null;dataAttributesDefs=null;dataAttributesDynamicLogicDefs=null;setup(){this.buttonList=_.clone(this.buttonList),this.events=_.clone(this.events),this.scope=this.options.scope,this.type=this.options.type,this.realType=this.options.realType,this.setId=this.options.setId,this.em=this.options.em;var e=this.getMetadata().get(["clientDefs",this.scope,"additionalLayouts",this.type])??{};this.typeDefs=e,this.dataAttributeList=Espo.Utils.clone(e.dataAttributeList||this.dataAttributeList),this.isCustom=!!e.isCustom,this.isCustom&&this.em&&this.buttonList.push({name:"remove",label:"Remove"}),this.isCustom||this.buttonList.push({name:"resetToDefault",label:"Reset to Default"})}actionSave(){this.disableButtons(),Espo.Ui.notify(this.translate("saving","messages")),this.save(this.enableButtons.bind(this))}disableButtons(){this.$el.find(".button-container button").attr("disabled","disabled")}enableButtons(){this.$el.find(".button-container button").removeAttr("disabled")}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}setIsChanged(){this.isChanged=!0,this.setConfirmLeaveOut(!0)}setIsNotChanged(){this.isChanged=!1,this.setConfirmLeaveOut(!1)}save(e){var t=this.fetch();if(!this.validate(t))return this.enableButtons(),!1;this.getHelper().layoutManager.set(this.scope,this.type,t,()=>{Espo.Ui.success(this.translate("Saved")),this.setIsNotChanged(),"function"==typeof e&&e(),this.getHelper().broadcastChannel.postMessage("update:layout")},this.setId).catch(()=>this.enableButtons())}resetToDefault(){this.getHelper().layoutManager.resetToDefault(this.scope,this.type,()=>{this.loadLayout(()=>{this.setIsNotChanged(),this.prepareLayout().then(()=>this.reRender())})},this.options.setId)}prepareLayout(){return Promise.resolve()}reset(){this.render()}fetch(){}unescape(e){if(null===e)return"";let t={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'"};var a=new RegExp("("+_.keys(t).join("|")+")","g");return(""+e).replace(a,e=>t[e])}getEditAttributesModalViewOptions(e){return{name:e.name,scope:this.scope,attributeList:this.dataAttributeList,attributeDefs:this.dataAttributesDefs,dynamicLogicDefs:this.dataAttributesDynamicLogicDefs,attributes:e,languageCategory:this.languageCategory,headerText:" "}}openEditDialog(e){let i=e.name;e=this.getEditAttributesModalViewOptions(e);this.createView("editModal","views/admin/layouts/modals/edit-attributes",e,s=>{s.render(),this.listenToOnce(s,"after:save",e=>{this.trigger("update-item",i,e);var t,a=$("#layout ul > li[data-name='"+i+"']");for(t in e)a.attr("data-"+t,e[t]),a.data(t,e[t]),a.find("."+t+"-value").text(e[t]);s.close(),this.setIsChanged()})})}cancel(){this.loadLayout(()=>{this.setIsNotChanged(),this.em?this.trigger("cancel"):this.prepareLayout().then(()=>this.reRender())})}loadLayout(e){}validate(e){return!0}actionDelete(){this.confirm(this.translate("confirmation","messages")).then(()=>{this.disableButtons(),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("Layout/action/delete",{scope:this.scope,name:this.type}).then(()=>{Espo.Ui.success(this.translate("Removed"),{suppress:!0}),this.trigger("after-delete")}).catch(()=>{this.enableButtons()})})}}e.default=s}),define("views/admin/layouts/rows",["exports","views/admin/layouts/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/layouts/rows";dataAttributeList=null;dataAttributesDefs={};editable=!1;data(){return{scope:this.scope,type:this.type,buttonList:this.buttonList,enabledFields:this.enabledFields,disabledFields:this.disabledFields,layout:this.rowLayout,dataAttributeList:this.dataAttributeList,dataAttributesDefs:this.dataAttributesDefs,editable:this.editable}}setup(){this.itemsData={},super.setup(),this.events['click a[data-action="editItem"]']=e=>{e=$(e.target).closest("li").data("name");this.editRow(e)},this.on("update-item",(e,t)=>{this.itemsData[e]=Espo.Utils.cloneDeep(t)}),Espo.loader.require("res!client/css/misc/layout-manager-rows.css",e=>{this.$style=$("<style>").html(e).appendTo($("body"))})}onRemove(){this.$style&&this.$style.remove()}editRow(e){var t=Espo.Utils.cloneDeep(this.itemsData[e]||{});t.name=e,this.openEditDialog(t)}afterRender(){$("#layout ul.enabled, #layout ul.disabled").sortable({connectWith:"#layout ul.connected",update:e=>{$(e.target).hasClass("disabled")||(this.onDrop(e),this.setIsChanged())}}),this.$el.find(".enabled-well").focus()}onDrop(e){}fetch(){let i=[];return $("#layout ul.enabled > li").each((e,t)=>{let a={};t=$(t).data("name");let s=this.itemsData[t]||{};s.name=t,this.dataAttributeList.forEach(e=>{var t;(this.dataAttributesDefs[e]||{}).notStorable||(t=s[e]||null)&&(a[e]=t)}),i.push(a)}),i}validate(e){return 0!==e.length||(Espo.Ui.error(this.translate("cantBeEmpty","messages","LayoutManager")),!1)}}e.default=s}),define("views/admin/layouts/side-panels-detail",["exports","views/admin/layouts/rows"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name","dynamicLogicVisible","style","dynamicLogicStyled","sticked"];dataAttributesDefs={dynamicLogicVisible:{type:"base",view:"views/admin/field-manager/fields/dynamic-logic-conditions",tooltip:"dynamicLogicVisible"},style:{type:"enum",options:["default","success","danger","warning","info"],style:{info:"info",success:"success",danger:"danger",warning:"warning"},default:"default",translation:"LayoutManager.options.style",tooltip:"panelStyle"},dynamicLogicStyled:{type:"base",view:"views/admin/field-manager/fields/dynamic-logic-conditions",tooltip:"dynamicLogicStyled"},sticked:{type:"bool",tooltip:"sticked"},name:{readOnly:!0}};dataAttributesDynamicLogicDefs={fields:{dynamicLogicStyled:{visible:{conditionGroup:[{type:"and",value:[{attribute:"style",type:"notEquals",value:"default"},{attribute:"style",type:"isNotEmpty"}]}]}}}};editable=!0;ignoreList=[];viewType="detail";setup(){super.setup(),this.dataAttributesDefs=Espo.Utils.cloneDeep(this.dataAttributesDefs),this.dataAttributesDefs.dynamicLogicVisible.scope=this.scope,this.dataAttributesDefs.dynamicLogicStyled.scope=this.scope,this.wait(!0),this.loadLayout(()=>{this.wait(!1)})}loadLayout(t){this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{this.readDataFromLayout(e),t&&t()})}getDataFromLayout(o,e,t){let a=[],n={},d={},r=[],f=[],h={};if(o=(o=Espo.Utils.cloneDeep(o))||{},t){t=t();if(t.panelListAll&&t.panelListAll.forEach(e=>a.push(e)),t.params)for(var[s,i]of Object.entries(t.params))d[s]=i;if(t.labels)for(var[l,c]of Object.entries(t.labels))n[l]=c}return(this.getMetadata().get(["clientDefs",this.scope,e,this.viewType])||[]).forEach(e=>{(e=e.reference?{...this.getMetadata().get("app.clientRecord.panels."+e.reference),...e}:e).name&&(a.push(e.name),e.labelText&&(n[e.name]=e.labelText),e.label&&(n[e.name]=e.label),d[e.name]=e)}),a.push("_delimiter_"),o._delimiter_||(o._delimiter_={disabled:!0,index:1e4}),a.forEach((t,e)=>{let a=!1;var s=o[t]||{};s.disabled&&(a=!0),o[t]||(d[t]||{}).disabled&&(a=!0);let i;if(i=n[t]?this.getLanguage().translate(n[t],"labels",this.scope):this.getLanguage().translate(t,"panels",this.scope),a){let e={name:t,labelText:i};"_"===e.name[0]&&"_delimiter_"===e.name&&(e.notEditable=!0,e.labelText=". . ."),void r.push(e)}else{let a={name:t,labelText:i};for(var l in"_"===a.name[0]&&"_delimiter_"===a.name&&(a.notEditable=!0,a.labelText=". . ."),a.name in d&&this.dataAttributeList.forEach(e=>{var t;"name"!==e&&e in(t=d[a.name]||{})&&(a[e]=t[e])}),s)a[l]=s[l];a.index="index"in s?s.index:e,f.push(a),h[a.name]=Espo.Utils.cloneDeep(a)}}),f.sort((e,t)=>e.index-t.index),r.sort((e,t)=>"_delimiter_"===e.name?1:(e=n[e.name]||e.name,t=n[t.name]||t.name,e.localeCompare(t))),{panelListAll:a,labels:n,params:d,disabledFields:r,rowLayout:f,itemsData:h}}readDataFromLayout(e){e=this.getDataFromLayout(e,"sidePanels",()=>{var e=[],t={};return!1===this.getMetadata().get(`clientDefs.${this.scope}.defaultSidePanel.`+this.viewType)||this.getMetadata().get(`clientDefs.${this.scope}.defaultSidePanelDisabled`)||(e.push("default"),t.default="Default"),{panelListAll:e,labels:t}});this.disabledFields=e.disabledFields,this.rowLayout=e.rowLayout,this.itemsData=e.itemsData}fetch(){let i={};return $("#layout ul.disabled > li").each((e,t)=>{t=$(t).attr("data-name");i[t]={disabled:!0}}),$("#layout ul.enabled > li").each((e,t)=>{t=$(t);let a={};t=t.attr("data-name");let s=this.itemsData[t]||{};s.name=t,this.dataAttributeList.forEach(e=>{"name"!==e&&e in s&&(a[e]=s[e])}),a.index=e,i[t]=a}),i}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-base",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-base";level;scope;number;operator;operatorString;itemData;additionalData;field;data(){return{valueViewKey:this.getValueViewKey(),scope:this.scope,operator:this.operator,operatorString:this.operatorString,field:this.field,leftString:this.getLeftPartString()}}setup(){this.itemData=this.options.itemData,this.level=this.options.level||0,this.number=this.options.number||0,this.scope=this.options.scope,this.operator=this.options.operator||this.operator,this.operatorString=this.options.operatorString||this.operatorString,this.additionalData=this.itemData.data||{},this.field=(this.itemData.data||{}).field||this.itemData.attribute,this.wait(!0),this.isCurrentUser=this.itemData.attribute&&this.itemData.attribute.startsWith("$user."),this.isCurrentUser&&(this.scope="User"),this.getModelFactory().create(this.scope,e=>{this.model=e,this.populateValues(),this.createValueFieldView(),this.wait(!1)})}getLeftPartString(){if("$user.id"===this.itemData.attribute)return"$"+this.translate("User","scopeNames");let e=this.translate(this.field,"fields",this.scope);return e=this.isCurrentUser?"$"+this.translate("User","scopeNames")+"."+e:e}populateValues(){this.itemData.attribute&&this.model.set(this.itemData.attribute,this.itemData.value),this.model.set(this.additionalData.values||{})}getValueViewKey(){return`view-${this.level.toString()}-${this.number.toString()}-0`}getFieldValueView(){var e;return"$user.id"===this.itemData.attribute?"views/admin/dynamic-logic/fields/user-id":(e=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"type"])||"base",this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"view"])||this.getFieldManager().getViewName(e))}createValueFieldView(){var e=this.getValueViewKey(),t=this.getFieldValueView();this.createView("value",t,{model:this.model,name:this.field,selector:`[data-view-key="${e}"]`,readOnly:!0})}}e.default=s}),define("views/admin/link-manager/modals/edit-params",["exports","views/modal","model","views/record/edit-for-modal","views/fields/bool"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{templateContent=`
        <div class="record no-side-margin">{{{record}}}</div>
    `;type;constructor(e){super(),this.props=e}setup(){this.headerText=this.translate("Parameters","labels","EntityManager")+" · "+this.translate(this.props.entityType,"scopeNames")+" · "+this.translate(this.props.link,"links",this.props.entityType);var e=this.getMetadata().get(`entityDefs.${this.props.entityType}.links.`+this.props.link)||{};this.type=e.type,this.buttonList=[{name:"save",style:"danger",label:"Save",onClick:()=>this.save()},{name:"cancel",label:"Cancel",onClick:()=>this.close()}],e.isCustom||this.addDropdownItem({name:"resetToDefault",text:this.translate("Reset to Default","labels","Admin"),onClick:()=>this.resetToDefault()}),this.formModel=new a.default(this.getParamsFromMetadata()),this.recordView=new s.default({model:this.formModel,detailLayout:[{rows:[[{view:new i.default({name:"readOnly",labelText:this.translate("readOnly","fields","Admin"),params:{tooltip:"EntityManager.linkParamReadOnly"}})},!1]]}]}),this.hasReadOnly()||(this.recordView.hideField("readOnly"),this.recordView.setFieldReadOnly("readOnly")),this.assignView("record",this.recordView,".record")}hasReadOnly(){return["hasMany","hasChildren"].includes(this.type)}getParamsFromMetadata(){return{readOnly:(this.getMetadata().get(`entityDefs.${this.props.entityType}.links.`+this.props.link)||{}).readOnly||!1}}disableAllActionItems(){this.disableButton("save"),this.hideActionItem("resetToDefault")}enableAllActionItems(){this.enableButton("save"),this.showActionItem("resetToDefault")}async save(){if(!this.recordView.validate()){this.disableAllActionItems(),Espo.Ui.notifyWait();var e={};this.hasReadOnly()&&(e.readOnly=this.formModel.attributes.readOnly);try{await Espo.Ajax.postRequest("EntityManager/action/updateLinkParams",{entityType:this.props.entityType,link:this.props.link,params:e})}catch(e){return void this.enableAllActionItems()}await Promise.all([this.getMetadata().loadSkipCache()]),this.broadcastUpdate(),this.close(),Espo.Ui.success(this.translate("Saved"))}}async resetToDefault(){this.disableAllActionItems(),Espo.Ui.notifyWait();try{await Espo.Ajax.postRequest("EntityManager/action/resetLinkParamsToDefault",{entityType:this.props.entityType,link:this.props.link})}catch(e){return void this.enableAllActionItems()}await Promise.all([this.getMetadata().loadSkipCache()]),this.broadcastUpdate(),this.formModel.setMultiple(this.getParamsFromMetadata()),this.enableAllActionItems(),Espo.Ui.success(this.translate("Saved"))}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata")}}e.default=o}),define("views/admin/layouts/grid",["exports","views/admin/layouts/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/layouts/grid";dataAttributeList=null;panels=null;columnCount=2;panelDataAttributeList=["panelName","style"];panelDataAttributesDefs={};panelDynamicLogicDefs=null;data(){return{scope:this.scope,type:this.type,buttonList:this.buttonList,enabledFields:this.enabledFields,disabledFields:this.disabledFields,panels:this.panels,columnCount:this.columnCount,panelDataList:this.getPanelDataList()}}additionalEvents={'click #layout a[data-action="addPanel"]':function(){this.addPanel(),this.setIsChanged(),this.makeDraggable()},'click #layout a[data-action="removePanel"]':function(e){$(e.target).closest("ul.panels > li").find("ul.cells > li").each((e,t)=>{$(t).attr("data-name")&&$(t).appendTo($("#layout ul.disabled"))}),$(e.target).closest("ul.panels > li").remove();let a=$(e.currentTarget).data("number"),s=(this.clearView("panels-"+a),-1);this.panels.forEach((e,t)=>{e.number===a&&(s=t)}),~s&&this.panels.splice(s,1),this.normalizeDisabledItemList(),this.setIsChanged()},'click #layout a[data-action="addRow"]':function(e){var t=this.unescape($("#layout-row-tpl").html()),t=_.template(t);$(e.target).closest("ul.panels > li").find("ul.rows").append(t),this.setIsChanged(),this.makeDraggable()},'click #layout a[data-action="removeRow"]':function(e){$(e.target).closest("ul.rows > li").find("ul.cells > li").each((e,t)=>{$(t).attr("data-name")&&$(t).appendTo($("#layout ul.disabled"))}),$(e.target).closest("ul.rows > li").remove(),this.normalizeDisabledItemList(),this.setIsChanged()},'click #layout a[data-action="removeField"]':function(e){var e=$(e.target).closest("li"),t=e.index(),a=e.parent(),s=(e.appendTo($("ul.disabled")),$($("#empty-cell-tpl").html()));if(1===parseInt(a.attr("data-cell-count")))for(let e=0;e<this.columnCount;e++)a.append(s.clone());else 0===t?a.prepend(s):s.insertAfter(a.children(":nth-child("+t+")"));e=a.children().length;a.attr("data-cell-count",e.toString()),a.closest("li").attr("data-cell-count",e.toString()),this.setIsChanged(),this.makeDraggable()},'click #layout a[data-action="minusCell"]':function(e){var t;this.columnCount<2||(t=(e=$(e.currentTarget).closest("li")).parent(),e.remove(),e=t.children().length||2,this.setIsChanged(),this.makeDraggable(),t.attr("data-cell-count",e.toString()),t.closest("li").attr("data-cell-count",e.toString()))},'click #layout a[data-action="plusCell"]':function(e){var e=$(e.currentTarget).closest("li").find("ul"),t=$($("#empty-cell-tpl").html()),t=(e.append(t),e.children().length);e.attr("data-cell-count",t.toString()),e.closest("li").attr("data-cell-count",t.toString()),this.setIsChanged(),this.makeDraggable()},'click #layout a[data-action="edit-panel-label"]':function(e){e=$(e.target).closest("header");let a=e.children("label");var t=a.text();let s=e.closest("li"),i=s.data("number").toString(),l={panelName:t};this.panelDataAttributeList.forEach(e=>{"panelName"!==e&&(l[e]=this.panelsData[i][e])});e=this.panelDataAttributeList,t=this.panelDataAttributesDefs;this.createView("dialog","views/admin/layouts/modals/panel-attributes",{attributeList:e,attributeDefs:t,attributes:l,dynamicLogicDefs:this.panelDynamicLogicDefs},e=>{e.render(),this.listenTo(e,"after:save",t=>{a.text(t.panelName),a.attr("data-is-custom","true"),this.panelDataAttributeList.forEach(e=>{"panelName"!==e&&(this.panelsData[i][e]=t[e])}),s.attr("data-tab-break",!!t.tabBreak&&"true"),e.close(),this.$el.find(".well").focus(),this.setIsChanged()})})}};normalizeDisabledItemList(){}setup(){super.setup(),this.events={...this.additionalEvents,...this.events},this.panelsData={},Espo.loader.require("res!client/css/misc/layout-manager-grid.css",e=>{this.$style=$("<style>").html(e).appendTo($("body"))})}onRemove(){this.$style&&this.$style.remove()}addPanel(){this.lastPanelNumber++;var e,t=this.lastPanelNumber,a={customLabel:null,rows:[[]],number:t},s=(this.panels.push(a),{});for(e in this.panelDataAttributesDefs){var i=this.panelDataAttributesDefs[e];"default"in i&&(s[e]=i.default)}this.panelsData[t.toString()]=s;var l=$('<li class="panel-layout"></li>');l.attr("data-number",t),this.$el.find("ul.panels").append(l),this.createPanelView(a,!0,e=>{e.render()})}getPanelDataList(){let a=[];return this.panels.forEach(e=>{var t={};t.viewKey="panel-"+e.number,t.number=e.number,t.tabBreak=!!e.tabBreak,a.push(t)}),a}prepareLayout(){return new Promise(e=>{let t=0;this.setupPanels(()=>{++t===this.panels.length&&e()})})}setupPanels(a){this.lastPanelNumber=-1,this.panels=Espo.Utils.cloneDeep(this.panels),this.panels.forEach((e,t)=>{e.number=t,this.lastPanelNumber++,this.createPanelView(e,!1,a),this.panelsData[t.toString()]=e})}createPanelView(e,s,t){e.label=e.label||"",e.isCustomLabel=!1,e.customLabel?(e.labelTranslated=e.customLabel,e.isCustomLabel=!0):e.labelTranslated=this.translate(e.label,"labels",this.scope),e.style=e.style||null,e.rows.forEach(t=>{var e,a=this.columnCount-t.length;if(s)for(let e=0;e<a;e++)t.push(!1);for(e in t)!1!==t[e]&&(t[e].label=this.getLanguage().translate(t[e].name,"fields",this.scope),"customLabel"in t[e])&&(t[e].hasCustomLabel=!0)}),this.createView("panel-"+e.number,"view",{selector:'li.panel-layout[data-number="'+e.number+'"]',template:"admin/layouts/grid-panel",data:()=>{let t=Espo.Utils.clone(e);return t.dataAttributeList=[],this.panelDataAttributeList.forEach(e=>{"panelName"!==e&&t.dataAttributeList.push(e)}),t}},t)}makeDraggable(){let i=this;var e=$("#layout ul.panels"),t=$("#layout ul.rows"),e=(e.sortable({distance:4,update:()=>{this.setIsChanged()}}),e.disableSelection(),t.sortable({distance:4,connectWith:".rows",update:()=>{this.setIsChanged()}}),t.disableSelection(),$("#layout ul.cells > li"));e.draggable({revert:"invalid",revertDuration:200,zIndex:10}).css("cursor","pointer"),e.droppable().droppable("destroy"),$("#layout ul.cells:not(.disabled) > li").droppable({accept:".cell",zIndex:10,hoverClass:"ui-state-hover",drop:function(e,t){var a=t.draggable.index(),s=t.draggable.parent();s.get(0)===$(this).parent().get(0)?$(this).index()<t.draggable.index()?$(this).before(t.draggable):$(this).after(t.draggable):(t.draggable.insertAfter($(this)),0===a?$(this).prependTo(s):$(this).insertAfter(s.children(":nth-child("+a+")"))),t.draggable.css({top:0,left:0}),$(this).parent().hasClass("disabled")&&!$(this).data("name")&&$(this).remove(),i.makeDraggable(),i.setIsChanged()}})}afterRender(){this.makeDraggable(),this.$el.find(".enabled-well").get(0).focus({preventScroll:!0})}fetch(){let o=[];return $("#layout ul.panels > li").each((e,t)=>{var a=$(t).find("header label");let s=$(t).data("number").toString(),l={rows:[]};this.panelDataAttributeList.forEach(e=>{"panelName"!==e&&(l[e]=this.panelsData[s][e])}),l.style=l.style||"default";var i=$(t).find("header").data("name");i&&(l.name=i),a.attr("data-is-custom")?l.customLabel=a.text():l.label=a.data("label"),$(t).find("ul.rows > li").each((e,t)=>{let i=[];$(t).find("ul.cells > li").each((e,a)=>{let s=!1;$(a).hasClass("empty")||(s={},this.dataAttributeList.forEach(e=>{var t;(this.dataAttributesDefs[e]||{}).notStorable||("customLabel"===e?$(a).get(0).hasAttribute("data-custom-label")&&(s[e]=$(a).attr("data-custom-label")):(t=$(a).data(Espo.Utils.toDom(e))||null)&&(s[e]=t))})),i.push(s)}),l.rows.push(i)}),o.push(l)}),o}validate(e){let t=0;return e.forEach(e=>{e.rows.forEach(e=>{e.forEach(e=>{!1!==e&&null!==e&&t++})})}),0!==t||(Espo.Ui.error(this.translate("cantBeEmpty","messages","LayoutManager")),!1)}}e.default=s}),define("views/admin/layouts/default-page",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{templateContent=`
        <div class="margin-bottom">{{translate 'selectLayout' category='messages' scope='Admin'}}</div>
        <div class="button-container">
            <button data-action="createLayout" class="btn btn-link">{{translate 'Create'}}</button>
        </div>
    `}e.default=s}),define("views/admin/layouts/bottom-panels-detail",["exports","views/admin/layouts/side-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{hasStream=!0;hasRelationships=!0;TAB_BREAK_KEY="_tabBreak_{n}";setup(){super.setup(),this.on("update-item",(e,t)=>{this.isTabName(e)&&$("#layout ul > li[data-name='"+e+"']").find(".left > span").text(this.composeTabBreakLabel(t))})}composeTabBreakLabel(e){let t=". . . "+this.translate("tabBreak","fields","LayoutManager");return e.tabLabel&&(t+=" : "+e.tabLabel),t}readDataFromLayout(o){var e=this.getDataFromLayout(o,"bottomPanels",()=>{let s=[],i={},l={};if(this.hasStream&&(this.getMetadata().get(`scopes.${this.scope}.stream`)||"User"===this.scope)&&(s.push("stream"),i.stream=this.translate("Stream"),l.stream={name:"stream",sticked:!1,index:2}),this.links={},this.hasRelationships){let t=this.getMetadata().get(`entityDefs.${this.scope}.links`)||{};Object.keys(t).forEach(e=>{if(!(t[e].disabled||t[e].utility||t[e].layoutRelationshipsDisabled)&&["hasMany","hasChildren"].includes(t[e].type)){s.push(e),i[e]=this.translate(e,"links",this.scope);let a={name:e,index:5};this.dataAttributeList.forEach(e=>{var t;e in a||null!==(t=this.getMetadata().get(["clientDefs",this.scope,"relationshipPanels",a.name,e]))&&(a[e]=t)}),this.links[e]=!0,(l[a.name]=a).name in o||(a.disabled=!0)}})}for(var e in s.push(this.TAB_BREAK_KEY),i[this.TAB_BREAK_KEY]=". . . "+this.translate("tabBreak","fields","LayoutManager"),l[this.TAB_BREAK_KEY]={disabled:!0},o){var t=o[e];t.tabBreak&&(s.push(e),i[e]=this.composeTabBreakLabel(t),l[e]={name:t.name,index:t.index,tabBreak:!0,tabLabel:t.tabLabel||null})}return{panelListAll:s,labels:i,params:l}});this.disabledFields=e.disabledFields,this.rowLayout=e.rowLayout,this.itemsData=e.itemsData}onDrop(){let s=-1,i=null;this.$el.find("ul.enabled").children().each((e,t)=>{var t=$(t).attr("data-name");this.isTabName(t)&&t!==this.TAB_BREAK_KEY&&(t=parseInt(t.split("_")[2]))>s&&(s=t)}),s++,this.$el.find("ul.enabled").children().each((e,t)=>{var t=$(t),a=t.attr("data-name");this.isTabName(a)&&a===this.TAB_BREAK_KEY&&(i=t.clone(),a=this.TAB_BREAK_KEY.slice(0,-3)+s,t.attr("data-name",a),delete this.itemsData[a])}),i||this.$el.find("ul.disabled").children().each((e,t)=>{var t=$(t),a=t.attr("data-name");this.isTabName(a)&&a!==this.TAB_BREAK_KEY&&t.remove()}),i&&i.prependTo(this.$el.find("ul.disabled"))}isTabName(e){return e.substring(0,this.TAB_BREAK_KEY.length-3)===this.TAB_BREAK_KEY.slice(0,-3)}getEditAttributesModalViewOptions(e){var t=super.getEditAttributesModalViewOptions(e);return this.isTabName(e.name)&&(t.attributeList=["tabLabel"],t.attributeDefs={tabLabel:{type:"varchar"}}),t}fetch(){var e,t,a=super.fetch(),s={};for(e in a)a[e].disabled&&this.links[e]||(s[e]=a[e],this.isTabName(e)&&e!==this.TAB_BREAK_KEY?(t=this.itemsData[e]||{},s[e].tabBreak=!0,s[e].tabLabel=t.tabLabel):(delete s[e].tabBreak,delete s[e].tabLabel));return delete s[this.TAB_BREAK_KEY],s}validate(e){return!!super.validate(e)}}e.default=s}),define("views/admin/layouts/modals/create",["exports","views/modal","views/record/edit-for-modal","model","views/fields/enum","views/fields/varchar"],function(e,t,a,s,i,l){function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=o(t),a=o(a),s=o(s),i=o(i),l=o(l);class n extends t.default{templateContent=`
        <div class="complex-text-container">{{complexText info}}</div>
        <div class="record no-side-margin">{{{record}}}</div>
    `;className="dialog dialog-record";constructor(e){super(),this.scope=e.scope}data(){return{info:this.translate("createInfo","messages","LayoutManager")}}setup(){this.headerText=this.translate("Create"),this.buttonList=[{name:"create",style:"danger",label:"Create",onClick:()=>this.actionCreate()},{name:"cancel",label:"Cancel"}],this.model=new s.default({type:"list",name:"listForMyEntityType",label:"List (for MyEntityType)"}),this.recordView=new a.default({model:this.model,detailLayout:[{columns:[[{view:new i.default({name:"type",params:{readOnly:!0,translation:"Admin.layouts",options:["list"]},labelText:this.translate("type","fields","Admin")})},{view:new l.default({name:"name",params:{required:!0,noSpellCheck:!0,pattern:"$latinLetters"},labelText:this.translate("name","fields")})},{view:new l.default({name:"label",params:{required:!0,pattern:"$noBadCharacters"},labelText:this.translate("label","fields","Admin")})}],[]]}]}),this.assignView("record",this.recordView,".record")}actionCreate(){this.recordView.fetch(),this.recordView.validate()||(this.disableButton("create"),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("Layout/action/create",{scope:this.scope,type:this.model.get("type"),name:this.model.get("name"),label:this.model.get("label")}).then(()=>{this.reRender(),Espo.Ui.success("Created",{suppress:!0}),this.trigger("done"),this.close()}).catch(()=>{this.enableButton("create")}))}}e.default=n}),define("views/admin/field-manager/detail-fields/attributes",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent=`
        {{#if dataList.length}}
            <table class="table table-bordered" style="table-layout: fixed;">
                <thead>
                    <tr>
                        <th
                        >{{translate 'name' category='fields' scope='Admin'}}</th>
                        <th style="width: 33%"
                        >{{translate 'type' category='fields' scope='Admin'}}</th>
                        <th style="width: 16%"
                        >{{translate 'notStorable' category='fields' scope='Admin'}}</th>
                        <th style="width: 16%"
                        >{{translate 'readOnly' category='fields' scope='Admin'}}</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each dataList}}
                        <tr>
                            <td>{{name}}</td>
                            <td>{{type}}</td>
                            <td>
                                <input
                                    type="checkbox"
                                    class="form-checkbox form-checkbox-simple"
                                    disabled
                                    {{#if notStorable}} checked {{/if}}
                                >
                            </td>
                            <td>
                                <input
                                    type="checkbox"
                                    class="form-checkbox form-checkbox-simple"
                                    disabled
                                    {{#if readOnly}} checked {{/if}}
                                >
                            </td>
                        </tr>
                    {{/each}}
                </tbody>
            </table>
        {{else}}
            <span class="none-value">{{translate 'None'}}</span>
        {{/if}}
    `;data(){return{dataList:this.model.attributes.attributes||[]}}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-operator-only-base",["exports","views/admin/dynamic-logic/conditions-string/item-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-operator-only-base";createValueFieldView(){}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/base",["exports","view","ui/select","model"],function(e,t,a,s){function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=i(t),a=i(a),s=i(s);class l extends t.default{template="admin/dynamic-logic/conditions/field-types/base";itemData;additionalData;type;field;scope;typeList;baseModel;events={'click > div > div > [data-action="remove"]':function(e){e.stopPropagation(),this.trigger("remove-item")}};data(){return{type:this.type,field:this.field,scope:this.scope,typeList:this.typeList,leftString:this.translateLeftString()}}translateLeftString(){return this.translate(this.field,"fields",this.scope)}setup(){this.type=this.options.type,this.field=this.options.field,this.scope=this.options.scope,this.fieldType=this.options.fieldType,this.itemData=this.options.itemData,this.additionalData=this.itemData.data||{},this.typeList=this.getMetadata().get(`clientDefs.DynamicLogic.fieldTypes.${this.fieldType}.typeList`),this.baseModel=new s.default,this.wait(!0),this.createModel().then(e=>{this.model=e,this.populateValues(),this.manageValue(),this.wait(!1)})}async createModel(){return this.getModelFactory().create(this.scope)}afterRender(){this.$type=this.$el.find('select[data-name="type"]'),a.default.init(this.$type.get(0)),this.$type.on("change",()=>{this.type=this.$type.val(),this.manageValue()})}populateValues(){"varchar-matches"===this.getValueType()?this.itemData.attribute&&this.baseModel.set(this.itemData.attribute,this.itemData.value):(this.itemData.attribute&&this.model.set(this.itemData.attribute,this.itemData.value),this.model.set(this.additionalData.values||{}))}getValueViewName(){var e=this.getMetadata().get(`entityDefs.${this.scope}.fields.${this.field}.type`)||"base";return this.getMetadata().get(`entityDefs.${this.scope}.fields.${this.field}.view`)||this.getFieldManager().getViewName(e)}getValueFieldName(){return this.field}getValueType(){return this.getMetadata().get(`clientDefs.DynamicLogic.fieldTypes.${this.fieldType}.conditionTypes.${this.type}.valueType`)||this.getMetadata().get(`clientDefs.DynamicLogic.conditionTypes.${this.type}.valueType`)}manageValue(){var e,t,a=this.getValueType();"field"===a?(e=this.getValueViewName(),t=this.getValueFieldName(),this.createView("value",e,{model:this.model,name:t,selector:".value-container",mode:"edit",readOnlyDisabled:!0},e=>{this.isRendered()&&e.render()})):"custom"===a?(this.clearView("value"),this["createValueView"+Espo.Utils.upperCaseFirst(this.type)]()):"varchar"===a?this.createView("value","views/fields/varchar",{model:this.model,name:this.getValueFieldName(),selector:".value-container",mode:"edit",readOnlyDisabled:!0},e=>{this.isRendered()&&e.render()}):"varchar-matches"===a?this.createView("value","views/fields/varchar",{model:this.baseModel,name:this.getValueFieldName(),selector:".value-container",mode:"edit",readOnlyDisabled:!0},e=>{this.isRendered()&&e.render()}):this.clearView("value")}getValueView(){return this.getView("value")}fetch(){var e=this.getValueView(),t="varchar-matches"===this.getValueType()?this.baseModel:this.model,a={type:this.type,attribute:this.field};return e&&(e.fetchToModel(),a.value=t.get(this.field)),a}}e.default=l}),define("views/settings/edit",["exports","views/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{scope="Settings";setupHeader(){this.createView("header",this.headerView,{model:this.model,fullSelector:"#main > .header",template:this.options.headerTemplate,label:this.options.label})}}e.default=s}),define("views/settings/record/edit",["exports","views/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{saveAndContinueEditingAction=!1;sideView=null;layoutName="settings";setup(){super.setup(),this.listenTo(this.model,"after:save",()=>{this.getConfig().set(this.model.getClonedAttributes())})}exit(e){"cancel"===e&&this.getRouter().navigate("#Admin",{trigger:!0})}}e.default=s}),define("views/settings/fields/quick-create-list",["exports","views/fields/array"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.options=Object.keys(this.getMetadata().get("scopes")).filter(e=>{if(!this.getMetadata().get(`scopes.${e}.disabled`))return this.getMetadata().get(`scopes.${e}.entity`)&&this.getMetadata().get(`scopes.${e}.object`)}).sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),super.setup()}}e.default=s}),define("views/role/record/table",["exports","view","model","views/fields/enum","view-record-helper"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{template="role/table";scopeList;type="acl";mode="detail";lowestLevelByDefault=!0;collaborators=!0;actionList=["create","read","edit","delete","stream"];accessList=["not-set","enabled","disabled"];fieldLevelList=["yes","no"];fieldActionList=["read","edit"];levelList=["yes","all","team","own","no"];booleanLevelList=["yes","no"];booleanActionList=["create"];levelListMap={recordAllTeamOwnNo:["all","team","own","no"],recordAllTeamNo:["all","team","no"],recordAllOwnNo:["all","own","no"],recordAllNo:["all","no"],record:["all","team","own","no"]};defaultLevels={delete:"no"};styleMap={yes:"success",all:"success",account:"info",contact:"info",team:"info",own:"warning",no:"danger",enabled:"success",disabled:"danger","not-set":"muted"};scopeLevelMemory;formModel;formRecordHelper;enumViews;acl;fieldTableDataList;data(){var e={};e.styleMap=this.styleMap,e.editMode="edit"===this.mode,e.actionList=this.actionList,e.accessList=this.accessList,e.fieldActionList=this.fieldActionList,e.fieldLevelList=this.fieldLevelList,e.tableDataList=this.getTableDataList(),e.fieldTableDataList=this.fieldTableDataList;let t=!1;return this.fieldTableDataList.forEach(e=>{e.list.length&&(t=!0)}),e.hasFieldLevelData=t,e.hiddenFields=this.formRecordHelper.getHiddenFields(),e}events={'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)},'click .action[data-action="addField"]':function(e){e=$(e.currentTarget).data().scope;this.showAddFieldModal(e)},'click .action[data-action="removeField"]':function(e){var t=$(e.currentTarget).data().scope,e=$(e.currentTarget).data().field;this.removeField(t,e)}};getTableDataList(){let o=this.acl.data,t=[],a=null;return this.scopeList.forEach(s=>{var e=this.getMetadata().get(`scopes.${s}.module`);a!==e&&(a=e,t.push(!1));let i="not-set",l=(this.final&&(i="enabled"),s in o&&(i=!1===o[s]?"disabled":"enabled"),[]);e=this.aclTypeMap[s];"boolean"!==this.aclTypeMap[s]&&this.actionList.forEach(t=>{var a=this.getMetadata().get(`scopes.${s}.${this.type}ActionList`);if(a&&!a.includes(t))l.push({action:t,levelList:null,level:null});else if("stream"!==t||this.getMetadata().get(`scopes.${s}.stream`)){let e=null;a=this.getLevelList(s,t);s in o&&("enabled"===i?!0!==o[s]&&null===(e=t in o[s]?o[s][t]:e)&&(e=a[a.length-1]):e="no"),e&&!a.includes(e)&&(a.push(e),a.sort((t,a)=>this.levelList.findIndex(e=>e===t)-this.levelList.findIndex(e=>e===a))),l.push({level:e,name:s+"-"+t,action:t,levelList:a})}else l.push({action:"stream",levelList:null,level:null})}),t.push({list:l,access:i,name:s,type:e})}),t}getLevelList(e,t){return this.booleanActionList.includes(t)?this.booleanLevelList:this.getMetadata().get(`scopes.${e}.${this.type}ActionLevelListMap.`+t)||this.getMetadata().get(`scopes.${e}.${this.type}LevelList`)||(t=this.aclTypeMap[e],this.levelListMap[t])||[]}setup(){this.mode=this.options.mode||"detail",this.final=this.options.final||!1,this.scopeLevelMemory={},this.setupData(),this.setupFormModel(),this.listenTo(this.model,"change:data change:fieldData",async()=>{this.setupData(),await this.setupFormModel(),this.isRendered()&&await this.reRenderPreserveSearch()}),this.listenTo(this.model,"sync",async()=>{this.setupData(),await this.setupFormModel(),this.isRendered()&&await this.reRenderPreserveSearch()}),this.template="role/table","edit"===this.mode&&(this.template="role/table-edit"),this.once("remove",()=>{$(window).off("scroll.scope-"+this.cid),$(window).off("resize.scope-"+this.cid),$(window).off("scroll.field-"+this.cid),$(window).off("resize.field-"+this.cid)})}async setupFormModel(){let l={fields:{}},o=(this.formModel=new a.default({},{defs:l}),this.formRecordHelper=new i.default,this.enumViews={},[]);return this.getTableDataList().forEach(i=>{if(i){let s=i.name;l.fields[s]={type:"enum",options:["not-set","enabled","disabled"],translation:"Role.options.accessList",style:this.styleMap},this.formModel.set(s,i.access,{silent:!0});var e,t=new n({name:s,model:this.formModel,mode:this.mode,inlineEditDisabled:!0,recordHelper:this.formRecordHelper});o.push(this.assignView(s,t,`td[data-name="${s}"]`)),i.list&&(this.listenTo(this.formModel,"change:"+s,()=>this.onSelectAccess(s)),i.list.forEach(e=>{var t,a;e.levelList&&(t=e.name,l.fields[t]={type:"enum",options:e.levelList,translation:"Role.options.levelList",style:this.styleMap},this.formModel.set(t,e.level,{silent:!0}),a=new n({name:t,model:this.formModel,mode:this.mode,inlineEditDisabled:!0,recordHelper:this.formRecordHelper}),this.enumViews[t]=a,o.push(this.assignView(t,a,`div[data-name="${t}"]`)),this.formRecordHelper.setFieldStateParam(t,"hidden","enabled"!==i.access),"read"===e.action&&this.listenTo(this.formModel,`change:${s}-read`,(e,t)=>{["edit","delete","stream"].forEach(e=>this.controlSelect(s,e,t))}),"edit"===e.action)&&this.listenTo(this.formModel,`change:${s}-edit`,(e,t)=>{this.controlSelect(s,"delete",t)})}),t=this.formModel.attributes[s+"-read"],e=this.formModel.attributes[s+"-edit"],t&&(this.controlSelect(s,"edit",t,!0),this.controlSelect(s,"stream",t,!0),e||this.controlSelect(s,"delete",t,!0)),e)&&this.controlSelect(s,"delete",e,!0)}}),this.fieldTableDataList.forEach(t=>{t.list.forEach(e=>this.setupFormField(t.name,e))}),Promise.all(o)}async setupFormField(s,e){let i=[],l=e.name,o=this.formModel.defs;e.list.forEach(e=>{var t=e.name,a=(o.fields[t]={type:"enum",options:["yes","no"],translation:"Role.options.levelList",style:this.styleMap},this.formModel.set(t,e.value,{silent:!0}),new n({name:t,model:this.formModel,mode:this.mode,inlineEditDisabled:!0,recordHelper:this.formRecordHelper}));this.enumViews[t]=a,i.push(this.assignView(t,a,`div[data-name="${t}"]`)),"read"===e.action&&this.listenTo(this.formModel,`change:${s}-${l}-read`,(e,t)=>{this.controlFieldEditSelect(s,l,t,!0)})}),e.list.length&&(e=this.formModel.attributes[`${s}-${l}-read`])&&this.controlFieldEditSelect(s,l,e),await Promise.all(i)}setupData(){this.acl={},this.options.acl?this.acl.data=this.options.acl.data:this.acl.data=Espo.Utils.cloneDeep(this.model.attributes.data||{}),this.options.acl?this.acl.fieldData=this.options.acl.fieldData:this.acl.fieldData=Espo.Utils.cloneDeep(this.model.attributes.fieldData||{}),this.setupScopeList(),this.setupFieldTableDataList()}getSortedScopeList(){let i=[null,"Crm"],l=this.getMetadata().get("scopes");return Object.keys(l).forEach(e=>{e=l[e].module;e&&"Custom"!==e&&!i.includes(e)&&i.push(e)}),i.push("Custom"),Object.keys(l).sort((e,t)=>{let a=l[e].module||null,s=l[t].module||null;return a!==s?i.findIndex(e=>e===a)-i.findIndex(e=>e===s):this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))})}setupScopeList(){this.aclTypeMap={},this.scopeList=[],this.getSortedScopeList().forEach(e=>{var t;this.getMetadata().get(`scopes.${e}.disabled`)||(t=this.getMetadata().get(`scopes.${e}.acl`))&&(this.scopeList.push(e),!0===(this.aclTypeMap[e]=t))&&(this.aclTypeMap[e]="record")})}setupFieldTableDataList(){this.fieldTableDataList=[],this.scopeList.forEach(i=>{var t=this.getMetadata().get("scopes."+i)||{};if(t.entity&&!t.aclFieldLevelDisabled&&!this.isAclFieldLevelDisabledForScope(i)){if(!(i in this.acl.fieldData))return"edit"===this.mode?void this.fieldTableDataList.push({name:i,list:[]}):void 0;let s=this.acl.fieldData[i];t=this.getFieldManager().getEntityTypeFieldList(i);this.getLanguage().sortFieldList(i,t);let e=[];t.forEach(a=>{if(a in s){let t=[];this.fieldActionList.forEach(e=>{t.push({name:i+`-${a}-`+e,action:e,value:s[a][e]||"yes"})}),"detail"===this.mode&&!t.length||e.push({name:a,list:t})}}),this.fieldTableDataList.push({name:i,list:e})}})}isAclFieldLevelDisabledForScope(e){return!!this.getMetadata().get(`scopes.${e}.aclFieldLevelDisabled`)}fetchScopeData(e){var t,a={},s=this.scopeList,i=this.actionList,l=this.aclTypeMap;for(t of s)if(!e||t===e){var o=this.formModel.attributes[t]||"not-set";if(e||"not-set"!==o)if(e||"disabled"!==o){let e=!0;if("boolean"!==l[t])for(var n in e={},i){var n=i[n],d=this.formModel.attributes[t+"-"+n];void 0!==d&&(e[n]=d)}a[t]=e}else a[t]=!1}return a}fetchFieldData(){let a={};return this.fieldTableDataList.forEach(e=>{let t={},i=e.name;e.list.forEach(a=>{var e=a.name;let s={};this.fieldActionList.forEach(e=>{var t=`${i}-${a.name}-`+e,t=this.formModel.attributes[t];void 0!==t&&(s[e]=t)}),t[e]=s}),a[i]=t}),a}afterRender(){this.$quickSearch=this.$el.find('input[data-name="quick-search"]'),"edit"!==this.mode&&"detail"!==this.mode||(this.initStickyHeader("scope"),this.initStickyHeader("field"))}controlFieldEditSelect(e,t,a,s){e+=`-${t}-edit`;let i=this.formModel.attributes[e];!s&&this.levelList.indexOf(i)<this.levelList.indexOf(a)&&(i=a);t=this.fieldLevelList.filter(e=>this.levelList.indexOf(e)>=this.levelList.indexOf(a)),s||this.formModel.set(e,i),this.formRecordHelper.setFieldOptionList(e,t),s=this.enumViews[e];s&&s.setOptionList(t)}controlSelect(e,t,a,s){let i=e+"-"+t,l=this.formModel.attributes[i];!s&&this.levelList.indexOf(l)<this.levelList.indexOf(a)&&(l=a);e=this.getLevelList(e,t).filter(e=>this.levelList.indexOf(e)>=this.levelList.indexOf(a)),s||setTimeout(()=>this.formModel.set(i,l),0),this.formRecordHelper.setFieldOptionList(i,e),t=this.enumViews[i];t&&t.setOptionList(e)}showAddFieldModal(s){var e=Object.keys(this.acl.fieldData[s]||{});this.createView("dialog","views/role/modals/add-field",{scope:s,ignoreFieldList:e,type:this.type},t=>{t.render(),this.listenTo(t,"add-fields",async e=>{t.close();let a=this.fieldTableDataList.find(e=>e.name===s);if(a){let t=[];e.filter(t=>!a.list.find(e=>e.name===t)).forEach(e=>{e={name:e,list:[{name:s+`-${e}-read`,action:"read",value:"no"},{name:s+`-${e}-edit`,action:"edit",value:"no"}]};a.list.unshift(e),t.push(this.setupFormField(s,e))}),await Promise.all(t),await this.reRenderPreserveSearch(),this.trigger("change")}})})}async removeField(t,a){var e=t+`-${a}-read`,s=t+`-${a}-edit`,e=(delete this.enumViews[e],delete this.enumViews[s],this.clearView(e),this.clearView(s),this.formModel.unset(e),this.formModel.unset(s),delete this.formModel.defs.fields[e],delete this.formModel.defs.fields[s],this.fieldTableDataList.find(e=>e.name===t));e&&-1!==(s=e.list.findIndex(e=>e.name===a))&&(e.list.splice(s,1),await this.reRenderPreserveSearch(),this.trigger("change"))}async reRenderPreserveSearch(){var e=this.$quickSearch.val(),t=window.scrollY;await this.reRender(),window.scrollTo({top:t}),this.$quickSearch.val(e),this.processQuickSearch(e)}initStickyHeader(e){let o=this.$el.find(".sticky-header-"+e),n=this.getThemeManager().getParam("screenWidthXs"),d=$(".detail-button-container"),r=this.$el.find(`table.${e}-level`);if(r.length&&d.length){let l=this.getThemeManager().getParam("navbarHeight")*this.getThemeManager().getFontSizeFactor();var t=()=>{var e,t,a,s,i;!(window.innerWidth<n)&&(e=d.get(0).getBoundingClientRect().top+d.outerHeight(),t=r.position().top,a=(t=(t=(t-=d.height())+r.find("tr > th").height())-l)+r.outerHeight(!0)-d.height(),s=window.scrollY,i=r.width(),t<s)&&s<a?(o.css({position:"fixed",marginTop:e+"px",top:0,width:i+"px",marginLeft:"1px"}),o.removeClass("hidden")):o.addClass("hidden")},a=$(window);a.off(`scroll.${e}-`+this.cid),a.on(`scroll.${e}-`+this.cid,t),a.off(`resize.${e}-`+this.cid),a.on(`resize.${e}-`+this.cid,t),t()}}getScopeActionView(e,t){return this.getView(e+"-"+t)}hideScopeActions(a){this.actionList.forEach(e=>{var t=this.getScopeActionView(a,e);t&&(t.hide(),this.formRecordHelper.setFieldStateParam(a+"-"+e,"hidden",!0))})}showScopeActions(a){this.actionList.forEach(e=>{var t=this.getScopeActionView(a,e);t&&(t.show(),this.formRecordHelper.setFieldStateParam(a+"-"+e,"hidden",!1))})}onSelectAccess(i){var e;if("enabled"!==this.formModel.attributes[i])e=this.fetchScopeData(i),this.hideScopeActions(i),delete this.scopeLevelMemory[i],i in e&&(this.scopeLevelMemory[i]=e[i]||{});else{this.showScopeActions(i);let s={};this.actionList.forEach(e=>{var t=(this.scopeLevelMemory[i]||{})[e];let a=t||this.defaultLevels[e];a=a||this.getLevelList(i,e)[0],!t&&this.lowestLevelByDefault&&(a=[...this.getLevelList(i,e)].pop()),s[i+"-"+e]=a}),setTimeout(()=>this.formModel.set(s),0)}}processQuickSearch(e){if(e=e.trim()){let s=[],i=e.toLowerCase();this.scopeList.forEach(e=>{let t=!1;var a=this.getLanguage().translate(e,"scopeNamesPlural");(t=0!==a.toLowerCase().indexOf(i)&&0!==e.toLowerCase().indexOf(i)?t:!0)||a.split(" ").concat(a.split(" ")).forEach(e=>{0===e.toLowerCase().indexOf(i)&&(t=!0)}),t&&s.push(e)}),0===s.length?this.$el.find("table tr.item-row").addClass("hidden"):(this.$el.find('table tr.item-row[data-name="_"]').addClass("hidden"),this.scopeList.forEach(e=>{var t=this.$el.find(`table tr.item-row[data-name="${e}"]`);s.includes(e)?t.removeClass("hidden"):t.addClass("hidden")}))}else this.$el.find("table tr.item-row").removeClass("hidden")}}class n extends s.default{nativeSelect=!0}e.default=o}),define("views/role/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{quickDetailDisabled=!0;quickEditDisabled=!0;massActionList=["remove","export"];checkAllResultDisabled=!0}e.default=s}),define("views/role/record/edit",["exports","views/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{tableView="views/role/record/table";sideView=!1;isWide=!0;stickButtonsContainerAllTheWay=!0;fetch(){var e=super.fetch();return e.data=this.getTableView().fetchScopeData(),e.fieldData=this.getTableView().fetchFieldData(),e}setup(){super.setup(),this.createView("extra",this.tableView,{mode:"edit",selector:".extra",model:this.model},e=>{this.listenTo(e,"change",()=>{var e=this.fetch();this.model.set(e)})})}getTableView(){return this.getView("extra")}}e.default=s}),define("views/role/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{tableView="views/role/record/table";sideView=!1;isWide=!0;editModeDisabled=!0;stickButtonsContainerAllTheWay=!0;setup(){super.setup(),this.createView("extra",this.tableView,{selector:".extra",model:this.model})}}e.default=s}),define("views/inbound-email/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.setupFieldsBehaviour(),this.initSslFieldListening()}modifyDetailLayout(e){e.filter(e=>"$label:SMTP"===e.tabLabel).forEach(e=>{e.rows.forEach(e=>{e.forEach(e=>{var t=this.translate(e.name,"fields","InboundEmail");t&&0===t.indexOf("SMTP ")&&(e.labelText=Espo.Utils.upperCaseFirst(t.substring(5)))})})})}wasFetched(){return!this.model.isNew()&&!!(this.model.get("fetchData")||{}).lastUID}initSmtpFieldsControl(){this.controlSmtpFields(),this.listenTo(this.model,"change:useSmtp",this.controlSmtpFields,this),this.listenTo(this.model,"change:smtpAuth",this.controlSmtpFields,this)}controlSmtpFields(){this.model.get("useSmtp")?(this.showField("smtpHost"),this.showField("smtpPort"),this.showField("smtpAuth"),this.showField("smtpSecurity"),this.showField("smtpTestSend"),this.showField("fromName"),this.showField("smtpIsShared"),this.showField("smtpIsForMassEmail"),this.setFieldRequired("smtpHost"),this.setFieldRequired("smtpPort"),this.controlSmtpAuthField()):(this.hideField("smtpHost"),this.hideField("smtpPort"),this.hideField("smtpAuth"),this.hideField("smtpUsername"),this.hideField("smtpPassword"),this.hideField("smtpAuthMechanism"),this.hideField("smtpSecurity"),this.hideField("smtpTestSend"),this.hideField("fromName"),this.hideField("smtpIsShared"),this.hideField("smtpIsForMassEmail"),this.setFieldNotRequired("smtpHost"),this.setFieldNotRequired("smtpPort"),this.setFieldNotRequired("smtpUsername"))}controlSmtpAuthField(){this.model.get("smtpAuth")?(this.showField("smtpUsername"),this.showField("smtpPassword"),this.showField("smtpAuthMechanism"),this.setFieldRequired("smtpUsername")):(this.hideField("smtpUsername"),this.hideField("smtpPassword"),this.hideField("smtpAuthMechanism"),this.setFieldNotRequired("smtpUsername"))}controlStatusField(){var e=["username","port","host","monitoredFolders"];"Active"===this.model.get("status")&&this.model.get("useImap")?e.forEach(e=>{this.setFieldRequired(e)}):e.forEach(e=>{this.setFieldNotRequired(e)})}setupFieldsBehaviour(){this.controlStatusField(),this.listenTo(this.model,"change:status",(e,t,a)=>{a.ui&&this.controlStatusField()}),this.listenTo(this.model,"change:useImap",(e,t,a)=>{a.ui&&this.controlStatusField()}),this.wasFetched()?this.setFieldReadOnly("fetchSince"):this.setFieldNotReadOnly("fetchSince"),this.initSmtpFieldsControl();let s=e=>{e.get("createCase")?this.showField("caseDistribution"):this.hideField("caseDistribution"),e.get("createCase")&&-1!==["Round-Robin","Least-Busy"].indexOf(e.get("caseDistribution"))?(this.setFieldRequired("team"),this.showField("targetUserPosition")):(this.setFieldNotRequired("team"),this.hideField("targetUserPosition")),e.get("createCase")&&"Direct-Assignment"===e.get("caseDistribution")?(this.setFieldRequired("assignToUser"),this.showField("assignToUser")):(this.setFieldNotRequired("assignToUser"),this.hideField("assignToUser")),e.get("createCase")&&""!==e.get("createCase")?this.showField("team"):this.hideField("team")};this.listenTo(this.model,"change:createCase",(e,t,a)=>{s(e),a.ui&&!e.get("createCase")&&this.model.set({caseDistribution:"",teamId:null,teamName:null,assignToUserId:null,assignToUserName:null,targetUserPosition:""})}),s(this.model),this.listenTo(this.model,"change:caseDistribution",(e,t,a)=>{s(e),a.ui&&setTimeout(()=>{this.model.get("caseDistribution")?("Direct-Assignment"===this.model.get("caseDistribution")&&this.model.set({targetUserPosition:""}),this.model.set({assignToUserId:null,assignToUserName:null})):this.model.set({assignToUserId:null,assignToUserName:null,targetUserPosition:""})},10)})}initSslFieldListening(){this.listenTo(this.model,"change:security",(e,t,a)=>{a.ui&&("SSL"===t?this.model.set("port",993):this.model.set("port",143))}),this.listenTo(this.model,"change:smtpSecurity",(e,t,a)=>{a.ui&&("SSL"===t?this.model.set("smtpPort",465):"TLS"===t?this.model.set("smtpPort",587):this.model.set("smtpPort",25))})}}e.default=s}),define("views/admin/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/index";events={"click [data-action]":function(e){Espo.Utils.handleAction(this,e.originalEvent,e.currentTarget)},'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)}};data(){return{panelDataList:this.panelDataList,iframeUrl:this.iframeUrl,iframeHeight:this.getConfig().get("adminPanelIframeHeight")||1330,iframeDisabled:this.getConfig().get("adminPanelIframeDisabled")||!1}}afterRender(){var e=this.$el.find('input[data-name="quick-search"]');this.quickSearchText&&(e.val(this.quickSearchText),this.processQuickSearch(this.quickSearchText)),e.get(0).focus({preventScroll:!0})}setup(){this.panelDataList=[];var e,a=this.getMetadata().get("app.adminPanel")||{};for(e in a){let t=Espo.Utils.cloneDeep(a[e]);t.name=e,t.itemList=t.itemList||[],t.label=this.translate(t.label,"labels","Admin"),t.itemList&&t.itemList.forEach(e=>{e.label=this.translate(e.label,"labels","Admin"),e.description?(e.keywords=(this.getLanguage().get("Admin","keywords",e.description)||"").split(","),e.keywords=e.keywords.map(e=>e.trim().toLowerCase())):e.keywords=[]}),t.items&&t.items.forEach(e=>{e.label=this.translate(e.label,"labels","Admin"),t.itemList.push(e),e.keywords=[]}),this.panelDataList.push(t)}this.panelDataList.sort((e,t)=>("order"in e||!("order"in t))&&"order"in t?e.order-t.order:0);var t=["version="+encodeURIComponent(this.getConfig().get("version")),"css="+encodeURIComponent(this.getConfig().get("siteUrl")+"/"+this.getThemeManager().getStylesheet())];this.iframeUrl=this.getConfig().get("adminPanelIframeUrl")||"https://s.espocrm.com/",~this.iframeUrl.indexOf("?")?this.iframeUrl+="&"+t.join("&"):this.iframeUrl+="?"+t.join("&"),this.getConfig().get("adminNotificationsDisabled")||this.createView("notificationsPanel","views/admin/panels/notifications",{selector:".notifications-panel-container"})}processQuickSearch(n){n=n.trim(),this.quickSearchText=n;var e=this.$noData||this.$el.find(".no-data");if(e.addClass("hidden"),n){n=n.toLowerCase(),this.$el.find(".admin-content-section").addClass("hidden"),this.$el.find(".admin-content-row").addClass("hidden");let o=!1;this.panelDataList.forEach((e,s)=>{let i=!1,l=!1;e.label&&0===e.label.toLowerCase().indexOf(n)&&(i=!0,l=!0),e.itemList.forEach((e,a)=>{if(e.label){let t=!1;(t=(t=l?!0:t)||0===e.label.toLowerCase().indexOf(n))||(e.label.split(" ").forEach(e=>{0===e.toLowerCase().indexOf(n)&&(t=!0)}),t=t||~e.keywords.indexOf(n))||3<=n.length&&e.keywords.forEach(e=>{0===e.indexOf(n)&&(t=!0)}),t&&(i=!0,this.$el.find('.admin-content-section[data-index="'+s.toString()+'"] .admin-content-row[data-index="'+a.toString()+'"]').removeClass("hidden"),o=!0)}}),i&&(this.$el.find('.admin-content-section[data-index="'+s.toString()+'"]').removeClass("hidden"),o=!0)}),o||e.removeClass("hidden")}else this.$el.find(".admin-content-section").removeClass("hidden"),this.$el.find(".admin-content-row").removeClass("hidden")}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Administration"))}actionClearCache(){this.trigger("clear-cache")}actionRebuild(){this.trigger("rebuild")}}e.default=s}),define("views/admin/link-manager/index",["exports","view","views/admin/link-manager/modals/edit-params"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/link-manager/index";scope;data(){return{linkDataList:this.linkDataList,scope:this.scope,isCreatable:this.isCustomizable}}events={'click a[data-action="editLink"]':function(e){e=$(e.currentTarget).data("link");this.editLink(e)},'click button[data-action="createLink"]':function(){this.createLink()},'click [data-action="removeLink"]':function(e){let t=$(e.currentTarget).data("link");e=this.translate("confirmRemoveLink","messages","EntityManager").replace("{link}",t);this.confirm(e,()=>{this.removeLink(t)})},'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)}};computeRelationshipType(e,t){return"hasMany"===e?"hasMany"===t?"manyToMany":"belongsTo"===t?"oneToMany":void 0:"belongsTo"===e?"hasMany"===t?"manyToOne":"hasOne"===t?"oneToOneRight":void 0:"belongsToParent"===e?"hasChildren"===t?"childrenToParent":void 0:"hasChildren"===e?"belongsToParent"===t?"parentToChildren":void 0:"hasOne"===e&&"belongsTo"===t?"oneToOneLeft":void 0}setupLinkData(){this.linkDataList=[],this.isCustomizable=!!this.getMetadata().get(`scopes.${this.scope}.customizable`)&&!1!==this.getMetadata().get(`scopes.${this.scope}.entityManager.relationships`);let n=this.getMetadata().get(`entityDefs.${this.scope}.links`);Object.keys(n).sort((e,t)=>e.localeCompare(t)).forEach(e=>{var t=n[e];let a,s=this.isCustomizable;if("belongsToParent"===t.type)a="childrenToParent";else{if(!t.entity)return;t.foreign?(i=this.getMetadata().get(`entityDefs.${t.entity}.links.${t.foreign}.type`),a=this.computeRelationshipType(t.type,i)):(s=!1,t.relationName?a="manyToMany":"belongsTo"===t.type&&(a="manyToOne"))}var i=t.entity?this.getLanguage().translate(t.entity,"scopeNames"):void 0,l=t.isCustom,o="hasMany"===t.type||"hasChildren"===t.type;this.linkDataList.push({link:e,isCustom:t.isCustom,isRemovable:l,customizable:t.customizable,isEditable:s,hasDropdown:s||l||o,hasEditParams:o,type:a,entityForeign:t.entity,entity:this.scope,labelEntityForeign:i,linkForeign:t.foreign,label:this.getLanguage().translate(e,"links",this.scope),labelForeign:this.getLanguage().translate(t.foreign,"links",t.entity)})})}setup(){this.addActionHandler("editParams",(e,t)=>this.actionEditParams(t.dataset.link)),this.scope=this.options.scope||null,this.setupLinkData(),this.on("after:render",()=>{this.renderHeader()})}afterRender(){this.$noData=this.$el.find(".no-data"),this.$el.find('input[data-name="quick-search"]').focus()}createLink(){this.createView("edit","views/admin/link-manager/modals/edit",{scope:this.scope},e=>{e.render(),this.listenTo(e,"after:save",()=>{this.clearView("edit"),this.setupLinkData(),this.render()}),this.listenTo(e,"close",()=>{this.clearView("edit")})})}editLink(e){this.createView("edit","views/admin/link-manager/modals/edit",{scope:this.scope,link:e},e=>{e.render(),this.listenTo(e,"after:save",()=>{this.clearView("edit"),this.setupLinkData(),this.render()}),this.listenTo(e,"close",()=>{this.clearView("edit")})})}removeLink(e){Espo.Ajax.postRequest("EntityManager/action/removeLink",{entity:this.scope,link:e}).then(()=>{this.$el.find(`table tr[data-link="${e}"]`).remove(),this.getMetadata().loadSkipCache().then(()=>{this.setupLinkData(),Espo.Ui.success(this.translate("Removed"),{suppress:!0}),this.reRender()})})}renderHeader(){var e=$("#scope-header");this.scope?e.show().html(this.getLanguage().translate(this.scope,"scopeNames")):e.html("")}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Entity Manager","labels","Admin"))}processQuickSearch(e){e=e.trim();var t=this.$noData;if(t.addClass("hidden"),e){let l=[],o=e.toLowerCase();this.linkDataList.forEach(e=>{let t=!1;var a=e.label||"",s=e.link||"",i=e.entityForeign||"",e=e.labelEntityForeign||"";(t=0!==a.toLowerCase().indexOf(o)&&0!==s.toLowerCase().indexOf(o)&&0!==i.toLowerCase().indexOf(o)&&0!==e.toLowerCase().indexOf(o)?t:!0)||s.split(" ").concat(a.split(" ")).concat(i.split(" ")).concat(e.split(" ")).forEach(e=>{0===e.toLowerCase().indexOf(o)&&(t=!0)}),t&&l.push(s)}),0===l.length?(this.$el.find("table tr.link-row").addClass("hidden"),t.removeClass("hidden")):this.linkDataList.map(e=>e.link).forEach(e=>{~l.indexOf(e)?this.$el.find(`table tr.link-row[data-link="${e}"]`).removeClass("hidden"):this.$el.find(`table tr.link-row[data-link="${e}"]`).addClass("hidden")})}else this.$el.find("table tr.link-row").removeClass("hidden")}async actionEditParams(e){e=new a.default({entityType:this.scope,link:e});await this.assignView("dialog",e),await e.render()}}e.default=i}),define("views/admin/layouts/list",["exports","views/admin/layouts/rows"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name","widthComplex","width","widthPx","link","notSortable","noLabel","align","view","customLabel","label","hidden"];dataAttributesDefs={widthComplex:{label:"width",type:"base",view:"views/admin/layouts/fields/width-complex",tooltip:"width",notStorable:!0},link:{type:"bool",tooltip:!0},width:{type:"float",min:0,max:100,hidden:!0},widthPx:{type:"int",min:0,max:720,hidden:!0},notSortable:{type:"bool",tooltip:!0},align:{type:"enum",options:["left","right"]},view:{type:"varchar",readOnly:!0},noLabel:{type:"bool",tooltip:!0},customLabel:{type:"varchar",readOnly:!0},name:{type:"varchar",readOnly:!0},label:{type:"varchar",readOnly:!0},hidden:{type:"bool"}};dataAttributesDynamicLogicDefs={fields:{widthPx:{visible:{conditionGroup:[{attribute:"width",type:"isEmpty"}]}}}};editable=!0;languageCategory="fields";ignoreList=[];ignoreTypeList=[];defaultWidth=16;setup(){super.setup(),this.wait(new Promise(e=>this.loadLayout(()=>e())))}loadLayout(a){this.getModelFactory().create(Espo.Utils.hyphenToUpperCamelCase(this.scope),t=>{this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{this.readDataFromLayout(t,e),a()})})}readDataFromLayout(e,t){var a,s=[];for(a in e.defs.fields)this.checkFieldType(e.getFieldParam(a,"type"))&&this.isFieldEnabled(e,a)&&s.push(a);s.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),this.enabledFieldsList=[],this.enabledFields=[],this.disabledFields=[];var i,l,o,n,d,r=[];let f=[];for(i of t){var h=this.getLanguage().translate(i.name,"fields",this.scope);r.includes(h)&&f.push(h),r.push(h),this.enabledFields.push({name:i.name,labelText:h}),this.enabledFieldsList.push(i.name)}for(l of s)this.enabledFieldsList.includes(l)||(n=this.getLanguage().translate(l,"fields",this.scope),r.includes(n)&&f.push(n),r.push(n),n={name:o=l,labelText:n},d=this.getMetadata().get(["entityDefs",this.scope,"fields",o,"type"]),this.itemsData[o]=this.itemsData[o]||{},d&&this.getMetadata().get(`fields.${d}.notSortable`)&&(this.itemsData[o].notSortable=n.notSortable=!0),n.width=this.defaultWidth,this.itemsData[o].width=this.defaultWidth,this.disabledFields.push(n));this.enabledFields.forEach(e=>{f.includes(e.labelText)&&(e.labelText+=" ("+e.name+")")}),this.disabledFields.forEach(e=>{f.includes(e.labelText)&&(e.labelText+=" ("+e.name+")")}),this.rowLayout=t;for(let a of this.rowLayout){let t=this.getLanguage().translate(a.name,"fields",this.scope);this.enabledFields.forEach(e=>{a.name===e.name&&(t=e.labelText)}),a.labelText=t,this.itemsData[a.name]=Espo.Utils.cloneDeep(a)}}checkFieldType(e){return!0}isFieldEnabled(e,t){if(-1!==this.ignoreList.indexOf(t))return!1;if(-1!==this.ignoreTypeList.indexOf(e.getFieldParam(t,"type")))return!1;var a=e.getFieldParam(t,"layoutAvailabilityList");let s=this.realType;return"listSmall"===s&&(s="list"),!(a&&!a.includes(this.type)&&!a.includes(s)||(a=e.getFieldParam(t,"layoutIgnoreList")||[]).includes(s)||a.includes(this.type)||e.getFieldParam(t,"disabled")||e.getFieldParam(t,"utility")||e.getFieldParam(t,"layoutListDisabled"))}}e.default=s}),define("views/admin/layouts/index",["exports","view","views/admin/layouts/default-page","views/admin/layouts/modals/create"],function(e,t,a,s){function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=i(t),a=i(a),s=i(s);class l extends t.default{template="admin/layouts/index";scopeList=null;baseUrl="#Admin/layouts";typeList=["list","detail","listSmall","detailSmall","defaultSidePanel","bottomPanelsDetail","filters","massUpdate","sidePanelsDetail","sidePanelsEdit","sidePanelsDetailSmall","sidePanelsEditSmall"];scope=null;type=null;data(){return{scopeList:this.scopeList,typeList:this.typeList,scope:this.scope,layoutScopeDataList:this.getLayoutScopeDataList(),headerHtml:this.getHeaderHtml(),em:this.em}}setup(){this.addHandler("click","#layouts-menu a.layout-link","onLayoutLinkClick"),this.addHandler("click","a.accordion-toggle","onItemHeaderClick"),this.addHandler("keydown.shortcuts","","onKeyDown"),this.addActionHandler("createLayout",()=>this.actionCreateLayout()),this.em=this.options.em||!1,this.scope=this.options.scope||null,this.type=this.options.type||null,this.scopeList=[],this.getMetadata().getScopeList().sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))).forEach(e=>{this.getMetadata().get("scopes."+e+".entity")&&this.getMetadata().get("scopes."+e+".layouts")&&this.scopeList.push(e)}),this.em&&this.scope&&(this.scopeList.includes(this.scope)?this.scopeList=[this.scope]:this.scopeList=[]),this.on("after:render",()=>{$("#layouts-menu a[data-scope='"+this.options.scope+"'][data-type='"+this.options.type+"']").addClass("disabled"),this.renderLayoutHeader(),this.options.scope&&this.options.type||(this.checkLayout(),this.renderDefaultPage()),this.scope&&this.options.type&&(this.checkLayout(),this.openLayout(this.options.scope,this.options.type))})}checkLayout(){let t=this.options.scope;var e=this.options.type;if(t){var a=this.getLayoutScopeDataList().find(e=>e.scope===t);if(!a)throw new Espo.Exceptions.NotFound("Layouts not available for entity type.");if(e&&!a.typeList.includes(e))throw new Espo.Exceptions.NotFound("The layout type is not available for the entity type.")}}afterRender(){Espo.Ui.notify(),this.controlActiveButton()}controlActiveButton(){var e;this.scope&&(e=this.$el.find(`.accordion-toggle[data-scope="${this.scope}"]`),this.undisableLinks(),(this.em&&this.scope&&!this.type?e:(e.removeClass("disabled"),this.$el.find(`a.layout-link[data-scope="${this.scope}"][data-type="${this.type}"]`))).addClass("disabled"))}onLayoutLinkClick(e){e.preventDefault();let t=$(e.target).data("scope"),a=$(e.target).data("type");this.getContentView()&&this.scope===t&&this.type===a||this.getRouter().checkConfirmLeaveOut(()=>{this.openLayout(t,a),this.controlActiveButton()})}openDefaultPage(){this.clearView("content"),this.type=null,this.renderDefaultPage(),this.controlActiveButton(),this.navigate(this.scope)}onItemHeaderClick(e){if(e.preventDefault(),this.em)return this.getContentView()?void this.getRouter().checkConfirmLeaveOut(()=>{this.openDefaultPage()}):void 0;e=$(e.target).data("scope"),e=$('.collapse[data-scope="'+e+'"]');e.hasClass("in")?e.collapse("hide"):e.collapse("show")}onKeyDown(e){var t=Espo.Utils.getKeyFromKeyEvent(e);!this.hasView("content")||"Control+Enter"!==t&&"Control+KeyS"!==t||(e.stopPropagation(),e.preventDefault(),this.getContentView().actionSave())}undisableLinks(){$("#layouts-menu a.layout-link").removeClass("disabled")}getContentView(){return this.getView("content")}openLayout(e,t){this.scope=e,this.type=t,this.navigate(e,t),Espo.Ui.notifyWait();var a=this.getMetadata().get("clientDefs."+e+".additionalLayouts."+t+".type")||t;this.createView("content","views/admin/layouts/"+Espo.Utils.camelCaseToHyphen(a),{fullSelector:"#layout-content",scope:e,type:t,realType:a,setId:this.setId,em:this.em},e=>{this.renderLayoutHeader(),e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0),this.em&&(this.listenToOnce(e,"cancel",()=>{this.openDefaultPage()}),this.listenToOnce(e,"after-delete",()=>{this.openDefaultPage(),Promise.all([this.getMetadata().loadSkipCache(),this.getLanguage().loadSkipCache()]).then(()=>{this.reRender()})}))})}navigate(e,t){let a="#Admin/layouts/scope="+e;t&&(a+="&type="+t),this.em&&(a+="&em=true"),this.getRouter().navigate(a,{trigger:!1})}renderDefaultPage(){$("#layout-header").html("").hide(),this.em?this.assignView("default",new a.default,"#layout-content").then(e=>{e.render()}):(this.clearView("default"),$("#layout-content").html(this.translate("selectLayout","messages","Admin")))}renderLayoutHeader(){var e,t=$("#layout-header");this.scope?(e=[],this.em||e.push($("<span>").text(this.translate(this.scope,"scopeNames"))),e.push($("<span>").text(this.translateLayoutName(this.type,this.scope))),e=e.map(e=>e.get(0).outerHTML).join(' <span class="breadcrumb-separator"><span></span></span> '),t.show().html(e)):t.html("")}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Layout Manager","labels","Admin"))}getHeaderHtml(){var e=[],t=$("<a>").attr("href","#Admin").text(this.translate("Administration"));return e.push(t),this.em?(e.push($("<a>").attr("href","#Admin/entityManager").text(this.translate("Entity Manager","labels","Admin"))),this.scope&&(e.push($("<a>").attr("href","#Admin/entityManager/scope="+this.scope).text(this.translate(this.scope,"scopeNames"))),e.push($("<span>").text(this.translate("Layouts","labels","EntityManager"))))):e.push($("<span>").text(this.translate("Layout Manager","labels","Admin"))),e.map(e=>e.get(0).outerHTML).join(' <span class="breadcrumb-separator"><span></span></span> ')}translateLayoutName(e,t){return this.getLanguage().get(t,"layouts",e)?this.getLanguage().translate(e,"layouts",t):this.getLanguage().translate(e,"layouts","Admin")}getLayoutScopeDataList(){let l=[];return this.scopeList.forEach(a=>{var e,t={};let s=Espo.Utils.clone(this.typeList);for(e in t.scope=a,t.url=this.baseUrl+"/scope="+a,this.em&&(t.url+="&em=true"),this.getMetadata().get(["clientDefs",a,"bottomPanels","edit"])&&s.push("bottomPanelsEdit"),(this.getMetadata().get(["clientDefs",a,"defaultSidePanelDisabled"])||this.getMetadata().get(["clientDefs",a,"defaultSidePanelFieldList"]))&&(s=s.filter(e=>"defaultSidePanel"!==e)),this.getMetadata().get(["clientDefs",a,"kanbanViewMode"])&&s.push("kanban"),this.getMetadata().get(["clientDefs",a,"additionalLayouts"])||{})s.push(e);s=s.filter(e=>!this.getMetadata().get(["clientDefs",a,"layout"+Espo.Utils.upperCaseFirst(e)+"Disabled"]));let i=[];s.forEach(e=>{let t=this.baseUrl+"/scope="+a+"&type="+e;this.em&&(t+="&em=true"),i.push({type:e,url:t,label:this.translateLayoutName(e,a)})}),t.typeList=s,t.typeDataList=i,l.push(t)}),l}actionCreateLayout(){var e=new s.default({scope:this.scope});this.assignView("dialog",e).then(e=>{e.render(),this.listenToOnce(e,"done",()=>{Promise.all([this.getMetadata().loadSkipCache(),this.getLanguage().loadSkipCache()]).then(()=>{this.reRender()})})})}}e.default=l}),define("views/admin/layouts/detail",["exports","views/admin/layouts/grid"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name","fullWidth","customLabel","noLabel"];panelDataAttributeList=["panelName","dynamicLogicVisible","style","dynamicLogicStyled","tabBreak","tabLabel","hidden","noteText","noteStyle"];dataAttributesDefs={fullWidth:{type:"bool"},name:{readOnly:!0},label:{type:"varchar",readOnly:!0},customLabel:{type:"varchar",readOnly:!0},noLabel:{type:"bool",readOnly:!0}};panelDataAttributesDefs={panelName:{type:"varchar"},style:{type:"enum",options:["default","success","danger","warning","info"],style:{info:"info",success:"success",danger:"danger",warning:"warning"},default:"default",translation:"LayoutManager.options.style",tooltip:"panelStyle"},dynamicLogicVisible:{type:"base",view:"views/admin/field-manager/fields/dynamic-logic-conditions"},dynamicLogicStyled:{type:"base",view:"views/admin/field-manager/fields/dynamic-logic-conditions",tooltip:"dynamicLogicStyled"},hidden:{type:"bool",tooltip:"hiddenPanel"},tabBreak:{type:"bool",tooltip:"tabBreak"},tabLabel:{type:"varchar"},noteText:{type:"text",tooltip:"noteText"},noteStyle:{type:"enum",options:["info","success","danger","warning"],style:{info:"info",success:"success",danger:"danger",warning:"warning"},default:"info",translation:"LayoutManager.options.style"}};defaultPanelFieldList=["modifiedAt","createdAt","modifiedBy","createdBy"];panelDynamicLogicDefs={fields:{tabLabel:{visible:{conditionGroup:[{attribute:"tabBreak",type:"isTrue"}]}},dynamicLogicStyled:{visible:{conditionGroup:[{attribute:"style",type:"notEquals",value:"default"}]}},noteStyle:{visible:{conditionGroup:[{attribute:"noteText",type:"isNotEmpty"}]}}}};setup(){super.setup(),this.panelDataAttributesDefs=Espo.Utils.cloneDeep(this.panelDataAttributesDefs),this.panelDataAttributesDefs.dynamicLogicVisible.scope=this.scope,this.panelDataAttributesDefs.dynamicLogicStyled.scope=this.scope,this.wait(!0),this.loadLayout(()=>{this.setupPanels(),this.wait(!1)})}loadLayout(e){let s,i;var t=[];t.push(new Promise(a=>{this.getModelFactory().create(this.scope,t=>{this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{s=e,i=t,a()})})})),["detail","detailSmall"].includes(this.type)&&t.push(new Promise(t=>{this.getHelper().layoutManager.getOriginal(this.scope,"sidePanels"+Espo.Utils.upperCaseFirst(this.type),this.setId,e=>{this.sidePanelsLayout=e,t()})})),t.push(new Promise(t=>{this.getMetadata().get(["clientDefs",this.scope,"layoutDefaultSidePanelDisabled"])||this.typeDefs.allFields?t():this.getHelper().layoutManager.getOriginal(this.scope,"defaultSidePanel",this.setId,e=>{this.defaultPanelFieldList=Espo.Utils.clone(this.defaultPanelFieldList),e.forEach(e=>{let t=e.name;t&&(":assignedUser"===t&&(t="assignedUser"),this.defaultPanelFieldList.includes(t)||this.defaultPanelFieldList.push(t))}),t()})})),Promise.all(t).then(()=>{this.readDataFromLayout(i,s),e&&e()})}readDataFromLayout(e,t){var a,s,i=[];for(a in e.defs.fields)this.isFieldEnabled(e,a)&&i.push(a);for(s in this.enabledFields=[],this.disabledFields=[],(this.panels=t).forEach(e=>{e.rows.forEach(e=>{e.forEach(e=>{this.enabledFields.push(e.name)})})}),i.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),i)_.contains(this.enabledFields,i[s])||this.disabledFields.push(i[s])}isFieldEnabled(e,t){if(this.hasDefaultPanel()&&this.defaultPanelFieldList.includes(t))return!1;var a=e.getFieldParam(t,"layoutAvailabilityList");let s=this.realType;return"detailSmall"===s&&(s="detail"),!(a&&!a.includes(this.type)&&!a.includes(s)||(a=e.getFieldParam(t,"layoutIgnoreList")||[]).includes(s)||a.includes(this.type)||e.getFieldParam(t,"disabled")||e.getFieldParam(t,"utility")||e.getFieldParam(t,"layoutDetailDisabled"))}hasDefaultPanel(){if(!1===this.getMetadata().get(["clientDefs",this.scope,"defaultSidePanel",this.viewType]))return!1;if(this.getMetadata().get(["clientDefs",this.scope,"defaultSidePanelDisabled"]))return!1;if(this.sidePanelsLayout)for(var e in this.sidePanelsLayout)if("default"===e&&this.sidePanelsLayout[e].disabled)return!1;return!0}validate(e){if(!super.validate(e))return!1;let s=[],i=(e.forEach(e=>{e.rows.forEach(e=>{e.forEach(e=>{!1!==e&&null!==e&&e.name&&s.push(e.name)})})}),[]),l=!1;return s.forEach(a=>{if(!l){let t=(this.getMetadata().get(["entityDefs",this.scope,"fields",a])||{}).detailLayoutIncompatibleFieldList||[];t.forEach(e=>{l||~s.indexOf(e)&&(l=!0,i=[a].concat(t))})}}),!l||(Espo.Ui.error(this.translate("fieldsIncompatible","messages","LayoutManager").replace("{fields}",i.map(e=>this.translate(e,"fields",this.scope)).join(", "))),!1)}}e.default=s}),define("views/admin/layouts/bottom-panels-edit",["exports","views/admin/layouts/bottom-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{hasStream=!1;hasRelationships=!1;viewType="edit"}e.default=s}),define("views/admin/integrations/edit",["exports","view","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/integrations/edit";data(){return{integration:this.integration,dataFieldList:this.dataFieldList,helpText:this.helpText}}events={'click button[data-action="cancel"]':function(){this.getRouter().navigate("#Admin/integrations",{trigger:!0})},'click button[data-action="save"]':function(){this.save()}};setup(){this.integration=this.options.integration,this.helpText=!1,this.getLanguage().has(this.integration,"help","Integration")&&(this.helpText=this.translate(this.integration,"help","Integration")),this.fieldList=[],this.dataFieldList=[],this.model=new a.default,this.model.id=this.integration,this.model.name="Integration",this.model.urlRoot="Integration",this.model.defs={fields:{enabled:{required:!0,type:"bool"}}},this.wait(!0),this.fields=this.getMetadata().get(`integrations.${this.integration}.fields`),Object.keys(this.fields).forEach(e=>{this.model.defs.fields[e]=this.fields[e],this.dataFieldList.push(e)}),this.model.populateDefaults(),this.listenToOnce(this.model,"sync",()=>{this.createFieldView("bool","enabled"),Object.keys(this.fields).forEach(e=>{this.createFieldView(this.fields[e].type,e,null,this.fields[e])}),this.wait(!1)}),this.model.fetch()}hideField(e){this.$el.find('label[data-name="'+e+'"]').addClass("hide"),this.$el.find('div.field[data-name="'+e+'"]').addClass("hide");e=this.getView(e);e&&(e.disabled=!0)}showField(e){this.$el.find('label[data-name="'+e+'"]').removeClass("hide"),this.$el.find('div.field[data-name="'+e+'"]').removeClass("hide");e=this.getFieldView(e);e&&(e.disabled=!1)}getFieldView(e){return this.getView(e)}afterRender(){this.model.get("enabled")||this.dataFieldList.forEach(e=>{this.hideField(e)}),this.listenTo(this.model,"change:enabled",()=>{this.model.get("enabled")?this.dataFieldList.forEach(e=>this.showField(e)):this.dataFieldList.forEach(e=>this.hideField(e))})}createFieldView(e,t,a,s){e=this.model.getFieldParam(t,"view")||this.getFieldManager().getViewName(e);this.createView(t,e,{model:this.model,selector:`.field[data-name="${t}"]`,defs:{name:t,params:s},mode:a?"detail":"edit",readOnly:a}),this.fieldList.push(t)}save(){this.fieldList.forEach(e=>{e=this.getFieldView(e);e.readOnly||e.fetchToModel()});let t=!1;this.fieldList.forEach(e=>{e=this.getFieldView(e);e&&!e.disabled&&(t=e.validate()||t)}),t?Espo.Ui.error(this.translate("Not valid")):(this.listenToOnce(this.model,"sync",()=>{Espo.Ui.success(this.translate("Saved"))}),Espo.Ui.notify(this.translate("saving","messages")),this.model.save())}}e.default=i}),define("views/admin/field-manager/modals/view-details",["exports","views/modal","model","views/fields/varchar","views/admin/field-manager/detail-fields/attributes","views/record/detail","views/fields/bool"],function(e,t,a,s,i,l,o){function n(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=n(t),a=n(a),s=n(s),i=n(i),l=n(l),o=n(o);class d extends t.default{templateContent=`
        <div class="record-container no-side-margin">{{{record}}}</div>
    `;backdrop=!0;recordView;formModel;fieldType;fieldDefs;constructor(e){super(e),this.field=e.field,this.entityType=e.entityType}setup(){this.headerText=this.translate(this.entityType,"scopeNames")+" · "+this.translate(this.field,"fields",this.entityType),this.fieldDefs=this.getMetadata().get(`entityDefs.${this.entityType}.fields.`+this.field)||{},this.fieldType=this.fieldDefs.type,this.formModel=new a.default({name:this.field,attributes:this.getFieldAttributes(),readOnly:this.fieldDefs.readOnly||!1,type:this.fieldDefs.type}),this.recordView=new l.default({model:this.formModel,detailLayout:[{rows:[[{view:new s.default({name:"name",labelText:this.translate("name","fields")})},!1],[{view:new s.default({name:"type",labelText:this.translate("type","fields","Admin")})},!1],[{view:new o.default({name:"readOnly",labelText:this.translate("readOnly","fields","Admin")})},!1],[{view:new i.default({name:"attributes",labelText:this.translate("attributes","otherFields","FieldManager")})}]]}],readOnly:!0,sideView:null,isWide:!0,buttonsDisabled:!0}),this.assignView("record",this.recordView)}getFieldAttributes(){if("link"===this.fieldType||"linkOne"===this.fieldType||"file"===this.fieldType||"image"===this.fieldType)return[{name:this.field+"Id",type:"varchar",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Name",type:"varchar",notStorable:!0,readOnly:!0}];if("linkParent"===this.fieldType)return[{name:this.field+"Id",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Type",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Name",type:"string",notStorable:!0,readOnly:!0}];var e;if("linkMultiple"===this.fieldType||"attachmentMultiple"===this.fieldType)return e=[{name:this.field+"Ids",type:"string[]",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Names",type:"Record.<string, string>",notStorable:!0,readOnly:this.fieldDefs.readOnly||!0}],this.fieldDefs.columns&&e.push({name:this.field+"Columns",type:"Record.<string, Record.<string, string>>",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}),e;if("currency"===this.fieldType)return[{name:this.field,type:this.fieldDefs.decimal?"string":"float",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Currency",type:"string",notStorable:this.fieldDefs.notStorable||this.fieldDefs.onlyDefaultCurrency,readOnly:this.fieldDefs.readOnly||!1}];if("personName"===this.fieldType)return[{name:this.field,type:"string",notStorable:!0,readOnly:!0},{name:"salutation"+Espo.Utils.upperCaseFirst(this.field),type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:"first"+Espo.Utils.upperCaseFirst(this.field),type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:"last"+Espo.Utils.upperCaseFirst(this.field),type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:"middle"+Espo.Utils.upperCaseFirst(this.field),type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}];if("address"===this.fieldType)return[{name:this.field,type:"string",notStorable:!0,readOnly:!0},{name:this.field+"Street",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"City",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Country",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"State",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"PostalCode",type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}];if(["enum"].includes(this.fieldType)){let t="string";if(this.fieldDefs.options&&Array.isArray(this.fieldDefs.options)){let e=this.fieldDefs.options;this.fieldDefs.required&&(e=e.filter(e=>""!==e)),t=e.map(e=>""===e?"null":`"${e}"`).join("|")}return[{name:this.field,type:t,notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]}return["varchar","text","wysiwyg","colorpicker","barcode","url","enum","date","datetime","datetimeOptional"].includes(this.fieldType)?[{name:this.field,type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"bool"===this.fieldType?[{name:this.field,type:"boolean",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"int"===this.fieldType||"enumInt"===this.fieldType?[{name:this.field,type:"int",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"float"===this.fieldType||"enumFloat"===this.fieldType?[{name:this.field,type:"float",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"autoincrement"===this.fieldType?[{name:this.field,type:"int",notStorable:this.fieldDefs.notStorable||!1,readOnly:!0}]:"number"===this.fieldType?[{name:this.field,type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:!0}]:["multiEnum","checklist","array","urlMultiple"].includes(this.fieldType)?[{name:this.field,type:"string[]",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"email"===this.fieldType?[{name:this.field,type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Data",type:"{emailAddress: string, lower: string, primary: boolean, optOut: boolean, invalid: boolean}[]",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:"phone"===this.fieldType?[{name:this.field,type:"string",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1},{name:this.field+"Data",type:"{phoneNumber: string, primary: boolean, optOut: boolean, invalid: boolean}[]",notStorable:this.fieldDefs.notStorable||!1,readOnly:this.fieldDefs.readOnly||!1}]:void 0}}e.default=d}),define("views/admin/field-manager/fields/options",["exports","views/fields/array"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{maxItemLength=100;setup(){super.setup(),this.translatedOptions={},(this.model.get(this.name)||[]).forEach(e=>{this.translatedOptions[e]=this.getLanguage().translateOption(e,this.options.field,this.options.scope)}),this.model.fetchedAttributes.translatedOptions=this.translatedOptions}getItemHtml(e){var t=this.translatedOptions[e]||e;return $("<div>").addClass("list-group-item link-with-role form-inline").attr("data-value",e).append($("<div>").addClass("pull-left item-content").css("width","92%").css("display","inline-block").append($("<input>").attr("type","text").attr("data-name","translatedValue").attr("data-value",e).addClass("role form-control input-sm pull-right").attr("value",t).css("width","auto")).append($("<div>").addClass("item-text").text(e))).append($("<div>").css("width","8%").css("display","inline-block").css("vertical-align","top").append($("<a>").attr("role","button").attr("tabindex","0").addClass("pull-right").attr("data-value",e).attr("data-action","removeValue").append($("<span>").addClass("fas fa-times")))).append($("<br>").css("clear","both")).get(0).outerHTML}fetch(){let a=super.fetch();return a[this.name].length?(a.translatedOptions={},(a[this.name]||[]).forEach(e=>{var t=CSS.escape(e),t=this.$el.find(`input[data-name="translatedValue"][data-value="${t}"]`).val()||e;a.translatedOptions[e]=t.toString()})):(a[this.name]=null,a.translatedOptions={}),a}}e.default=s}),define("views/admin/entity-manager/record/edit-formula",["exports","views/record/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/entity-manager/record/edit-formula";data(){return{field:this.field,fieldKey:this.field+"Field"}}setup(){super.setup(),this.field=this.options.type;let e=null;"beforeSaveApiScript"===this.options.type&&(e=this.getRecordServiceFunctionDataList()),this.createField(this.field,"views/fields/formula",{targetEntityType:this.options.targetEntityType,height:504},"edit",!1,{additionalFunctionDataList:e})}getRecordServiceFunctionDataList(){return[{name:"recordService\\skipDuplicateCheck",insertText:"recordService\\skipDuplicateCheck()",returnType:"bool"},{name:"recordService\\throwDuplicateConflict",insertText:"recordService\\throwDuplicateConflict(RECORD_ID)"},{name:"recordService\\throwBadRequest",insertText:"recordService\\throwBadRequest(MESSAGE)"},{name:"recordService\\throwForbidden",insertText:"recordService\\throwForbidden(MESSAGE)"},{name:"recordService\\throwConflict",insertText:"recordService\\throwConflict(MESSAGE)"}]}}e.default=s}),define("views/admin/entity-manager/modals/export",["exports","views/modal","model","views/record/edit-for-modal","views/fields/varchar"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{templateContent=`
        <div class="record-container no-side-margin">{{{record}}}</div>
    `;setup(){this.headerText=this.translate("Export"),this.buttonList=[{name:"export",label:"Export",style:"danger",onClick:()=>this.export()},{name:"cancel",label:"Cancel"}];var e=this.getConfig().get("customExportManifest")||{};this.model=new a.default({name:e.name??null,module:e.module??null,version:e.version??"0.0.1",author:e.author??null,description:e.description??null}),this.recordView=new s.default({model:this.model,detailLayout:[{rows:[[{view:new i.default({name:"name",labelText:this.translate("name","fields"),params:{pattern:"$latinLettersDigitsWhitespace",required:!0}})},{view:new i.default({name:"module",labelText:this.translate("module","fields","EntityManager"),params:{pattern:"[A-Z][a-z][A-Za-z]+",required:!0}})}],[{view:new i.default({name:"version",labelText:this.translate("version","fields","EntityManager"),params:{pattern:"[0-9]+\\.[0-9]+\\.[0-9]+",required:!0}})},!1],[{view:new i.default({name:"author",labelText:this.translate("author","fields","EntityManager"),params:{required:!0}})},{view:new i.default({name:"description",labelText:this.translate("description","fields"),params:{}})}]]}]}),this.assignView("record",this.recordView)}export(){let t=this.recordView.fetch();this.recordView.validate()||(this.disableButton("export"),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("EntityManager/action/exportCustom",t).then(e=>{this.close(),this.getConfig().set("customExportManifest",t),Espo.Ui.success(this.translate("Done")),window.location=this.getBasePath()+"?entryPoint=download&id="+e.id}).catch(()=>this.enableButton("create")))}}e.default=o}),define("views/admin/entity-manager/fields/primary-filters",["exports","views/fields/array"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent=`
        {{#unless isEmpty}}
            <table class="table table-bordered">
                <tbody>
                    {{#each dateList}}
                        <tr>
                            <td style="width: 42%">{{name}}</td>
                            <td style="width: 42%">{{label}}</td>
                            <td style="width: 16%; text-align: center;">
                                <a
                                    role="button"
                                    data-action="copyToClipboard"
                                    data-name="{{name}}"
                                    class="text-soft"
                                    title="{{translate 'Copy to Clipboard'}}"
                                ><span class="far fa-copy"></span></a>
                            </td>
                        </tr>
                    {{/each}}
                </tbody>
            </table>
        {{else}}
            {{#if valueIsSet}}
                <span class="none-value">{{translate 'None'}}</span>
            {{else}}
                <span class="loading-value"></span>
            {{/if}}
        {{/unless}}
    `;data(){return{...super.data(),dateList:this.getValuesItems()}}constructor(e){super(e),this.targetEntityType=e.targetEntityType}getValuesItems(){return(this.model.get(this.name)||[]).map(e=>({name:e,label:this.translate(e,"presetFilters",this.targetEntityType)}))}setup(){super.setup(),this.addActionHandler("copyToClipboard",(e,t)=>this.copyToClipboard(t.dataset.name))}copyToClipboard(t){var e=`#${this.targetEntityType}/list/primaryFilter=`+t;navigator.clipboard.writeText(e).then(()=>{var e=this.translate("urlHashCopiedToClipboard","messages","EntityManager").replace("{name}",t);Espo.Ui.notify(e,"success",void 0,{closeButton:!0})})}}e.default=s}),define("views/admin/entity-manager/fields/acl-contact-link",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{targetEntityType="Contact";setupOptions(){let a=this.model.attributes.name,t=this.getMetadata().get(`entityDefs.${a}.links`)||{};var e=Object.keys(t).filter(e=>{e=t[e];return!("belongsToParent"!==e.type||!e.entityList||!e.entityList.includes(this.targetEntityType))||e.entity===this.targetEntityType}).sort((e,t)=>this.getLanguage().translate(e,"links",a).localeCompare(this.getLanguage().translate(t,"links",a)));e.unshift(""),this.translatedOptions=e.reduce((e,t)=>({[t]:this.translate(t,"links",a),...e}),{}),this.params.options=e}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-operator-only-date",["exports","views/admin/dynamic-logic/conditions-string/item-operator-only-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-operator-only-date";dateValue;data(){var e=super.data();return e.dateValue=this.dateValue,e}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/group-base",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/group-base";level;scope;number;operator;itemData;additionalData;viewList;viewDataList;data(){return this.conditionList.length?{viewDataList:this.viewDataList,operator:this.operator,level:this.level}:{isEmpty:!0}}setup(){this.level=this.options.level||0,this.number=this.options.number||0,this.scope=this.options.scope,this.operator=this.options.operator||this.operator,this.itemData=this.options.itemData||{},this.viewList=[];let s=this.conditionList=this.itemData.value||[];this.viewDataList=[],s.forEach((e,t)=>{var a=`view-${this.level.toString()}-${this.number.toString()}-`+t.toString();this.createItemView(t,a,e),this.viewDataList.push({key:a,isEnd:t===s.length-1})})}getFieldType(e){return this.getMetadata().get(["entityDefs",this.scope,"fields",e.attribute,"type"])||"base"}createItemView(t,a,s){this.viewList.push(a);var i=((s=s||{}).data||{}).type||s.type||"equals",l=this.getFieldType(s),l=this.getMetadata().get(["clientDefs","DynamicLogic","fieldTypes",l,"conditionTypes",i,"itemView"])||this.getMetadata().get(["clientDefs","DynamicLogic","itemTypes",i,"view"]);if(l){var o=this.getMetadata().get(["clientDefs","DynamicLogic","itemTypes",i,"operator"]);let e=this.getMetadata().get(["clientDefs","DynamicLogic","itemTypes",i,"operatorString"]);e=e||'<i class="small">'+(e=this.getLanguage().translateOption(i,"operators","DynamicLogic").toLowerCase())+"</i>",this.createView(a,l,{itemData:s,scope:this.scope,level:this.level+1,selector:`[data-view-key="${a}"]`,number:t,operator:o,operatorString:e})}}}e.default=s}),define("views/admin/dynamic-logic/conditions/group-base",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions/group-base";operator;viewDataList;viewList;itemData;data(){return{viewDataList:this.viewDataList,operator:this.operator,level:this.level,groupOperator:this.getGroupOperator()}}events={'click > div.group-head > [data-action="remove"]':function(e){e.stopPropagation(),this.trigger("remove-item")},'click > div.group-bottom [data-action="addField"]':function(){this.actionAddField()},'click > div.group-bottom [data-action="addAnd"]':function(){this.actionAddGroup("and")},'click > div.group-bottom [data-action="addOr"]':function(){this.actionAddGroup("or")},'click > div.group-bottom [data-action="addNot"]':function(){this.actionAddGroup("not")},'click > div.group-bottom [data-action="addCurrentUser"]':function(){this.addCurrentUser()},'click > div.group-bottom [data-action="addCurrentUserTeams"]':function(){this.addCurrentUserTeams()}};setup(){this.level=this.options.level||0,this.number=this.options.number||0,this.scope=this.options.scope,this.itemData=this.options.itemData||{},this.viewList=[];var e=this.conditionList=this.itemData.value||[];this.viewDataList=[],e.forEach((e,t)=>{var a=this.getKey(t);this.createItemView(t,a,e),this.addViewDataListItem(t,a)})}getGroupOperator(){return"or"===this.operator?"or":"and"}getKey(e){return`view-${this.level.toString()}-${this.number.toString()}-`+e.toString()}createItemView(t,e,a){this.viewList.push(e),this.isCurrentUser=a.attribute&&a.attribute.startsWith("$user.");var s=this.isCurrentUser?"User":this.scope,i=(a=a||{}).data||{},l=i.type||a.type||"equals",i=i.field||a.attribute;let o,n;["and","or","not"].includes(l)?o="views/admin/dynamic-logic/conditions/"+l:(n=this.getMetadata().get(["entityDefs",s,"fields",i,"type"]),"id"===i&&(n="id"),"$user.id"===a.attribute&&(n="currentUser"),(n="$user.teamsIds"===a.attribute?"currentUserTeams":n)&&(o=this.getMetadata().get(["clientDefs","DynamicLogic","fieldTypes",n,"view"]))),o=o||"views/admin/dynamic-logic/conditions/field-types/base",this.createView(e,o,{itemData:a,scope:s,level:this.level+1,selector:`[data-view-key="${e}"]`,number:t,type:l,field:i,fieldType:n},e=>{this.isRendered()&&e.render(),this.controlAddItemVisibility(),this.listenToOnce(e,"remove-item",()=>{this.removeItem(t)})})}fetch(){let t=[];return this.viewDataList.forEach(e=>{e=this.getView(e.key);t.push(e.fetch())}),{type:this.operator,value:t}}removeItem(a){var e=this.getKey(a);this.clearView(e),this.$el.find(`[data-view-key="${e}"]`).remove(),this.$el.find(`[data-view-ref-key="${e}"]`).remove();let s=-1;this.viewDataList.forEach((e,t)=>{e.index===a&&(s=t)}),~s&&this.viewDataList.splice(s,1),this.controlAddItemVisibility()}actionAddField(){this.createView("modal","views/admin/dynamic-logic/modals/add-field",{scope:this.scope},t=>{t.render(),this.listenToOnce(t,"add-field",e=>{this.addField(e),t.close()})})}addCurrentUser(){var e=this.getIndexForNewItem(),t=this.getKey(e);this.addItemContainer(e),this.addViewDataListItem(e,t),this.createItemView(e,t,{attribute:"$user.id",data:{type:"equals"}})}addCurrentUserTeams(){var e=this.getIndexForNewItem(),t=this.getKey(e);this.addItemContainer(e),this.addViewDataListItem(e,t),this.createItemView(e,t,{attribute:"$user.teamsIds",data:{type:"contains",field:"teams"}})}addField(e){let t=this.getMetadata().get(["entityDefs",this.scope,"fields",e,"type"]);if(t||"id"!==e||(t="id"),!this.getMetadata().get(["clientDefs","DynamicLogic","fieldTypes",t]))throw new Error;var a=this.getMetadata().get(["clientDefs","DynamicLogic","fieldTypes",t,"typeList"])[0],s=this.getIndexForNewItem(),i=this.getKey(s);this.addItemContainer(s),this.addViewDataListItem(s,i),this.createItemView(s,i,{data:{field:e,type:a}})}getIndexForNewItem(){return this.viewDataList.length?this.viewDataList[this.viewDataList.length-1].index+1:0}addViewDataListItem(e,t){this.viewDataList.push({index:e,key:t})}addItemContainer(e){var t=$(`<div data-view-key="${this.getKey(e)}"></div>`),t=(this.$el.find("> .item-list").append(t),this.translate(this.getGroupOperator(),"logicalOperators","Admin")),e=$(`<div class="group-operator" data-view-ref-key="${this.getKey(e)}">${t}</div>`);this.$el.find("> .item-list").append(e)}actionAddGroup(e){var t=this.getIndexForNewItem(),a=this.getKey(t),s=(this.addItemContainer(t),this.addViewDataListItem(t,a),"not"!==e?[]:void 0);this.createItemView(t,a,{type:e,value:s})}afterRender(){this.controlAddItemVisibility()}controlAddItemVisibility(){}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/link-multiple",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getValueFieldName(){return this.name}getValueViewName(){return"views/fields/link"}createValueViewContains(){this.createLinkValueField()}createValueViewNotContains(){this.createLinkValueField()}createLinkValueField(){this.createView("value","views/fields/link",{model:this.model,name:"link",selector:".value-container",mode:"edit",readOnlyDisabled:!0,foreignScope:this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"entity"])||this.getMetadata().get(["entityDefs",this.scope,"links",this.field,"entity"])},e=>{this.isRendered()&&e.render()})}fetch(){var e=this.getView("value"),t={type:this.type,attribute:this.field+"Ids",data:{field:this.field}};return e&&(e.fetchToModel(),t.value=this.model.get("linkId"),(e={}).linkName=this.model.get("linkName"),e.linkId=this.model.get("linkId"),t.data.values=e),t}}e.default=s}),define("views/email-account/fields/test-send",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{templateContent=`
        <button
            class="btn btn-default hidden"
            data-action="sendTestEmail"
        >{{translate 'Send Test Email' scope='Email'}}</button>
    `;setup(){super.setup(),this.addActionHandler("sendTestEmail",()=>this.send())}fetch(){return{}}checkAvailability(){this.model.get("smtpHost")?this.$el.find("button").removeClass("hidden"):this.$el.find("button").addClass("hidden")}afterRender(){this.checkAvailability(),this.stopListening(this.model,"change:smtpHost"),this.listenTo(this.model,"change:smtpHost",()=>{this.checkAvailability()})}enableButton(){this.$el.find("button").removeClass("disabled").removeAttr("disabled")}disabledButton(){this.$el.find("button").addClass("disabled").attr("disabled","disabled")}send(){let a=this.getSmtpData();this.createView("popup","views/outbound-email/modals/test-send",{emailAddress:this.getUser().get("emailAddress")}).then(t=>{t.render(),this.listenToOnce(t,"send",e=>{this.disabledButton(),a.emailAddress=e,Espo.Ui.notify(this.translate("Sending...")),t.close(),Espo.Ajax.postRequest("Email/sendTest",a).then(()=>{this.enableButton(),Espo.Ui.success(this.translate("testEmailSent","messages","Email"))}).catch(e=>{let t=e.getResponseHeader("X-Status-Reason")||"",a=(t=t.replace(/ $/,"").replace(/,$/,""),this.translate("Error"));if(200!==e.status&&(a+=" "+e.status),e.responseText)try{var s=JSON.parse(e.responseText);if(s.messageTranslation)return void this.enableButton();t=s.message||t}catch(e){return this.enableButton(),void console.error("Could not parse error response body.")}t&&(a+=": "+t),Espo.Ui.error(a,!0),console.error(a),e.errorIsHandled=!0,this.enableButton()})})})}getSmtpData(){return{server:this.model.get("smtpHost"),port:this.model.get("smtpPort"),auth:this.model.get("smtpAuth"),security:this.model.get("smtpSecurity"),username:this.model.get("smtpUsername"),password:this.model.get("smtpPassword")||null,authMechanism:this.model.get("smtpAuthMechanism"),fromName:this.getUser().get("name"),fromAddress:this.model.get("emailAddress"),type:"emailAccount",id:this.model.id,userId:this.model.get("assignedUserId")}}}e.default=s}),define("views/email-account/fields/test-connection",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{readOnly=!0;templateContent=`
        <button class="btn btn-default disabled" data-action="testConnection"
        >{{translate 'Test Connection' scope='EmailAccount'}}</button>
    `;url="EmailAccount/action/testConnection";setup(){super.setup(),this.addActionHandler("testConnection",()=>this.test())}fetch(){return{}}checkAvailability(){this.model.get("host")?this.$el.find("button").removeClass("disabled").removeAttr("disabled"):this.$el.find("button").addClass("disabled").attr("disabled","disabled")}afterRender(){this.checkAvailability(),this.stopListening(this.model,"change:host"),this.listenTo(this.model,"change:host",()=>{this.checkAvailability()})}getData(){return{host:this.model.get("host"),port:this.model.get("port"),security:this.model.get("security"),username:this.model.get("username"),password:this.model.get("password")||null,id:this.model.id,emailAddress:this.model.get("emailAddress"),userId:this.model.get("assignedUserId")}}test(){var e=this.getData();let s=this.$el.find("button");s.addClass("disabled"),Espo.Ui.notify(this.translate("pleaseWait","messages")),Espo.Ajax.postRequest(this.url,e).then(()=>{s.removeClass("disabled"),Espo.Ui.success(this.translate("connectionIsOk","messages","EmailAccount"))}).catch(e=>{let t=e.getResponseHeader("X-Status-Reason")||"",a=(t=(t=t.replace(/ $/,"")).replace(/,$/,""),this.translate("Error"));200!==parseInt(e.status)&&(a+=" "+e.status),t&&(a+=": "+t),Espo.Ui.error(a,!0),console.error(a),e.errorIsHandled=!0,s.removeClass("disabled")})}}e.default=s}),define("views/email-account/fields/folders",["exports","views/fields/array"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getFoldersUrl="EmailAccount/action/getFolders";setupOptions(){this.params.options=["INBOX"]}fetchFolders(){return new Promise(t=>{var e={host:this.model.get("host"),port:this.model.get("port"),security:this.model.get("security"),username:this.model.get("username"),emailAddress:this.model.get("emailAddress"),userId:this.model.get("assignedUserId")};this.model.has("password")&&(e.password=this.model.get("password")),this.model.isNew()||(e.id=this.model.id),Espo.Ajax.postRequest(this.getFoldersUrl,e).then(e=>{t(e)}).catch(e=>{Espo.Ui.error(this.translate("couldNotConnectToImap","messages","EmailAccount")),e.errorIsHandled=!0,t(["INBOX"])})})}actionAddItem(){Espo.Ui.notifyWait(),this.fetchFolders().then(e=>{Espo.Ui.notify(!1),this.createView("addModal",this.addItemModalView,{options:e}).then(t=>{t.render(),t.once("add",e=>{this.addValue(e),t.close()}),t.once("add-mass",e=>{e.forEach(e=>{this.addValue(e)}),t.close()})})})}}e.default=s}),define("views/email-account/fields/folder",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{editTemplate="email-account/fields/folder/edit";getFoldersUrl="EmailAccount/action/getFolders";setup(){super.setup(),this.addActionHandler("selectFolder",()=>{Espo.Ui.notify(this.translate("pleaseWait","messages"));var e={host:this.model.get("host"),port:this.model.get("port"),security:this.model.get("security"),username:this.model.get("username"),emailAddress:this.model.get("emailAddress"),userId:this.model.get("assignedUserId")};this.model.has("password")&&(e.password=this.model.get("password")),this.model.isNew()||(e.id=this.model.id),Espo.Ajax.postRequest(this.getFoldersUrl,e).then(e=>{this.createView("modal","views/email-account/modals/select-folder",{folders:e},t=>{Espo.Ui.notify(!1),t.render(),this.listenToOnce(t,"select",e=>{t.close(),this.addFolder(e)})})}).catch(e=>{Espo.Ui.error(this.translate("couldNotConnectToImap","messages","EmailAccount")),e.errorIsHandled=!0})})}addFolder(e){this.$element.val(e)}}e.default=s}),define("views/templates/event/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.getAcl().checkModel(this.model,"edit")&&-1===["Held","Not Held"].indexOf(this.model.get("status"))&&(this.dropdownItemList.push({html:this.translate("Set Held","labels",this.scope),name:"setHeld",onClick:()=>this.actionSetHeld()}),this.dropdownItemList.push({html:this.translate("Set Not Held","labels",this.scope),name:"setNotHeld",onClick:()=>this.actionSetNotHeld()}))}actionSetHeld(){this.model.save({status:"Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved","labels","Meeting")),this.removeActionItem("setHeld"),this.removeActionItem("setNotHeld")})}actionSetNotHeld(){this.model.save({status:"Not Held"},{patch:!0}).then(()=>{Espo.Ui.success(this.translate("Saved","labels","Meeting")),this.removeActionItem("setHeld"),this.removeActionItem("setNotHeld")})}}e.default=s}),define("views/settings/modals/tab-list-field-add",["exports","views/modals/array-field-add"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.options.noGroups||this.buttonList.push({name:"addGroup",text:this.translate("Group Tab","labels","Settings"),onClick:()=>this.actionAddGroup(),position:"right",iconClass:"fas fa-plus fa-sm"}),this.buttonList.push({name:"addDivider",text:this.translate("Divider","labels","Settings"),onClick:()=>this.actionAddDivider(),position:"right",iconClass:"fas fa-plus fa-sm"}),this.addButton({name:"addUrl",text:this.translate("URL","labels","Settings"),onClick:()=>this.actionAddUrl(),position:"right",iconClass:"fas fa-plus fa-sm"})}actionAddGroup(){this.trigger("add",{type:"group",text:this.translate("Group Tab","labels","Settings"),iconClass:null,color:null})}actionAddDivider(){this.trigger("add",{type:"divider",text:null})}actionAddUrl(){this.trigger("add",{type:"url",text:this.translate("URL","labels","Settings"),url:null,iconClass:null,color:null,aclScope:null,onlyAdmin:!1})}}e.default=s}),define("views/settings/modals/edit-tab-url",["exports","views/modal","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{className="dialog dialog-record";templateContent='<div class="record no-side-margin">{{{record}}}</div>';setup(){super.setup(),this.headerText=this.translate("URL","labels","Settings"),this.buttonList.push({name:"apply",label:"Apply",style:"danger"}),this.buttonList.push({name:"cancel",label:"Cancel"}),this.shortcutKeys={"Control+Enter":()=>this.actionApply()};var e=[{rows:[[{name:"url",labelText:this.translate("URL","labels","Settings"),view:"views/settings/fields/tab-url"}],[{name:"text",labelText:"Preferences"===this.options.parentType?this.translate("label","tabFields","Preferences"):this.translate("label","fields","Admin")},{name:"iconClass",labelText:"Preferences"===this.options.parentType?this.translate("iconClass","tabFields","Preferences"):this.translate("iconClass","fields","EntityManager")},{name:"color",labelText:"Preferences"===this.options.parentType?this.translate("color","tabFields","Preferences"):this.translate("color","fields","EntityManager")}],[{name:"aclScope",labelText:this.translate("aclScope","fields","Admin")},{name:"onlyAdmin",labelText:this.translate("onlyAdmin","fields","Admin")},!1]]}],t=this.model=new a.default;t.set(this.options.itemData),t.setDefs({fields:{text:{type:"varchar"},iconClass:{type:"base",view:"views/admin/entity-manager/fields/icon-class"},color:{type:"base",view:"views/fields/colorpicker"},url:{type:"url",required:!0,tooltip:"Admin.tabUrl"},aclScope:{type:"enum",translation:"Global.scopeNames",options:["",...this.getAclScopes()],tooltip:"Admin.tabUrlAclScope"},onlyAdmin:{type:"bool"}}}),this.createView("record","views/record/edit-for-modal",{detailLayout:e,model:t,selector:".record"}).then(e=>{"Preferences"===this.options.parentType&&(e.hideField("aclScope"),e.hideField("onlyAdmin"))})}actionApply(){var e=this.getView("record");e.validate()||(e=e.fetch(),this.trigger("apply",e))}getAclScopes(){return this.getMetadata().getScopeList().filter(e=>this.getMetadata().get(`scopes.${e}.acl`))}}e.default=i}),define("views/settings/modals/edit-tab-group",["exports","views/modal","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{className="dialog dialog-record";templateContent='<div class="record no-side-margin">{{{record}}}</div>';setup(){super.setup(),this.headerText=this.translate("Group Tab","labels","Settings"),this.buttonList.push({name:"apply",label:"Apply",style:"danger"}),this.buttonList.push({name:"cancel",label:"Cancel"}),this.shortcutKeys={"Control+Enter":()=>this.actionApply()};var e=[{rows:[[{name:"text",labelText:"Preferences"===this.options.parentType?this.translate("label","tabFields","Preferences"):this.translate("label","fields","Admin")},{name:"iconClass",labelText:"Preferences"===this.options.parentType?this.translate("iconClass","tabFields","Preferences"):this.translate("iconClass","fields","EntityManager")},{name:"color",labelText:"Preferences"===this.options.parentType?this.translate("color","tabFields","Preferences"):this.translate("color","fields","EntityManager")}],[{name:"itemList",labelText:"Preferences"===this.options.parentType?this.translate("tabList","fields","Preferences"):this.translate("tabList","fields","Settings")},!1]]}],t=this.model=new a.default;t.name="GroupTab",t.set(this.options.itemData),t.setDefs({fields:{text:{type:"varchar"},iconClass:{type:"base",view:"views/admin/entity-manager/fields/icon-class"},color:{type:"base",view:"views/fields/colorpicker"},itemList:{type:"array",view:"views/settings/fields/group-tab-list"}}}),this.createView("record","views/record/edit-for-modal",{detailLayout:e,model:t,selector:".record"})}actionApply(){var e=this.getView("record");e.validate()||(e=e.fetch(),this.trigger("apply",e))}}e.default=i}),define("views/settings/modals/edit-tab-divider",["exports","views/modal","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{className="dialog dialog-record";templateContent='<div class="record no-side-margin">{{{record}}}</div>';setup(){super.setup(),this.headerText=this.translate("Divider","labels","Settings"),this.buttonList.push({name:"apply",label:"Apply",style:"danger"}),this.buttonList.push({name:"cancel",label:"Cancel"}),this.shortcutKeys={"Control+Enter":()=>this.actionApply()};var e=[{rows:[[{name:"text",labelText:"Preferences"===this.options.parentType?this.translate("label","tabFields","Preferences"):this.translate("label","fields","Admin")},!1]]}],t=this.model=new a.default({},{entityType:"Dummy"});t.set(this.options.itemData),t.setDefs({fields:{text:{type:"varchar"}}}),this.createView("record","views/record/edit-for-modal",{detailLayout:e,model:t,selector:".record"})}actionApply(){var e=this.getView("record");e.validate()||(e=e.fetch(),this.trigger("apply",e))}}e.default=i}),define("views/settings/fields/time-format",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=this.getMetadata().get(["app","dateTime","timeFormatList"])||[]}}e.default=s}),define("views/settings/fields/thousand-separator",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{validations=["required","thousandSeparator"];validateThousandSeparator(){var e;if(this.model.get("thousandSeparator")===this.model.get("decimalMark"))return e=this.translate("thousandSeparatorEqualsDecimalMark","messages","Admin"),this.showValidationMessage(e),!0}fetch(){var e={},t=this.$element.val();return e[this.name]=t||"",e}}e.default=s}),define("views/settings/fields/tab-url",["exports","views/fields/url"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{optionalProtocol=!1;validateValid(){var e=this.model.get(this.name);return(!e||!e.startsWith("#"))&&super.validateValid()}}e.default=s}),define("views/settings/fields/stream-email-with-content-entity-type-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{e=this.getMetadata().get("scopes."+e)||{};return e.entity&&e.stream})}}e.default=s}),define("views/settings/fields/stream-email-notifications-entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if(!this.getMetadata().get(`scopes.${e}.disabled`)&&this.getMetadata().get(`scopes.${e}.object`)&&this.getMetadata().get(`scopes.${e}.stream`))return!0})}}e.default=s}),define("views/settings/fields/sms-provider",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetchEmptyValueAsNull=!0;setupOptions(){this.params.options=Object.keys(this.getMetadata().get(["app","smsProviders"])||{}),this.params.options.unshift("")}}e.default=s}),define("views/settings/fields/phone-number-preferred-country-list",["exports","views/fields/multi-enum","intl-tel-input-globals"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{setupOptions(){try{var e=a.default.getCountryData();this.params.options=e.map(e=>e.iso2),this.translatedOptions=e.reduce((e,t)=>(e[t.iso2]=t.iso2.toUpperCase()+" +"+t.dialCode,e),{})}catch(e){console.error(e)}}}e.default=i}),define("views/settings/fields/pdf-engine",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=Object.keys(this.getMetadata().get(["app","pdfEngines"])),0===this.params.options.length&&(this.params.options=[""])}}e.default=s}),define("views/settings/fields/outbound-email-from-address",["exports","views/fields/email-address"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{useAutocompleteUrl=!0;getAutocompleteUrl(e){return"InboundEmail?searchParams="+JSON.stringify({select:["emailAddress"],maxSize:7,where:[{type:"startsWith",attribute:"emailAddress",value:e},{type:"isTrue",attribute:"useSmtp"}]})}transformAutocompleteResult(e){e=super.transformAutocompleteResult(e);return e.forEach(e=>{e.value=e.attributes.emailAddress}),e}}e.default=s}),define("views/settings/fields/oidc-teams",["exports","views/fields/link-multiple-with-role"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{forceRoles=!0;roleType="varchar";columnName="group";roleMaxLength=255;setup(){super.setup(),this.rolePlaceholderText=this.translate("IdP Group","labels","Settings")}}e.default=s}),define("views/settings/fields/oidc-redirect-uri",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent=`
        {{#if isNotEmpty}}
            <a
                role="button"
                data-action="copyToClipboard"
                class="pull-right text-soft"
                title="{{translate 'Copy to Clipboard'}}"
            ><span class="far fa-copy"></span></a>
            {{value}}
        {{else}}
            <span class="none-value">{{translate 'None'}}</span>
        {{/if}}
    `;portalCollection=null;data(){var e="AuthenticationProvider"!==this.model.entityType||this.portalCollection;return{value:this.getValueForDisplay(),isNotEmpty:e}}copyToClipboard(){var e=this.getValueForDisplay();navigator.clipboard.writeText(e).then(()=>{Espo.Ui.success(this.translate("Copied to clipboard"))})}getValueForDisplay(){return"AuthenticationProvider"===this.model.entityType?this.portalCollection?this.portalCollection.models.map(e=>{var t="oauth-callback.php",a=(e.get("url")||"").replace(/\/+$/,"")+"/"+t,e=`/portal/${e.id}/`+t;return a.endsWith(e)?a.slice(0,-e.length)+"/portal/"+t:a}).join("\n"):null:(this.getConfig().get("siteUrl")||"").replace(/\/+$/,"")+"/oauth-callback.php"}setup(){super.setup(),"AuthenticationProvider"===this.model.entityType&&this.getCollectionFactory().create("Portal").then(e=>{e.data.select=["url","isDefault"].join(","),e.fetch().then(()=>{this.portalCollection=e,this.reRender()})})}}e.default=s}),define("views/settings/fields/language",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=Espo.Utils.clone(this.getMetadata().get(["app","language","list"])||[]),this.translatedOptions=Espo.Utils.clone(this.getLanguage().translate("language","options")||{})}}e.default=s}),define("views/settings/fields/history-entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if(!this.getMetadata().get("scopes."+e+".disabled")&&this.getMetadata().get("scopes."+e+".object")&&this.getMetadata().get("scopes."+e+".activity"))return!0})}}e.default=s}),define("views/settings/fields/group-tab-list",["exports","views/settings/fields/tab-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{noGroups=!0;noDelimiters=!0}e.default=s}),define("views/settings/fields/global-search-entity-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.options=Object.keys(this.getMetadata().get("scopes")).filter(e=>{var t=this.getMetadata().get(["scopes",e])||{};if(!t.disabled&&"Note"!==e)return t.customizable&&t.entity}).sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),super.setup()}}e.default=s}),define("views/settings/fields/fiscal-year-shift",["exports","views/fields/enum-int"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=[],this.translatedOptions={},(this.getLanguage().get("Global","lists","monthNames")||[]).forEach((e,t)=>{this.params.options.push(t),this.translatedOptions[t]=e})}}e.default=s}),define("views/settings/fields/email-address-lookup-entity-type-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if(!this.getMetadata().get(["scopes",e,"disabled"])&&this.getMetadata().get(["scopes",e,"object"]))return!!["User","Contact","Lead","Account"].includes(e)||"Company"===(e=this.getMetadata().get(["scopes",e,"type"]))||"Person"===e||void 0})}}e.default=s}),define("views/settings/fields/default-currency",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.validations.push(()=>this.validateExisting())}setupOptions(){this.params.options=Espo.Utils.clone(this.getConfig().get("currencyList")||[])}validateExisting(){var e,t=this.model.get("currencyList");return!!t&&(e=this.model.get(this.name),!t.includes(e))&&(t=this.translate("fieldInvalid","messages").replace("{field}",this.getLabelText()),this.showValidationMessage(t),!0)}}e.default=s}),define("views/settings/fields/date-format",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=this.getMetadata().get(["app","dateTime","dateFormatList"])||[]}}e.default=s}),define("views/settings/fields/dashboard-layout",["exports","views/fields/base","gridstack"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{detailTemplate="settings/fields/dashboard-layout/detail";editTemplate="settings/fields/dashboard-layout/edit";validationElementSelector='button[data-action="addDashlet"]';WIDTH_MULTIPLIER=3;HEIGHT_MULTIPLIER=4;data(){return{dashboardLayout:this.dashboardLayout,currentTab:this.currentTab,isEmpty:this.isEmpty()}}hasLocked(){return"Preferences"===this.model.entityType}setup(){this.addActionHandler("selectTab",(e,t)=>{t=parseInt(t.dataset.tab);this.selectTab(t)}),this.addActionHandler("removeDashlet",(e,t)=>{t=t.dataset.id;this.removeDashlet(t)}),this.addActionHandler("editDashlet",(e,t)=>{var a=t.dataset.id,t=t.dataset.name;this.editDashlet(a,t)}),this.addActionHandler("editTabs",()=>this.editTabs()),this.addActionHandler("addDashlet",()=>{this.createView("addDashlet","views/modals/add-dashlet",{parentType:this.model.entityType},e=>{e.render(),this.listenToOnce(e,"add",e=>this.addDashlet(e))})}),this.dashboardLayout=Espo.Utils.cloneDeep(this.model.get(this.name)||[]),this.dashletsOptions=Espo.Utils.cloneDeep(this.model.get("dashletsOptions")||{}),this.hasLocked()&&(this.dashboardLocked=this.model.get("dashboardLocked")||!1),this.listenTo(this.model,"change",()=>{this.model.hasChanged(this.name)&&(this.dashboardLayout=Espo.Utils.cloneDeep(this.model.get(this.name)||[])),this.model.hasChanged("dashletsOptions")&&(this.dashletsOptions=Espo.Utils.cloneDeep(this.model.get("dashletsOptions")||{})),this.model.hasChanged(this.name)&&this.dashboardLayout.length&&this.isDetailMode()&&this.selectTab(0),this.hasLocked()&&(this.dashboardLocked=this.model.get("dashboardLocked")||!1)}),this.currentTab=-1,this.currentTabLayout=null,this.dashboardLayout.length&&this.selectTab(0)}selectTab(e){this.currentTab=e,this.setupCurrentTabLayout(),this.isRendered()&&this.reRender().then(()=>{this.$el.find(`[data-action="selectTab"][data-tab="${e}"]`).focus()})}setupCurrentTabLayout(){~this.currentTab||(this.currentTabLayout=null);var e=this.dashboardLayout[this.currentTab].layout||[],e=a.default.Utils.sort(e);this.currentTabLayout=e}addDashletHtml(e,t){e=this.prepareGridstackItem(e,t);this.grid.addWidget(e.get(0),{x:0,y:0,w:2*this.WIDTH_MULTIPLIER,h:2*this.HEIGHT_MULTIPLIER})}generateId(){return Math.floor(10000001*Math.random()).toString()}addDashlet(e){let t="d"+Math.floor(1000001*Math.random()).toString();~this.currentTab?(this.addDashletHtml(t,e),this.fetchLayout()):(this.dashboardLayout.push({name:"My Espo",layout:[],id:this.generateId()}),this.currentTab=0,this.setupCurrentTabLayout(),this.once("after:render",()=>{setTimeout(()=>{this.addDashletHtml(t,e),this.fetchLayout()},50)}),this.reRender())}removeDashlet(a){var e=this.$gridstack.find('.grid-stack-item[data-id="'+a+'"]');this.grid.removeWidget(e.get(0),!0);let s=this.dashboardLayout[this.currentTab].layout;s.forEach((e,t)=>{e.id===a&&s.splice(t,1)}),delete this.dashletsOptions[a],this.setupCurrentTabLayout()}editTabs(){var e={dashboardLayout:this.dashboardLayout,tabListIsNotRequired:!0};this.hasLocked()&&(e.dashboardLocked=this.dashboardLocked),this.createView("editTabs","views/modals/edit-dashboard",e,t=>{t.render(),this.listenToOnce(t,"after:save",e=>{t.close();let i=[];e.dashboardTabList.forEach(t=>{let a=[],s=this.generateId();this.dashboardLayout.forEach(e=>{e.name===t&&(a=e.layout,s=e.id)}),t in e.renameMap&&(t=e.renameMap[t]),i.push({name:t,layout:a,id:s})}),this.dashboardLayout=i,this.hasLocked()&&(this.dashboardLocked=e.dashboardLocked),this.selectTab(0),this.deleteNotExistingDashletsOptions()})})}deleteNotExistingDashletsOptions(){let t=[];(this.dashboardLayout||[]).forEach(e=>{(e.layout||[]).forEach(e=>{t.push(e.id)})}),Object.keys(this.dashletsOptions).forEach(e=>{~t.indexOf(e)||delete this.dashletsOptions[e]})}editDashlet(a,s){let t=this.dashletsOptions[a]||{},i=(t=Espo.Utils.cloneDeep(t),this.getMetadata().get(["dashlets",s,"options","defaults"])||{});Object.keys(i).forEach(e=>{e in t||(t[e]=Espo.Utils.cloneDeep(i[e]))}),"title"in t||(t.title=this.translate(s,"dashlets"));var e=this.getMetadata().get(["dashlets",s,"options","view"])||"views/dashlets/options/base";this.createView("options",e,{name:s,optionsData:t,fields:this.getMetadata().get(["dashlets",s,"options","fields"])||{},userId:"Preferences"===this.model.entityType?this.model.id:null},e=>{e.render(),this.listenToOnce(e,"save",t=>{if(this.dashletsOptions[a]=t,e.close(),"title"in t){let e=t.title;e=e||this.translate(s,"dashlets"),this.$el.find('[data-id="'+a+'"] .panel-title').text(e)}})})}fetchLayout(){~this.currentTab&&(this.dashboardLayout[this.currentTab].layout=_.map(this.$gridstack.find(".grid-stack-item"),e=>{var e=$(e),t=e.attr("gs-x"),a=e.attr("gs-y"),s=e.attr("gs-h"),i=e.attr("gs-w");return{id:e.data("id"),name:e.data("name"),x:t/this.WIDTH_MULTIPLIER,y:a/this.HEIGHT_MULTIPLIER,width:i/this.WIDTH_MULTIPLIER,height:s/this.HEIGHT_MULTIPLIER}}),this.setupCurrentTabLayout())}afterRender(){var e;this.currentTabLayout&&(e=this.$gridstack=this.$el.find("> .grid-stack"),(this.grid=a.default.init({minWidth:4,cellHeight:20,margin:10,column:12,resizable:{handles:"se",helper:!1},disableOneColumnMode:!0,animate:!1,staticGrid:"edit"!==this.mode,disableResize:"edit"!==this.mode,disableDrag:"edit"!==this.mode})).removeAll(),this.currentTabLayout.forEach(e=>{var t=this.prepareGridstackItem(e.id,e.name);this.grid.addWidget(t.get(0),{x:e.x*this.WIDTH_MULTIPLIER,y:e.y*this.HEIGHT_MULTIPLIER,w:e.width*this.WIDTH_MULTIPLIER,h:e.height*this.HEIGHT_MULTIPLIER})}),e.find(" .grid-stack-item").css("position","absolute"),e.on("change",()=>{this.fetchLayout(),this.trigger("change")}))}prepareGridstackItem(e,t){var a=$("<div>").addClass("grid-stack-item");let s="",i=(this.isEditMode()&&(s=(s+=$("<div>").addClass("btn-group pull-right").append($("<button>").addClass("btn btn-default").attr("data-action","removeDashlet").attr("data-id",e).attr("title",this.translate("Remove")).append($("<span>").addClass("fas fa-times"))).get(0).outerHTML)+$("<div>").addClass("btn-group pull-right").append($("<button>").addClass("btn btn-default").attr("data-action","editDashlet").attr("data-id",e).attr("data-name",t).attr("title",this.translate("Edit")).append($("<span>").addClass("fas fa-pencil-alt fa-sm").css({position:"relative",top:"-1px"}))).get(0).outerHTML),this.getOption(e,"title"));i=i||this.translate(t,"dashlets");var l=$("<div>").addClass("panel-heading").append(s).append($("<h4>").addClass("panel-title").text(i)).get(0).outerHTML,l=$("<div>").addClass("grid-stack-item-content panel panel-default").append(l);return l.attr("data-id",e),l.attr("data-name",t),a.attr("data-id",e),a.attr("data-name",t),a.append(l),a}getOption(e,t){return((this.model.get("dashletsOptions")||{})[e]||{})[t]}isEmpty(){let t=!0;return this.dashboardLayout&&this.dashboardLayout.length&&this.dashboardLayout.forEach(e=>{e.layout&&e.layout.length&&(t=!1)}),t}validateRequired(){var e;return this.isRequired()&&this.isEmpty()?(e=this.translate("fieldIsRequired","messages").replace("{field}",this.getLabelText()),this.showValidationMessage(e),!0):void 0}fetch(){var e={};return this.dashboardLayout&&this.dashboardLayout.length?(e[this.name]=Espo.Utils.cloneDeep(this.dashboardLayout),e.dashletsOptions=Espo.Utils.cloneDeep(this.dashletsOptions),this.hasLocked()&&(e.dashboardLocked=this.dashboardLocked)):(e[this.name]=null,e.dashletsOptions={}),e}}e.default=i}),define("views/settings/fields/currency-rates",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{editTemplate="settings/fields/currency-rates/edit";data(){let t=this.model.get("baseCurrency"),a=this.model.get("currencyRates")||{},s={};return(this.model.get("currencyList")||[]).forEach(e=>{e!==t&&(s[e]=a[e],s[e]||(a[t]&&(s[e]=Math.round(1/a[t]*1e3)/1e3),s[e])||(s[e]=1))}),{rateValues:s,baseCurrency:t}}fetch(){var e={};let a={},s=this.model.get("baseCurrency");var t,i=this.model.get("currencyList")||[];for(t in i.forEach(e=>{var t;e!==s&&(t=this.$el.find(`input[data-currency="${e}"]`).val()||"1",a[e]=parseFloat(t))}),delete a[s],a)~i.indexOf(t)||delete a[t];return e[this.name]=a,e}}e.default=s}),define("views/settings/fields/currency-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{matchAnyWord=!0;setupOptions(){this.params.options=this.getMetadata().get(["app","currency","list"])||[],this.translatedOptions={},this.params.options.forEach(e=>{let t=e;var a=this.getLanguage().get("Currency","names",e);a&&(t+=" - "+a),this.translatedOptions[e]=t})}}e.default=s}),define("views/settings/fields/calendar-entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if(!this.getMetadata().get(`scopes.${e}.disabled`)&&this.getMetadata().get(`scopes.${e}.object`)&&this.getMetadata().get(`scopes.${e}.calendar`))return!0})}}e.default=s}),define("views/settings/fields/busy-ranges-entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if(!this.getMetadata().get(["scopes",e,"disabled"])&&this.getMetadata().get(["scopes",e,"object"])&&this.getMetadata().get(["scopes",e,"calendar"]))return!0})}}e.default=s}),define("views/settings/fields/available-reactions",["exports","views/fields/array","helpers/misc/reactions"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{iconClassMap;reactionsHelper;setup(){this.reactionsHelper=new a.default,this.iconClassMap=this.reactionsHelper.getDefinitionList().reduce((e,t)=>({[t.type]:t.iconClass,...e}),{}),super.setup()}setupOptions(){var e=this.reactionsHelper.getDefinitionList();this.params.options=e.map(e=>e.type),this.translatedOptions=e.reduce((e,t)=>({[t.type]:this.translate(t.type,"reactions"),...e}),{})}getItemHtml(e){var t=super.getItemHtml(e),t=(new DOMParser).parseFromString(t,"text/html").body.childNodes[0],e=this.createIconElement(e);return t.prepend(e),t.outerHTML}createIconElement(e){let t=document.createElement("span");return(this.iconClassMap[e]||"").split(" ").filter(e=>e).forEach(e=>t.classList.add(e)),t.classList.add("text-soft"),t.style.display="inline-block",t.style.width="var(--24px)",t}async actionAddItem(){let e=await super.actionAddItem();return e.whenRendered().then(()=>{e.element.querySelectorAll("a[data-value]").forEach(e=>{var t=this.createIconElement(e.dataset.value);e.prepend(t)})}),e}}e.default=i}),define("views/settings/fields/authentication-method",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=[];var e,t=this.getMetadata().get(["authenticationMethods"])||{};for(e in t)t[e].settings&&t[e].settings.isAvailable&&this.params.options.push(e)}}e.default=s}),define("views/settings/fields/auth-two-fa-method-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=[];var e,t=this.getMetadata().get(["app","authentication2FAMethods"])||{};for(e in t)t[e].settings&&t[e].settings.isAvailable&&this.params.options.push(e)}}e.default=s}),define("views/settings/fields/assignment-notifications-entity-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.options=Object.keys(this.getMetadata().get("scopes")).filter(e=>{if(!this.getMetadata().get(`scopes.${e}.disabled`)&&(!this.getMetadata().get(`scopes.${e}.stream`)||this.getMetadata().get(`notificationDefs.${e}.forceAssignmentNotificator`)))return this.getMetadata().get(`scopes.${e}.notifications`)&&this.getMetadata().get(`scopes.${e}.entity`)}).sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),super.setup()}}e.default=s}),define("views/settings/fields/assignment-email-notifications-entity-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.options=Object.keys(this.getMetadata().get("scopes")).filter(e=>{if("Email"!==e&&!this.getMetadata().get(`scopes.${e}.disabled`))return this.getMetadata().get(`scopes.${e}.notifications`)&&this.getMetadata().get(`scopes.${e}.entity`)}).sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),super.setup()}}e.default=s}),define("views/settings/fields/address-preview",["exports","views/fields/address"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup();let e=this.model,t=e.clone();t.entityType=e.entityType,t.name=e.name,t.set({addressPreviewStreet:"Street",addressPreviewPostalCode:"PostalCode",addressPreviewCity:"City",addressPreviewState:"State",addressPreviewCountry:"Country"}),this.listenTo(e,"change:addressFormat",()=>{t.set("addressFormat",e.get("addressFormat")),this.reRender()}),this.model=t}getAddressFormat(){return this.model.get("addressFormat")||1}}e.default=s}),define("views/settings/fields/activities-entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>{if("Email"!==e&&!this.getMetadata().get(`scopes.${e}.disabled`)&&this.getMetadata().get(`scopes.${e}.object`)&&this.getMetadata().get(`scopes.${e}.activity`))return!0})}}e.default=s}),define("views/scheduled-job/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{searchPanel=!1;setup(){super.setup(),this.addMenuItem("buttons",{link:"#Admin/jobs",text:this.translate("Jobs","labels","Admin")}),this.createView("search","views/base",{fullSelector:"#main > .search-container",template:"scheduled-job/cronjob"})}afterRender(){super.afterRender(),Espo.Ajax.getRequest("Admin/action/cronMessage").then(e=>{this.$el.find(".cronjob .message").html(e.message),this.$el.find(".cronjob .command").html("<strong>"+e.command+"</strong>")})}getHeader(){return this.buildHeaderHtml([$("<a>").attr("href","#Admin").text(this.translate("Administration","labels","Admin")),this.getLanguage().translate(this.scope,"scopeNamesPlural")])}}e.default=s}),define("views/scheduled-job/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{quickDetailDisabled=!0;quickEditDisabled=!0;massActionList=["remove","massUpdate"]}e.default=s}),define("views/scheduled-job/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{duplicateAction=!1}e.default=s}),define("views/scheduled-job/record/panels/log",["exports","views/record/panels/relationship"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupListLayout(){~(this.getMetadata().get(["clientDefs","ScheduledJob","jobWithTargetList"])||[]).indexOf(this.model.get("job"))&&(this.listLayoutName="listSmallWithTarget")}}e.default=s}),define("views/scheduled-job/fields/scheduling",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),(this.isEditMode()||this.isDetailMode())&&this.wait(Espo.loader.requirePromise("lib!cronstrue").then(e=>{this.Cronstrue=e,this.listenTo(this.model,"change:"+this.name,()=>this.showText())}))}afterRender(){var e;super.afterRender(),(this.isEditMode()||this.isDetailMode())&&(e=this.$text=$('<div class="small text-success"/>'),this.$el.append(e),this.showText())}showText(){let t;if(this.$text&&this.$text.length&&this.Cronstrue){var a=this.model.get(this.name);if(a)if("* * * * *"===a)this.$text.text(this.translate("As often as possible","labels","ScheduledJob"));else{let e="en";var s=Object.keys(this.Cronstrue.default.locales),i=this.getLanguage().name;~s.indexOf(i)?e=i:~s.indexOf(i.split("_")[0])&&(e=i.split("_")[0]);try{t=this.Cronstrue.toString(a,{use24HourTimeFormat:!this.getDateTime().hasMeridian(),locale:e})}catch(e){t=this.translate("Not valid")}this.$text.text(t)}else this.$text.text("")}}}e.default=s}),define("views/scheduled-job/fields/job",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),(this.isEditMode()||this.isDetailMode())&&(this.wait(!0),Espo.Ajax.getRequest("Admin/jobs").then(e=>{this.params.options=e.filter(e=>!this.getMetadata().get(["entityDefs","ScheduledJob","jobs",e,"isSystem"])),this.params.options.unshift(""),this.wait(!1)})),this.model.isNew()&&this.on("change",()=>{var e,t=this.model.get("job");t?(e=this.getLanguage().translateOption(t,"job","ScheduledJob"),t=this.getMetadata().get("entityDefs.ScheduledJob.jobSchedulingMap."+t)||"*/10 * * * *",this.model.set("name",e),this.model.set("scheduling",t)):(this.model.set("name",""),this.model.set("scheduling",""))})}}e.default=s}),define("views/role/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{searchPanel=!1}e.default=s}),define("views/role/record/detail-side",["exports","views/record/detail-side"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{panelList=[{name:"default",label:!1,view:"views/role/record/panels/side"}]}e.default=s}),define("views/role/record/panels/side",["exports","views/record/panels/side"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="role/record/panels/side"}e.default=s}),define("views/role/modals/add-field",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="role/modals/add-field";backdrop=!0;events={'click a[data-action="addField"]':function(e){this.trigger("add-fields",[$(e.currentTarget).data().name])}};data(){return{dataList:this.dataList,scope:this.scope}}setup(){this.addHandler("keyup",'input[data-name="quick-search"]',(e,t)=>{this.processQuickSearch(t.value)}),this.addHandler("click",'input[type="checkbox"]',(e,t)=>{var a=t.dataset.name;t.checked?this.checkedList.push(a):-1!==(t=this.checkedList.indexOf(a))&&this.checkedList.splice(t,1),this.checkedList.length?this.enableButton("select"):this.disableButton("select")}),this.buttonList=[{name:"select",label:"Select",style:"danger",disabled:!0,onClick:()=>{this.trigger("add-fields",this.checkedList)}},{name:"cancel",label:"Cancel",onClick:()=>this.actionCancel()}],this.checkedList=[];let t=this.scope=this.options.scope;this.headerText=this.translate(t,"scopeNamesPlural")+" · "+this.translate("Add Field");var e=this.getMetadata().get(`entityDefs.${t}.fields`)||{};let a=[],s=this.options.ignoreFieldList||[];Object.keys(e).filter(e=>!s.includes(e)).forEach(e=>{this.getFieldManager().isEntityTypeFieldAvailable(t,e)&&null==this.getMetadata().get(["app",this.options.type,"mandatory","scopeFieldLevel",this.scope,e])&&a.push(e)}),this.fieldList=this.getLanguage().sortFieldList(t,a),this.dataList=this.fieldList.map(e=>({name:e,label:this.translate(e,"fields",this.scope)}))}afterRender(){this.$table=this.$el.find("table.fields-table"),setTimeout(()=>{this.element.querySelector('input[data-name="quick-search"]').focus()},0)}processQuickSearch(e){if(e=e.trim()){let i=[],l=e.toLowerCase();this.dataList.forEach(e=>{let t=!1;var a=e.name,s=e.label;(t=0!==s.indexOf(l)&&0!==a.toLowerCase().indexOf(l)?t:!0)||s.split(" ").concat(s.split(" ")).forEach(e=>{0===e.toLowerCase().indexOf(l)&&(t=!0)}),t&&i.push(e)}),0===i.length?this.$table.find("tr").addClass("hidden"):this.dataList.forEach(e=>{var t=this.$table.find(`tr[data-name="${e.name}"]`);i.includes(e)?t.removeClass("hidden"):t.addClass("hidden")})}else this.$table.find("tr").removeClass("hidden")}}e.default=s}),define("views/role/fields/permission",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.style={yes:"success",all:"success",account:"info",contact:"info",team:"info",own:"warning",no:"danger",enabled:"success",disabled:"danger","not-set":"default"},super.setup()}}e.default=s}),define("views/portal-role/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{searchPanel=!1}e.default=s}),define("views/portal-role/record/table",["exports","views/role/record/table"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{levelListMap={recordAllAccountContactOwnNo:["all","account","contact","own","no"],recordAllAccountOwnNo:["all","account","own","no"],recordAllContactOwnNo:["all","contact","own","no"],recordAllAccountNo:["all","account","no"],recordAllContactNo:["all","contact","no"],recordAllAccountContactNo:["all","account","contact","no"],recordAllOwnNo:["all","own","no"],recordAllNo:["all","no"],record:["all","own","no"]};levelList=["all","account","contact","own","no"];type="aclPortal";lowestLevelByDefault=!0;setupScopeList(){this.aclTypeMap={},this.scopeList=[],this.getSortedScopeList().forEach(e=>{var t;this.getMetadata().get(`scopes.${e}.disabled`)||this.getMetadata().get(`scopes.${e}.disabledPortal`)||(t=this.getMetadata().get(`scopes.${e}.aclPortal`))&&(this.scopeList.push(e),!0===(this.aclTypeMap[e]=t))&&(this.aclTypeMap[e]="record")})}isAclFieldLevelDisabledForScope(e){return!!this.getMetadata().get(`scopes.${e}.aclPortalFieldLevelDisabled`)}}e.default=s}),define("views/portal-role/record/list",["exports","views/role/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/portal-role/record/edit",["exports","views/role/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{tableView="views/portal-role/record/table";stickButtonsContainerAllTheWay=!0}e.default=s}),define("views/portal-role/record/detail",["exports","views/role/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{tableView="views/portal-role/record/table";stickButtonsContainerAllTheWay=!0}e.default=s}),define("views/portal/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{massActionList=["remove"]}e.default=s}),define("views/portal/fields/tab-list",["exports","views/settings/fields/tab-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{noGroups=!0;setupOptions(){super.setupOptions(),this.params.options=this.params.options.filter(e=>"_delimiter_"===e||"Stream"===e||!!this.getMetadata().get(`scopes.${e}.aclPortal`))}}e.default=s}),define("views/portal/fields/quick-create-list",["exports","views/settings/fields/quick-create-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.params.options=this.params.options.filter(e=>!!this.getMetadata().get(`scopes.${e}.aclPortal`))}}e.default=s}),define("views/portal/fields/custom-id",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.listenTo(this,"change",()=>{let e=this.model.get("customId");e&&""!==e&&(e=e.replace(/ /i,"-").toLowerCase(),e=encodeURIComponent(e),this.model.set("customId",e))})}}e.default=s}),define("views/lead-capture-log-record/modals/detail",["exports","views/modals/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{editDisabled=!0;fullFormDisabled=!0}e.default=s}),define("views/layout-set/layouts",["exports","views/admin/layouts/index"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){let t=this.setId=this.options.layoutSetId;this.baseUrl="#LayoutSet/editLayouts/id="+t,super.setup(),this.wait(this.getModelFactory().create("LayoutSet").then(e=>((this.sModel=e).id=t,e.fetch())))}getLayoutScopeDataList(){let e=[],i=this.sModel.get("layoutList")||[],t=[];return i.forEach(e=>{e=e.split(".")[0];t.includes(e)||t.push(e)}),t.forEach(t=>{let a={},s=(a.scope=t,a.url=this.baseUrl+"&scope="+t,a.typeDataList=[],[]);i.forEach(e=>{var[e,t]=e.split(".");e===a.scope&&s.push(t)}),s.forEach(e=>{a.typeDataList.push({type:e,url:this.baseUrl+`&scope=${t}&type=`+e,label:this.translateLayoutName(e,t)})}),a.typeList=s,e.push(a)}),e}getHeaderHtml(){var e=' <span class="breadcrumb-separator"><span></span></span> ';return $("<span>").append($("<a>").attr("href","#LayoutSet").text(this.translate("LayoutSet","scopeNamesPlural")),e,$("<a>").attr("href","#LayoutSet/view/"+this.sModel.id).text(this.sModel.get("name")),e,$("<span>").text(this.translate("Edit Layouts","labels","LayoutSet"))).get(0).outerHTML}navigate(e,t){e="#LayoutSet/editLayouts/id="+this.setId+"&scope="+e+"&type="+t;this.getRouter().navigate(e,{trigger:!1})}}e.default=s}),define("views/layout-set/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{massActionList=["remove","export"]}e.default=s}),define("views/layout-set/fields/layout-list",["exports","views/fields/multi-enum","views/admin/layouts/index"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{typeList=["list","detail","listSmall","detailSmall","bottomPanelsDetail","filters","massUpdate","sidePanelsDetail","sidePanelsEdit","sidePanelsDetailSmall","sidePanelsEditSmall"];setupOptions(){this.params.options=[],this.translatedOptions={},this.scopeList=Object.keys(this.getMetadata().get("scopes")).filter(e=>this.getMetadata().get(["scopes",e,"layouts"])).sort((e,t)=>this.translate(e,"scopeNames").localeCompare(this.translate(t,"scopeNames"))),a.default.prototype.getLayoutScopeDataList.call(this).forEach(a=>{a.typeList.forEach(e=>{var t=a.scope+"."+e;"Portal"!==e.substr(-6)&&(this.params.options.push(t),this.translatedOptions[t]=this.translate(a.scope,"scopeNames")+" . "+this.translate(e,"layouts","Admin"))})})}translateLayoutName(e,t){return a.default.prototype.translateLayoutName.call(this,e,t)}}e.default=i}),define("views/layout-set/fields/edit",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent=`<a
            class="btn btn-default"
            href="#LayoutSet/editLayouts/id={{model.id}}"
        >{{translate 'Edit Layouts' scope='LayoutSet'}}</a>`;editTemplateContent=""}e.default=s}),define("views/inbound-email/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{quickDetailDisabled=!0;quickEditDisabled=!0;massActionList=["remove","massUpdate"];checkAllResultDisabled=!0}e.default=s}),define("views/inbound-email/record/edit",["exports","views/record/edit","views/inbound-email/record/detail"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{setup(){super.setup(),a.default.prototype.setupFieldsBehaviour.call(this),a.default.prototype.initSslFieldListening.call(this),a.default.prototype.wasFetched.call(this)&&this.setFieldReadOnly("fetchSince")}modifyDetailLayout(e){a.default.prototype.modifyDetailLayout.call(this,e)}controlStatusField(){a.default.prototype.controlStatusField.call(this)}initSmtpFieldsControl(){a.default.prototype.initSmtpFieldsControl.call(this)}controlSmtpFields(){a.default.prototype.controlSmtpFields.call(this)}controlSentFolderField(){a.default.prototype.controlSentFolderField.call(this)}controlSmtpAuthField(){a.default.prototype.controlSmtpAuthField.call(this)}wasFetched(){a.default.prototype.wasFetched.call(this)}}e.default=i}),define("views/inbound-email/fields/test-send",["exports","views/email-account/fields/test-send"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getSmtpData(){return{server:this.model.get("smtpHost"),port:this.model.get("smtpPort"),auth:this.model.get("smtpAuth"),security:this.model.get("smtpSecurity"),username:this.model.get("smtpUsername"),password:this.model.get("smtpPassword")||null,authMechanism:this.model.get("smtpAuthMechanism"),fromName:this.model.get("fromName"),fromAddress:this.model.get("emailAddress"),type:"inboundEmail",id:this.model.id}}}e.default=s}),define("views/inbound-email/fields/test-connection",["exports","views/email-account/fields/test-connection"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{url="InboundEmail/action/testConnection"}e.default=s}),define("views/inbound-email/fields/target-user-position",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.translatedOptions={"":`--${this.translate("All")}--`},this.params.options=[""],this.model.get("targetUserPosition")&&this.model.get("teamId")&&this.params.options.push(this.model.get("targetUserPosition")),this.loadRoleList(()=>{this.mode===this.MODE_EDIT&&this.isRendered()&&this.render()}),this.listenTo(this.model,"change:teamId",()=>{this.loadRoleList(()=>this.render())})}loadRoleList(t){let a=this.model.attributes.teamId;a||(this.params.options=[""]),this.getModelFactory().create("Team",e=>{e.id=a,e.fetch().then(()=>{this.params.options=e.get("positionList")||[],this.params.options.unshift(""),t.call(this)})})}}e.default=s}),define("views/inbound-email/fields/name",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{afterRender(){var e;super.afterRender(),this.mode===this.MODE_LIST_LINK&&this.model.attributes.isSystem&&(e=this.element.querySelector("a.link"))&&e.classList.add("text-warning")}}e.default=s}),define("views/inbound-email/fields/folders",["exports","views/email-account/fields/folders"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getFoldersUrl="InboundEmail/action/getFolders"}e.default=s}),define("views/inbound-email/fields/folder",["exports","views/email-account/fields/folder"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getFoldersUrl="InboundEmail/action/getFolders"}e.default=s}),define("views/inbound-email/fields/email-address",["exports","views/fields/email-address"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.on("change",()=>{var e=this.model.get("emailAddress");this.model.set("name",e),!this.model.isNew()&&this.model.get("replyToAddress")||this.model.set("replyToAddress",e)})}}e.default=s}),define("views/extension/record/row-actions",["exports","views/record/row-actions/default"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getActionList(){return this.options.acl.edit?this.model.get("isInstalled")?[{action:"uninstall",label:"Uninstall",data:{id:this.model.id}}]:[{action:"install",label:"Install",data:{id:this.model.id}},{action:"quickRemove",label:"Remove",data:{id:this.model.id}}]:[]}}e.default=s}),define("views/extension/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{rowActionsView="views/extension/record/row-actions";checkboxes=!1;quickDetailDisabled=!0;quickEditDisabled=!0;massActionList=[]}e.default=s}),define("views/authentication-provider/record/edit",["exports","helpers/misc/authentication-provider","views/record/edit"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends a.default{saveAndNewAction=!1;helper;setup(){this.helper=new t.default(this),super.setup()}setupDynamicBehavior(){this.dynamicLogicDefs=this.helper.setupMethods(),super.setupDynamicBehavior(),this.helper.setupPanelsVisibility(()=>{this.processDynamicLogic()})}modifyDetailLayout(e){this.helper.modifyDetailLayout(e)}}e.default=i}),define("views/authentication-provider/record/detail",["exports","views/record/detail","helpers/misc/authentication-provider"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{editModeDisabled=!0;helper;setup(){this.helper=new a.default(this),super.setup()}setupDynamicBehavior(){this.dynamicLogicDefs=this.helper.setupMethods(),super.setupDynamicBehavior(),this.helper.setupPanelsVisibility(()=>{this.processDynamicLogic()})}modifyDetailLayout(e){this.helper.modifyDetailLayout(e)}}e.default=i}),define("views/authentication-provider/fields/method",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){let t=this.getMetadata().get(["authenticationMethods"])||{};var e=Object.keys(t).filter(e=>(t[e].provider||{}).isAvailable);e.unshift(""),this.params.options=e}}e.default=s}),define("views/api-user/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getCreateAttributes(){return{type:"api"}}}e.default=s}),define("views/admin/user-interface",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="userInterface";saveAndContinueEditingAction=!1;setup(){super.setup(),this.controlColorsField(),this.listenTo(this.model,"change:scopeColorsDisabled",()=>this.controlColorsField()),this.on("save",e=>{this.model.get("theme")===e.theme&&(this.model.get("themeParams").navbar||{})===e.themeParams.navbar||(this.setConfirmLeaveOut(!1),window.location.reload())})}controlColorsField(){this.model.get("scopeColorsDisabled")?this.hideField("tabColorsDisabled"):this.showField("tabColorsDisabled")}}e.default=s}),define("views/admin/sms",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="sms"}e.default=s}),define("views/admin/settings",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="settings";saveAndContinueEditingAction=!1;dynamicLogicDefs={fields:{phoneNumberPreferredCountryList:{visible:{conditionGroup:[{attribute:"phoneNumberInternational",type:"isTrue"}]}},phoneNumberExtensions:{visible:{conditionGroup:[{attribute:"phoneNumberInternational",type:"isTrue"}]}}}};setup(){super.setup(),this.getHelper().getAppParam("isRestrictedMode")&&!this.getUser().isSuperAdmin()&&(this.hideField("cronDisabled"),this.hideField("maintenanceMode"),this.setFieldReadOnly("useWebSocket"),this.setFieldReadOnly("siteUrl"))}}e.default=s}),define("views/admin/outbound-emails",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="outboundEmails";saveAndContinueEditingAction=!1;dynamicLogicDefs={fields:{smtpUsername:{visible:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"},{type:"isTrue",attribute:"smtpAuth"}]},required:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"},{type:"isTrue",attribute:"smtpAuth"}]}},smtpPassword:{visible:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"},{type:"isTrue",attribute:"smtpAuth"}]}},smtpPort:{visible:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"}]},required:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"}]}},smtpSecurity:{visible:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"}]}},smtpAuth:{visible:{conditionGroup:[{type:"isNotEmpty",attribute:"smtpServer"}]}}}};afterRender(){super.afterRender();let t=this.getFieldView("smtpSecurity");this.listenTo(t,"change",()=>{var e=t.fetch().smtpSecurity;"SSL"===e?this.model.set("smtpPort",465):"TLS"===e?this.model.set("smtpPort",587):this.model.set("smtpPort",25)})}}e.default=s}),define("views/admin/notifications",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="notifications";saveAndContinueEditingAction=!1;dynamicLogicDefs={fields:{assignmentEmailNotificationsEntityList:{visible:{conditionGroup:[{type:"isTrue",attribute:"assignmentEmailNotifications"}]}},adminNotificationsNewVersion:{visible:{conditionGroup:[{type:"isTrue",attribute:"adminNotifications"}]}},adminNotificationsNewExtensionVersion:{visible:{conditionGroup:[{type:"isTrue",attribute:"adminNotifications"}]}}}};setup(){super.setup(),this.controlStreamEmailNotificationsEntityList(),this.listenTo(this.model,"change",e=>{(e.hasChanged("streamEmailNotifications")||e.hasChanged("portalStreamEmailNotifications"))&&this.controlStreamEmailNotificationsEntityList()})}controlStreamEmailNotificationsEntityList(){this.model.get("streamEmailNotifications")||this.model.get("portalStreamEmailNotifications")?(this.showField("streamEmailNotificationsEntityList"),this.showField("streamEmailNotificationsTypeList")):(this.hideField("streamEmailNotificationsEntityList"),this.hideField("streamEmailNotificationsTypeList"))}}e.default=s}),define("views/admin/jobs-settings",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="jobsSettings";saveAndContinueEditingAction=!1;dynamicLogicDefs={fields:{jobPoolConcurrencyNumber:{visible:{conditionGroup:[{type:"isTrue",attribute:"jobRunInParallel"}]}}}};setup(){super.setup(),this.getHelper().getAppParam("isRestrictedMode")&&!this.getUser().isSuperAdmin()&&(this.setFieldReadOnly("jobRunInParallel"),this.setFieldReadOnly("jobMaxPortion"),this.setFieldReadOnly("jobPoolConcurrencyNumber"),this.setFieldReadOnly("daemonInterval"),this.setFieldReadOnly("daemonMaxProcessNumber"),this.setFieldReadOnly("daemonProcessTimeout"))}}e.default=s}),define("views/admin/inbound-emails",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="inboundEmails";saveAndContinueEditingAction=!1}e.default=s}),define("views/admin/currency",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="currency";saveAndContinueEditingAction=!1;setup(){super.setup(),this.listenTo(this.model,"change:currencyList",(e,t,a)=>{a.ui&&(a=Espo.Utils.clone(e.get("currencyList")),this.setFieldOptionList("defaultCurrency",a),this.setFieldOptionList("baseCurrency",a),this.controlCurrencyRatesVisibility())}),this.listenTo(this.model,"change",(e,t)=>{t.ui&&(e.hasChanged("currencyList")||e.hasChanged("baseCurrency"))&&(t=this.getFieldView("currencyRates"))&&t.reRender()}),this.controlCurrencyRatesVisibility()}controlCurrencyRatesVisibility(){this.model.get("currencyList").length<2?this.hideField("currencyRates"):this.showField("currencyRates")}}e.default=s}),define("views/admin/authentication",["exports","views/settings/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{layoutName="authentication";saveAndContinueEditingAction=!1;dynamicLogicDefs={fields:{authIpAddressWhitelist:{visible:{conditionGroup:[{attribute:"authIpAddressCheck",type:"isTrue"}]},required:{conditionGroup:[{attribute:"authIpAddressCheck",type:"isTrue"}]}},authIpAddressCheckExcludedUsers:{visible:{conditionGroup:[{attribute:"authIpAddressCheck",type:"isTrue"}]}}},panels:{}};setup(){this.methodList=[];var e,t=this.getMetadata().get(["authenticationMethods"])||{};for(e in t)t[e].settings&&t[e].settings.isAvailable&&this.methodList.push(e);this.authFields={},super.setup(),this.getHelper().getAppParam("isRestrictedMode")&&!this.getUser().isSuperAdmin()&&(this.setFieldReadOnly("authIpAddressCheck",!0),this.setFieldReadOnly("authIpAddressWhitelist",!0),this.setFieldReadOnly("authIpAddressCheckExcludedUsers",!0)),this.handlePanelsVisibility(),this.listenTo(this.model,"change:authenticationMethod",()=>{this.handlePanelsVisibility()}),this.manage2FAFields(),this.listenTo(this.model,"change:auth2FA",()=>{this.manage2FAFields()}),this.managePasswordRecoveryFields(),this.listenTo(this.model,"change:passwordRecoveryDisabled",()=>{this.managePasswordRecoveryFields()})}setupBeforeFinal(){this.dynamicLogicDefs=Espo.Utils.cloneDeep(this.dynamicLogicDefs),this.methodList.forEach(e=>{var t=this.getMetadata().get(["authenticationMethods",e,"settings","fieldList"]),a=(t&&(this.authFields[e]=t),this.getMetadata().get(["authenticationMethods",e,"settings","dynamicLogic","fields"]));if(a)for(var s in a)this.dynamicLogicDefs.fields[s]=Espo.Utils.cloneDeep(a[s])}),super.setupBeforeFinal()}modifyDetailLayout(a){this.methodList.forEach(e=>{var t=this.getMetadata().get(["authenticationMethods",e,"settings","layout"]);t&&((t=Espo.Utils.cloneDeep(t)).name=e,t.tabBreak=!0,t.tabLabel=t.label,t.label=null,this.prepareLayout(t,e),a.push(t))})}prepareLayout(e,a){e.rows.forEach(e=>{e.filter(e=>!e.noLabel&&!e.labelText&&e.name).forEach(e=>{var t=this.translate(e.name,"fields","Settings");t&&0===t.toLowerCase().indexOf(a.toLowerCase()+" ")&&(e.labelText=t.substring(a.length+1))})})}handlePanelsVisibility(){let a=this.model.get("authenticationMethod");this.methodList.forEach(e=>{var t=this.authFields[e]||[];e!==a?(this.hidePanel(e),t.forEach(e=>{this.hideField(e)})):(this.showPanel(e),t.forEach(e=>{this.showField(e)}),this.processDynamicLogic())})}manage2FAFields(){this.model.get("auth2FA")?(this.showField("auth2FAForced"),this.showField("auth2FAMethodList"),this.showField("auth2FAInPortal"),this.setFieldRequired("auth2FAMethodList")):(this.hideField("auth2FAForced"),this.hideField("auth2FAMethodList"),this.hideField("auth2FAInPortal"),this.setFieldNotRequired("auth2FAMethodList"))}managePasswordRecoveryFields(){this.model.get("passwordRecoveryDisabled")?(this.hideField("passwordRecoveryForAdminDisabled"),this.hideField("passwordRecoveryForInternalUsersDisabled"),this.hideField("passwordRecoveryNoExposure")):(this.showField("passwordRecoveryForAdminDisabled"),this.showField("passwordRecoveryForInternalUsersDisabled"),this.showField("passwordRecoveryNoExposure"))}}e.default=s}),define("views/admin/upgrade/ready",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/upgrade/ready";cssName="ready-modal";createButton=!0;data(){return{version:this.upgradeData.version,text:this.translate("upgradeVersion","messages","Admin").replace("{version}",this.upgradeData.version)}}setup(){this.buttonList=[{name:"run",label:this.translate("Run Upgrade","labels","Admin"),style:"danger",onClick:()=>this.actionRun()},{name:"cancel",label:"Cancel"}],this.upgradeData=this.options.upgradeData,this.headerText=this.getLanguage().translate("Ready for upgrade","labels","Admin")}actionRun(){this.trigger("run"),this.remove()}}e.default=s}),define("views/admin/upgrade/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/upgrade/index";packageContents=null;data(){return{versionMsg:this.translate("Current version")+": "+this.getConfig().get("version"),infoMsg:this.translate("upgradeInfo","messages","Admin").replace("{url}","https://www.espocrm.com/documentation/administration/upgrading/"),backupsMsg:this.translate("upgradeBackup","messages","Admin"),upgradeRecommendation:this.translate("upgradeRecommendation","messages","Admin"),downloadMsg:this.translate("downloadUpgradePackage","messages","Admin").replace("{url}","https://www.espocrm.com/download/upgrades")}}afterRender(){this.$el.find(".panel-body a").attr("target","_BLANK")}events={'change input[name="package"]':function(e){this.$el.find('button[data-action="upload"]').addClass("disabled").attr("disabled","disabled"),this.$el.find(".message-container").html("");e=e.currentTarget.files;e.length&&this.selectFile(e[0])},'click button[data-action="upload"]':function(){this.upload()}};selectFile(e){var t=new FileReader;t.onload=e=>{this.packageContents=e.target.result,this.$el.find('button[data-action="upload"]').removeClass("disabled").removeAttr("disabled")},t.readAsDataURL(e)}showError(e){e=this.translate(e,"errors","Admin"),this.$el.find(".message-container").html(e)}upload(){this.$el.find('button[data-action="upload"]').addClass("disabled").attr("disabled","disabled"),Espo.Ui.notify(this.translate("Uploading...")),Espo.Ajax.postRequest("Admin/action/uploadUpgradePackage",this.packageContents,{contentType:"application/zip",timeout:0}).then(t=>{t.id?(Espo.Ui.notify(!1),this.createView("popup","views/admin/upgrade/ready",{upgradeData:t},e=>{e.render(),this.$el.find('button[data-action="upload"]').removeClass("disabled").removeAttr("disabled"),e.once("run",()=>{e.close(),this.$el.find(".panel.upload").addClass("hidden"),this.run(t.id,t.version)})})):this.showError(this.translate("Error occurred"))}).catch(e=>{this.showError(e.getResponseHeader("X-Status-Reason")),Espo.Ui.notify(!1)})}textNotification(e){this.$el.find(".notify-text").html(e)}run(e,t){var a=this.translate("Upgrading...","labels","Admin");Espo.Ui.notify(this.translate("pleaseWait","messages")),this.textNotification(a),Espo.Ajax.postRequest("Admin/action/runUpgrade",{id:e},{timeout:0,bypassAppReload:!0}).then(()=>{var e=this.getCache();e&&e.clear(),this.createView("popup","views/admin/upgrade/done",{version:t},e=>{Espo.Ui.notify(!1),e.render()})}).catch(e=>{this.$el.find(".panel.upload").removeClass("hidden");e=e.getResponseHeader("X-Status-Reason");this.textNotification(this.translate("Error")+": "+e)})}}e.default=s}),define("views/admin/upgrade/done",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/upgrade/done";cssName="done-modal";data(){return{version:this.options.version,text:this.translate("upgradeDone","messages","Admin").replace("{version}",this.options.version)}}setup(){this.on("remove",()=>window.location.reload()),this.buttonList=[{name:"close",label:"Close",onClick:e=>{setTimeout(()=>{this.getRouter().navigate("#Admin",{trigger:!0})},500),e.close()}}],this.headerText=this.getLanguage().translate("Upgraded successfully","labels","Admin")}}e.default=s}),define("views/admin/template-manager/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/template-manager/index";data(){return{templateDataList:this.templateDataList}}events={'click [data-action="selectTemplate"]':function(e){let t=$(e.currentTarget).data("name");this.getRouter().checkConfirmLeaveOut(()=>{this.selectTemplate(t)})}};setup(){this.templateDataList=[];var e=Object.keys(this.getMetadata().get(["app","templates"])||{});e.sort((e,t)=>this.translate(e,"templates","Admin").localeCompare(this.translate(t,"templates","Admin"))),e.forEach(t=>{var e=this.getMetadata().get(["app","templates",t]);e.scopeListConfigParam||e.scopeList?((e=Espo.Utils.clone(e.scopeList||this.getConfig().get(e.scopeListConfigParam)||[])).sort((e,t)=>this.translate(e,"scopeNames").localeCompare(this.translate(t,"scopeNames"))),e.forEach(e=>{e={name:t+"_"+e,text:this.translate(t,"templates","Admin")+" · "+this.translate(e,"scopeNames")};this.templateDataList.push(e)})):(e={name:t,text:this.translate(t,"templates","Admin")},this.templateDataList.push(e))}),this.selectedTemplate=this.options.name,this.selectedTemplate&&this.once("after:render",()=>{this.selectTemplate(this.selectedTemplate,!0)})}selectTemplate(e){this.selectedTemplate=e,this.getRouter().navigate("#Admin/templateManager/name="+this.selectedTemplate,{trigger:!1}),this.createRecordView(),this.$el.find('[data-action="selectTemplate"]').removeClass("disabled").removeAttr("disabled"),this.$el.find(`[data-name="${e}"][data-action="selectTemplate"]`).addClass("disabled").attr("disabled","disabled")}createRecordView(){Espo.Ui.notifyWait(),this.createView("record","views/admin/template-manager/edit",{selector:".template-record",name:this.selectedTemplate},e=>{e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0)})}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Template Manager","labels","Admin"))}}e.default=s}),define("views/admin/template-manager/edit",["exports","view","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/template-manager/edit";data(){return{title:this.title,hasSubject:this.hasSubject}}events={'click [data-action="save"]':function(){this.actionSave()},'click [data-action="cancel"]':function(){this.actionCancel()},'click [data-action="resetToDefault"]':function(){this.actionResetToDefault()},"keydown.form":function(e){var t=Espo.Utils.getKeyFromKeyEvent(e);"Control+KeyS"!==t&&"Control+Enter"!==t||(this.actionSave(),e.preventDefault(),e.stopPropagation())}};setup(){this.wait(!0),this.fullName=this.options.name,this.name=this.fullName,this.scope=null;var e=this.fullName.split("_");1<e.length&&(this.scope=e[1],this.name=e[0]),this.hasSubject=!this.getMetadata().get(["app","templates",this.name,"noSubject"]),this.title=this.translate(this.name,"templates","Admin"),this.scope&&(this.title+=" · "+this.translate(this.scope,"scopeNames")),this.attributes={},Espo.Ajax.getRequest("TemplateManager/action/getTemplate",{name:this.name,scope:this.scope}).then(e=>{var t=this.model=new a.default;t.name="TemplateManager",t.set("body",e.body),this.attributes.body=e.body,this.hasSubject&&(t.set("subject",e.subject),this.attributes.subject=e.subject),this.listenTo(t,"change",()=>{this.setConfirmLeaveOut(!0)}),this.createView("bodyField","views/admin/template-manager/fields/body",{name:"body",model:t,selector:".body-field",mode:"edit"}),this.hasSubject&&this.createView("subjectField","views/fields/varchar",{name:"subject",model:t,selector:".subject-field",mode:"edit"}),this.wait(!1)})}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}afterRender(){this.$save=this.$el.find('button[data-action="save"]'),this.$cancel=this.$el.find('button[data-action="cancel"]'),this.$resetToDefault=this.$el.find('button[data-action="resetToDefault"]')}actionSave(){this.$save.addClass("disabled").attr("disabled"),this.$cancel.addClass("disabled").attr("disabled"),this.$resetToDefault.addClass("disabled").attr("disabled"),this.getView("bodyField").fetchToModel();let e={name:this.name,body:this.model.get("body")};this.scope&&(e.scope=this.scope),this.hasSubject&&(this.getView("subjectField").fetchToModel(),e.subject=this.model.get("subject")),Espo.Ui.notify(this.translate("saving","messages")),Espo.Ajax.postRequest("TemplateManager/action/saveTemplate",e).then(()=>{this.setConfirmLeaveOut(!1),this.attributes.body=e.body,this.attributes.subject=e.subject,this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled"),this.$resetToDefault.removeClass("disabled").removeAttr("disabled"),Espo.Ui.success(this.translate("Saved"))}).catch(()=>{this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled"),this.$resetToDefault.removeClass("disabled").removeAttr("disabled")})}actionCancel(){this.model.set("subject",this.attributes.subject),this.model.set("body",this.attributes.body),this.setConfirmLeaveOut(!1)}actionResetToDefault(){this.confirm(this.translate("confirmation","messages"),()=>{this.$save.addClass("disabled").attr("disabled"),this.$cancel.addClass("disabled").attr("disabled"),this.$resetToDefault.addClass("disabled").attr("disabled");var e={name:this.name,body:this.model.get("body")};this.scope&&(e.scope=this.scope),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("TemplateManager/action/resetTemplate",e).then(e=>{this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled"),this.$resetToDefault.removeClass("disabled").removeAttr("disabled"),this.attributes.body=e.body,this.attributes.subject=e.subject,this.model.set("subject",e.subject),this.model.set("body",e.body),this.setConfirmLeaveOut(!1),Espo.Ui.notify(!1)}).catch(()=>{this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled"),this.$resetToDefault.removeClass("disabled").removeAttr("disabled")})})}}e.default=i}),define("views/admin/template-manager/fields/body",["exports","views/fields/wysiwyg"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{htmlPurificationForEditDisabled=!0;handlebars=!0}e.default=s}),define("views/admin/system-requirements/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/system-requirements/index";requirements;data(){return{phpRequirementList:this.requirements.php,databaseRequirementList:this.requirements.database,permissionRequirementList:this.requirements.permission}}setup(){this.requirements={},Espo.Ui.notifyWait();var e=Espo.Ajax.getRequest("Admin/action/systemRequirementList").then(e=>{this.requirements=e,Espo.Ui.notify()});this.wait(e)}}e.default=s}),define("views/admin/panels/notifications",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/panels/notifications";data(){return{notificationList:this.notificationList}}setup(){this.notificationList=[],Espo.Ajax.getRequest("Admin/action/adminNotificationList").then(e=>{this.notificationList=e,(this.isRendered()||this.isBeingRendered())&&this.reRender()})}}e.default=s}),define("views/admin/link-manager/modals/edit",["exports","views/modal","model","views/admin/link-manager/index","views/fields/enum"],function(e,t,p,m,g){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=a(t),p=a(p),m=a(m),g=a(g);class s extends t.default{template="admin/link-manager/modals/edit";cssName="edit";className="dialog dialog-record";model;shortcutKeys={"Control+KeyS":function(e){this.save({noClose:!0}),e.preventDefault(),e.stopPropagation()},"Control+Enter":function(e){document.activeElement instanceof HTMLInputElement&&document.activeElement.dispatchEvent(new Event("change",{bubbles:!0})),this.save(),e.preventDefault(),e.stopPropagation()}};setup(){this.buttonList=[{name:"save",label:"Save",style:"danger",onClick:()=>{this.save()}},{name:"cancel",label:"Cancel",onClick:()=>{this.close()}}];var e=this.scope=this.options.scope,a=this.link=this.options.link||!1,s=e,t=this.isNew=!1===a,i=(this.headerText=this.translate("Create Link","labels","Admin"),t||(this.headerText=this.translate("Edit Link","labels","Admin")+" · "+this.translate(e,"scopeNames")+" · "+this.translate(a,"links",e)),this.model=new p.default),l=(i.name="EntityManager",this.model.set("entity",e),this.getMetadata().getScopeEntityList().filter(e=>{e=this.getMetadata().get(["scopes",e])||{};return!!e.customizable&&!1!==(e.entityManager||{}).relationships}).sort((e,t)=>{e=this.translate(e,"scopeNames"),t=this.translate(t,"scopeNames");return e.localeCompare(t)}));let o=!0,n;if(!t){var d=this.getMetadata().get(`entityDefs.${e}.links.${a}.entity`),r=this.getMetadata().get(`entityDefs.${e}.links.${a}.foreign`),f=this.getLanguage().translate(a,"links",e);let t=this.getLanguage().translate(r,"links",d);var h=this.getMetadata().get(`entityDefs.${s}.links.${a}.type`),c=this.getMetadata().get(`entityDefs.${d}.links.${r}.type`);if("belongsToParent"===h){n="childrenToParent",t=null;let e=this.getMetadata().get(["entityDefs",s,"fields",a,"entityList"])||[];null===this.getMetadata().get(["entityDefs",s,"fields",a,"entityList"])&&(e=l,this.noParentEntityTypeList=!0),this.model.set("parentEntityTypeList",e);l=this.getForeignLinkEntityTypeList(s,a,e);this.model.set("foreignLinkEntityTypeList",l)}else n=m.default.prototype.computeRelationshipType.call(this,h,c);this.model.set("linkType",n),this.model.set("entityForeign",d),this.model.set("link",a),this.model.set("linkForeign",r),this.model.set("label",f),this.model.set("labelForeign",t);l="linkMultiple"===this.getMetadata().get(["entityDefs",e,"fields",a,"type"])&&!this.getMetadata().get(["entityDefs",e,"fields",a,"noLoad"]),h="linkMultiple"===this.getMetadata().get(["entityDefs",d,"fields",r,"type"])&&!this.getMetadata().get(["entityDefs",d,"fields",r,"noLoad"]),f=(this.model.set("linkMultipleField",l),this.model.set("linkMultipleFieldForeign",h),"manyToMany"===n&&(c=this.getMetadata().get(`entityDefs.${s}.links.${a}.relationName`),this.model.set("relationName",c)),this.getMetadata().get(["entityDefs",e,"links",a,"audited"])||!1),l=this.getMetadata().get(["entityDefs",d,"links",r,"audited"])||!1,h=(this.model.set("audited",f),this.model.set("auditedForeign",l),this.getMetadata().get(`clientDefs.${e}.relationshipPanels.${a}.layout`)),c=this.getMetadata().get(`clientDefs.${d}.relationshipPanels.${r}.layout`),f=(this.model.set("layout",h),this.model.set("layoutForeign",c),this.getRelationshipPanelParam(e,a,"selectPrimaryFilterName")),l=this.getRelationshipPanelParam(d,r,"selectPrimaryFilterName");this.model.set("selectFilter",f),this.model.set("selectFilterForeign",l),o=this.getMetadata().get(`entityDefs.${s}.links.${a}.isCustom`)}let u=this.getMetadata().get("scopes")||null;h=(Object.keys(u)||[]).filter(e=>{e=u[e]||{};return!(!e.entity||!e.customizable)&&!1!==(e.entityManager||{}).relationships}).sort((e,t)=>{e=this.translate(e,"scopeNames"),t=this.translate(t,"scopeNames");return e.localeCompare(t)}),h.unshift(""),this.createView("entity","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="entity"]',defs:{name:"entity"},readOnly:!0}),this.createView("entityForeign","views/fields/enum",{model:i,mode:"edit",selector:'.field[data-name="entityForeign"]',defs:{name:"entityForeign",params:{required:!0,options:h,translation:"Global.scopeNames"}},readOnly:!t}),this.createView("linkType","views/fields/enum",{model:i,mode:"edit",selector:'.field[data-name="linkType"]',defs:{name:"linkType",params:{required:!0,options:["","oneToMany","manyToOne","manyToMany","oneToOneRight","oneToOneLeft","childrenToParent"]}},readOnly:!t}),this.createView("link","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="link"]',defs:{name:"link",params:{required:!0,trim:!0,maxLength:61,noSpellCheck:!0}},readOnly:!t}),this.createView("linkForeign","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="linkForeign"]',defs:{name:"linkForeign",params:{required:!0,trim:!0,maxLength:61,noSpellCheck:!0}},readOnly:!t}),this.createView("label","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="label"]',defs:{name:"label",params:{required:!0,trim:!0}}}),this.createView("labelForeign","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="labelForeign"]',defs:{name:"labelForeign",params:{required:!0,trim:!0}}}),(t||this.model.get("relationName"))&&this.createView("relationName","views/fields/varchar",{model:i,mode:"edit",selector:'.field[data-name="relationName"]',defs:{name:"relationName",params:{required:!0,trim:!0,maxLength:61,noSpellCheck:!0}},readOnly:!t}),this.createView("linkMultipleField","views/fields/bool",{model:i,mode:"edit",selector:'.field[data-name="linkMultipleField"]',defs:{name:"linkMultipleField"},readOnly:!o,tooltip:!0,tooltipText:this.translate("linkMultipleField","tooltips","EntityManager")}),this.createView("linkMultipleFieldForeign","views/fields/bool",{model:i,mode:"edit",selector:'.field[data-name="linkMultipleFieldForeign"]',defs:{name:"linkMultipleFieldForeign"},readOnly:!o,tooltip:!0,tooltipText:this.translate("linkMultipleField","tooltips","EntityManager")}),this.createView("audited","views/fields/bool",{model:i,mode:"edit",selector:'.field[data-name="audited"]',defs:{name:"audited"},tooltip:!0,tooltipText:this.translate("linkAudited","tooltips","EntityManager")}),this.createView("auditedForeign","views/fields/bool",{model:i,mode:"edit",selector:'.field[data-name="auditedForeign"]',defs:{name:"auditedForeign"},tooltip:!0,tooltipText:this.translate("linkAudited","tooltips","EntityManager")}),c=["",...this.getEntityTypeLayouts(this.scope)],e=this.getEntityTypeLayoutsTranslations(this.scope);this.layoutFieldView=new g.default({name:"layout",model:i,mode:"edit",defs:{name:"layout"},params:{options:[""]},translatedOptions:{"":this.translate("Default")}}),this.layoutForeignFieldView=new g.default({name:"layoutForeign",model:i,mode:"edit",defs:{name:"layoutForeign"},params:{options:c},translatedOptions:e}),this.assignView("layout",this.layoutFieldView,'.field[data-name="layout"]'),this.assignView("layoutForeign",this.layoutForeignFieldView,'.field[data-name="layoutForeign"]'),this.selectFilterFieldView=new g.default({name:"selectFilter",model:i,mode:"edit",defs:{name:"selectFilter"},params:{options:[""]},translatedOptions:{"":this.translate("all","presetFilters")},tooltip:!0,tooltipText:this.translate("linkSelectFilter","tooltips","EntityManager")}),this.selectFilterForeignFieldView=new g.default({name:"selectFilterForeign",model:i,mode:"edit",defs:{name:"selectFilterForeign"},params:{options:["",...this.getEntityTypeFilters(this.scope)]},translatedOptions:this.getEntityTypeFiltersTranslations(this.scope),tooltip:!0,tooltipText:this.translate("linkSelectFilter","tooltips","EntityManager")}),this.assignView("selectFilter",this.selectFilterFieldView,'.field[data-name="selectFilter"]'),this.assignView("selectFilterForeign",this.selectFilterForeignFieldView,'.field[data-name="selectFilterForeign"]'),this.createView("parentEntityTypeList","views/fields/entity-type-list",{model:i,mode:"edit",selector:'.field[data-name="parentEntityTypeList"]',defs:{name:"parentEntityTypeList"}}),this.createView("foreignLinkEntityTypeList","views/admin/link-manager/fields/foreign-link-entity-type-list",{model:i,mode:"edit",selector:'.field[data-name="foreignLinkEntityTypeList"]',defs:{name:"foreignLinkEntityTypeList",params:{options:this.model.get("parentEntityTypeList")||[]}}}),this.model.fetchedAttributes=this.model.getClonedAttributes(),this.listenTo(this.model,"change",()=>{if(this.model.hasChanged("parentEntityTypeList")||this.model.hasChanged("linkForeign")||this.model.hasChanged("link")){var e=this.getView("foreignLinkEntityTypeList");e&&!this.noParentEntityTypeList&&e.setOptionList(this.model.get("parentEntityTypeList")||[]);let t=Espo.Utils.clone(this.model.get("foreignLinkEntityTypeList")||[]);this.getForeignLinkEntityTypeList(this.model.get("entity"),this.model.get("link"),this.model.get("parentEntityTypeList")||[],!0).forEach(e=>{~t.indexOf(e)||t.push(e)}),this.model.set("foreignLinkEntityTypeList",t)}}),this.controlLayoutField(),this.listenTo(this.model,"change:entityForeign",()=>this.controlLayoutField()),this.controlFilterField(),this.listenTo(this.model,"change:entityForeign",()=>this.controlFilterField())}getEntityTypeLayouts(e){let t=this.getMetadata().get(["clientDefs",e,"additionalLayouts"],{});return Object.keys(t).filter(e=>["list","listSmall"].includes(t[e].type))}getEntityTypeLayoutsTranslations(t){let a={};return this.getEntityTypeLayouts(t).forEach(e=>{a[e]=this.getLanguage().has(e,"layouts",t)?this.getLanguage().translate(e,"layouts",t):this.getLanguage().translate(e,"layouts","Admin")}),a[""]=this.translate("Default"),a}getEntityTypeFiltersTranslations(t){let a={};return this.getEntityTypeFilters(t).forEach(e=>{a[e]=this.getLanguage().translate(e,"presetFilters",t)}),a[""]=this.translate("all","presetFilters"),a}getEntityTypeFilters(e){return this.getMetadata().get(`clientDefs.${e}.filterList`,[]).map(e=>"string"==typeof e?e:e.name)}controlLayoutField(){var e=this.model.get("entityForeign"),t=e?["",...this.getEntityTypeLayouts(e)]:[""];this.layoutFieldView.translatedOptions=e?this.getEntityTypeLayoutsTranslations(e):{},this.layoutFieldView.setOptionList(t).then(()=>this.layoutFieldView.reRender())}controlFilterField(){var e=this.model.get("entityForeign"),t=e?["",...this.getEntityTypeFilters(e)]:[""];this.selectFilterFieldView.translatedOptions=e?this.getEntityTypeFiltersTranslations(e):{},this.selectFilterFieldView.setOptionList(t).then(()=>this.selectFilterFieldView.reRender())}toPlural(e){return"y"===e.slice(-1)?e.substr(0,e.length-1)+"ies":"s"===e.slice(-1)?e.substr(0,e.length)+"es":e+"s"}populateFields(){var s=this.model.get("entityForeign"),i=this.model.get("linkType");let l,o;if("childrenToParent"===i)this.model.set("link","parent"),this.model.set("label","Parent"),o=this.entityTypeToLink(this.scope,!0),this.getMetadata().get(["entityDefs",this.scope,"links","parent"])&&(this.model.set("link","parentAnother"),this.model.set("label","Parent Another"),o+="Another"),this.model.set("linkForeign",o),this.model.set("labelForeign",null),this.model.set("entityForeign",null);else if(s&&i){switch(i){case"oneToMany":o=this.entityTypeToLink(this.scope),l=this.entityTypeToLink(s,!0),s===this.scope&&(o+="Parent");break;case"manyToOne":o=this.entityTypeToLink(this.scope,!0),l=this.entityTypeToLink(s),s===this.scope&&(l+="Parent");break;case"manyToMany":o=this.entityTypeToLink(this.scope,!0),(l=this.entityTypeToLink(s,!0))===o&&(l+="Right",o+="Left");var n=this.stripPrefixFromCustomEntityType(this.scope),d=this.stripPrefixFromCustomEntityType(s),d=n.localeCompare(d)?Espo.Utils.lowerCaseFirst(n)+d:Espo.Utils.lowerCaseFirst(d)+n;this.model.set("relationName",d);break;case"oneToOneLeft":o=this.entityTypeToLink(this.scope),l=this.entityTypeToLink(s),s===this.scope&&o===Espo.Utils.lowerCaseFirst(this.scope)&&(l+="Parent");break;case"oneToOneRight":o=this.entityTypeToLink(this.scope),l=this.entityTypeToLink(s),s===this.scope&&o===Espo.Utils.lowerCaseFirst(this.scope)&&(o+="Parent")}let e=1;for(;this.getMetadata().get(["entityDefs",this.scope,"links",l]);)l+=e.toString(),e++;for(e=1;this.getMetadata().get(["entityDefs",s,"links",o]);)o+=e.toString(),e++;this.model.set("link",l),this.model.set("linkForeign",o);let t=Espo.Utils.upperCaseFirst(l.replace(/([a-z])([A-Z])/g,"$1 $2")),a=Espo.Utils.upperCaseFirst(o.replace(/([a-z])([A-Z])/g,"$1 $2"));t.startsWith("C ")&&(t=t.substring(2)),a.startsWith("C ")&&(a=a.substring(2)),this.model.set("label",t||null),this.model.set("labelForeign",a||null)}else this.model.set("link",null),this.model.set("linkForeign",null),this.model.set("label",null),this.model.set("labelForeign",null)}entityTypeToLink(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];let a=this.stripPrefixFromCustomEntityType(e);return a=Espo.Utils.lowerCaseFirst(a),a=t?this.toPlural(a):a}stripPrefixFromCustomEntityType(e){let t=e;return t=this.getMetadata().get(`scopes.${e}.isCustom`)&&"C"===e[0]&&/[A-Z]/.test(e[1])?t.substring(1):t}handleLinkChange(e){let t=this.model.get(e);t&&(t=t.replace(/-/g," ").replace(/_/g," ").replace(/[^\w\s]/gi,"").replace(/ (.)/g,(e,t)=>t.toUpperCase()).replace(" ","")).length&&(t=Espo.Utils.lowerCaseFirst(t)),this.model.set(e,t)}hideField(e){var t=this.getView(e);t&&(t.disabled=!0),this.$el.find(".cell[data-name="+e+"]").addClass("hidden-cell")}showField(e){var t=this.getView(e);t&&(t.disabled=!1),this.$el.find(".cell[data-name="+e+"]").removeClass("hidden-cell")}handleLinkTypeChange(){var e=this.model.get("linkType");this.showField("entityForeign"),this.showField("labelForeign"),this.hideField("parentEntityTypeList"),this.hideField("foreignLinkEntityTypeList"),"manyToMany"===e?(this.showField("relationName"),this.showField("linkMultipleField"),this.showField("linkMultipleFieldForeign"),this.showField("audited"),this.showField("auditedForeign"),this.showField("layout"),this.showField("layoutForeign"),this.showField("selectFilter"),this.showField("selectFilterForeign")):(this.hideField("relationName"),"oneToMany"===e?(this.showField("linkMultipleField"),this.hideField("linkMultipleFieldForeign"),this.showField("audited"),this.hideField("auditedForeign"),this.showField("layout"),this.hideField("layoutForeign"),this.showField("selectFilter"),this.showField("selectFilterForeign")):"manyToOne"===e?(this.hideField("linkMultipleField"),this.showField("linkMultipleFieldForeign"),this.hideField("audited"),this.showField("auditedForeign"),this.hideField("layout"),this.showField("layoutForeign"),this.showField("selectFilter"),this.showField("selectFilterForeign")):(this.hideField("linkMultipleField"),this.hideField("linkMultipleFieldForeign"),this.hideField("audited"),this.hideField("auditedForeign"),this.hideField("layout"),this.hideField("layoutForeign"),"parentToChildren"===e?(this.showField("audited"),this.hideField("auditedForeign"),this.showField("layout"),this.hideField("layoutForeign"),this.hideField("selectFilter"),this.hideField("selectFilterForeign")):"childrenToParent"===e?(this.hideField("audited"),this.showField("auditedForeign"),this.hideField("layout"),this.hideField("layoutForeign"),this.hideField("entityForeign"),this.hideField("labelForeign"),this.hideField("selectFilter"),this.hideField("selectFilterForeign"),this.noParentEntityTypeList||this.showField("parentEntityTypeList"),this.model.get("linkForeign")?this.showField("foreignLinkEntityTypeList"):this.hideField("foreignLinkEntityTypeList")):(this.hideField("audited"),this.hideField("auditedForeign"),this.hideField("layout"),this.hideField("layoutForeign"),e?(this.showField("selectFilter"),this.showField("selectFilterForeign")):(this.hideField("selectFilter"),this.hideField("selectFilterForeign"))))),this.getMetadata().get(["scopes",this.scope,"stream"])||this.hideField("audited"),this.getMetadata().get(["scopes",this.model.get("entityForeign"),"stream"])||this.hideField("auditedForeign")}afterRender(){this.handleLinkTypeChange(),this.getView("linkType").on("change",()=>{this.handleLinkTypeChange(),this.populateFields()}),this.getView("entityForeign").on("change",()=>{this.populateFields()}),this.getView("link").on("change",()=>{this.handleLinkChange("link")}),this.getView("linkForeign").on("change",()=>{this.handleLinkChange("linkForeign")})}save(t){t=t||{};var a=["link","linkForeign","label","labelForeign","linkType","entityForeign","relationName","linkMultipleField","linkMultipleFieldForeign","audited","auditedForeign","layout","layoutForeign","selectFilter","selectFilterForeign","parentEntityTypeList","foreignLinkEntityTypeList"];let s=!1;if(a.forEach(e=>{this.hasView(e)&&"edit"===(e=this.getView(e)).mode&&e.fetchToModel()}),a.forEach(e=>{!this.hasView(e)||"edit"!==(e=this.getView(e)).mode||e.disabled||(s=e.validate()||s)}),!s){this.$el.find('button[data-name="save"]').addClass("disabled").attr("disabled");let e="EntityManager/action/createLink";this.isNew||(e="EntityManager/action/updateLink");var a=this.scope,i=this.model.get("entityForeign"),l=this.model.get("link"),o=this.model.get("linkForeign"),n=this.model.get("label"),d=this.model.get("labelForeign"),r=this.model.get("relationName"),f=this.model.get("linkMultipleField"),h=this.model.get("linkMultipleFieldForeign"),c=this.model.get("audited"),u=this.model.get("auditedForeign"),p=this.model.get("layout"),m=this.model.get("layoutForeign"),g=this.model.get("linkType"),a={entity:a,entityForeign:i,link:l,linkForeign:o,label:n,labelForeign:d,linkType:g,relationName:r,linkMultipleField:f,linkMultipleFieldForeign:h,audited:c,auditedForeign:u,layout:p,layoutForeign:m,selectFilter:this.model.get("selectFilter"),selectFilterForeign:this.model.get("selectFilterForeign")};this.isNew||(a.label===this.model.fetchedAttributes.label&&delete a.label,a.labelForeign===this.model.fetchedAttributes.labelForeign&&delete a.labelForeign),"childrenToParent"===g&&(delete a.entityForeign,delete a.labelForeign,a.parentEntityTypeList=this.model.get("parentEntityTypeList"),a.foreignLinkEntityTypeList=this.model.get("foreignLinkEntityTypeList"),this.noParentEntityTypeList&&(a.parentEntityTypeList=null),delete a.selectFilter,delete a.selectFilterForeign),"parentToChildren"===g&&(delete a.selectFilter,delete a.selectFilterForeign),Espo.Ajax.postRequest(e,a).then(()=>{this.isNew?Espo.Ui.success(this.translate("Created")):Espo.Ui.success(this.translate("Saved")),this.model.fetchedAttributes=this.model.getClonedAttributes(),Promise.all([this.getMetadata().loadSkipCache(),this.getLanguage().loadSkipCache()]).then(()=>{this.broadcastUpdate(),this.trigger("after:save"),t.noClose||this.close(),t.noClose&&this.$el.find('button[data-name="save"]').removeClass("disabled").removeAttr("disabled")})}).catch(e=>{var t,a;409===e.status&&(t=this.translate("linkConflict","messages","EntityManager"),(a=e.getResponseHeader("X-Status-Reason"))&&console.error(a),Espo.Ui.error(t),e.errorIsHandled=!0),this.$el.find('button[data-name="save"]').removeClass("disabled").removeAttr("disabled")})}}getForeignLinkEntityTypeList(i,l,e,o){let n=[];return e.forEach(e=>{var t,a=this.getMetadata().get(["entityDefs",e,"links"])||{};let s=!1;for(t in a)if(!(a[t].foreign!==l||a[t].entity!==i||"hasChildren"!==a[t].type||o&&a[t].isCustom)){s=!0;break}s&&n.push(e)}),n}getRelationshipPanelParam(e,t,a){return this.getMetadata().get(`clientDefs.${e}.relationshipPanels.${t}.`+a)}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata"),this.getHelper().broadcastChannel.postMessage("update:language")}}e.default=s}),define("views/admin/link-manager/fields/foreign-link-entity-type-list",["exports","views/fields/checklist"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){this.params.translation="Global.scopeNames",super.setup()}afterRender(){super.afterRender(),this.controlOptionsAvailability()}controlOptionsAvailability(){this.params.options.forEach(e=>{var t,a=this.model.get("link"),s=this.model.get("linkForeign"),i=this.model.get("entity"),l=this.getMetadata().get(["entityDefs",e,"links"])||{};let o=!1;for(t in l)(l[t].foreign===a&&!l[t].isCustom&&l[t].entity===i||t===s&&"hasChildren"!==l[t].type)&&(o=!0);o&&this.$el.find(`input[data-name="checklistItem-foreignLinkEntityTypeList-${e}"]`).attr("disabled","disabled")})}}e.default=s}),define("views/admin/layouts/side-panels-edit",["exports","views/admin/layouts/side-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{viewType="edit"}e.default=s}),define("views/admin/layouts/side-panels-edit-small",["exports","views/admin/layouts/side-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{viewType="editSmall"}e.default=s}),define("views/admin/layouts/side-panels-detail-small",["exports","views/admin/layouts/side-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{viewType="detailSmall"}e.default=s}),define("views/admin/layouts/mass-update",["exports","views/admin/layouts/rows"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name"];editable=!1;ignoreList=[];ignoreTypeList=["duration"];dataAttributesDefs={name:{readOnly:!0}};setup(){super.setup(),this.wait(!0),this.loadLayout(()=>{this.wait(!1)})}loadLayout(n){this.getModelFactory().create(this.scope).then(o=>{this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{var t,a,s,i,l=[];for(t in o.defs.fields)o.getFieldParam(t,"massUpdateDisabled")||o.getFieldParam(t,"readOnly")||o.getFieldParam(t,"readOnlyAfterCreate")||!this.isFieldEnabled(o,t)||"foreign"===o.getFieldType("field")||l.push(t);for(a in l.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),this.enabledFieldsList=[],this.enabledFields=[],this.disabledFields=[],e)this.enabledFields.push({name:e[a],labelText:this.getLanguage().translate(e[a],"fields",this.scope)}),this.enabledFieldsList.push(e[a]);for(s in l)_.contains(this.enabledFieldsList,l[s])||this.disabledFields.push({name:l[s],labelText:this.getLanguage().translate(l[s],"fields",this.scope)});for(i in this.rowLayout=this.enabledFields,this.rowLayout)this.rowLayout[i].labelText=this.getLanguage().translate(this.rowLayout[i].name,"fields",this.scope),this.itemsData[this.rowLayout[i].name]=Espo.Utils.cloneDeep(this.rowLayout[i]);n()})})}fetch(){let a=[];return $("#layout ul.enabled > li").each((e,t)=>{a.push($(t).data("name"))}),a}validate(){return!0}isFieldEnabled(e,t){var a;return-1===this.ignoreList.indexOf(t)&&-1===this.ignoreTypeList.indexOf(e.getFieldParam(t,"type"))&&(!(a=e.getFieldParam(t,"layoutAvailabilityList"))||a.includes(this.type)?!(e.getFieldParam(t,"layoutIgnoreList")||[]).includes(this.type)&&!(e.getFieldParam(t,"disabled")||e.getFieldParam(t,"utility")||e.getFieldParam(t,"layoutMassUpdateDisabled")||e.getFieldParam(t,"readOnly")):void 0)}}e.default=s}),define("views/admin/layouts/list-small",["exports","views/admin/layouts/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{defaultWidth=20}e.default=s}),define("views/admin/layouts/kanban",["exports","views/admin/layouts/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name","link","align","view","isLarge","isMuted","hidden"];dataAttributesDefs={link:{type:"bool"},isLarge:{type:"bool"},isMuted:{type:"bool"},width:{type:"float"},align:{type:"enum",options:["left","right"]},view:{type:"varchar",readOnly:!0},name:{type:"varchar",readOnly:!0},hidden:{type:"bool"}};editable=!0;ignoreList=[];ignoreTypeList=[]}e.default=s}),define("views/admin/layouts/filters",["exports","views/admin/layouts/rows"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name"];editable=!1;ignoreList=[];setup(){super.setup(),this.wait(!0),this.loadLayout(()=>this.wait(!1))}loadLayout(n){this.getModelFactory().create(this.scope,o=>{this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{var t,a,s,i,l=[];for(t in o.defs.fields)this.checkFieldType(o.getFieldParam(t,"type"))&&this.isFieldEnabled(o,t)&&l.push(t);l.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),this.enabledFieldsList=[],this.enabledFields=[],this.disabledFields=[];for(a of e)this.enabledFields.push({name:a,labelText:this.getLanguage().translate(a,"fields",this.scope)}),this.enabledFieldsList.push(a);for(s of l)this.enabledFieldsList.includes(s)||this.disabledFields.push({name:s,labelText:this.getLanguage().translate(s,"fields",this.scope)});this.rowLayout=this.enabledFields;for(i of this.rowLayout)i.labelText=this.getLanguage().translate(i.name,"fields",this.scope);n()})})}fetch(){let a=[];return $("#layout ul.enabled > li").each((e,t)=>{a.push($(t).data("name"))}),a}checkFieldType(e){return this.getFieldManager().checkFilter(e)}validate(){return!0}isFieldEnabled(e,t){return-1===this.ignoreList.indexOf(t)&&!e.getFieldParam(t,"disabled")&&!e.getFieldParam(t,"utility")&&!e.getFieldParam(t,"layoutFiltersDisabled")}}e.default=s}),define("views/admin/layouts/detail-small",["exports","views/admin/layouts/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/admin/layouts/detail-convert",["exports","views/admin/layouts/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/admin/layouts/default-side-panel",["exports","views/admin/layouts/rows"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dataAttributeList=["name","view","customLabel"];dataAttributesDefs={view:{type:"varchar",readOnly:!0},customLabel:{type:"varchar",readOnly:!0},name:{type:"varchar",readOnly:!0}};editable=!1;languageCategory="fields";setup(){super.setup(),this.wait(!0),this.loadLayout(()=>{this.wait(!1)})}validate(){return!0}loadLayout(a){this.getModelFactory().create(Espo.Utils.hyphenToUpperCamelCase(this.scope),t=>{this.getHelper().layoutManager.getOriginal(this.scope,this.type,this.setId,e=>{this.readDataFromLayout(t,e),a&&a()})})}readDataFromLayout(e,i){var t,s=[];for(t in e.defs.fields)this.checkFieldType(e.getFieldParam(t,"type"))&&this.isFieldEnabled(e,t)&&s.push(t);s.sort((e,t)=>this.translate(e,"fields",this.scope).localeCompare(this.translate(t,"fields",this.scope))),~s.indexOf("assignedUser")&&s.unshift(":assignedUser"),this.enabledFieldsList=[],this.enabledFields=[],this.disabledFields=[];var l=[];let o=[];for(let s=0;s<i.length;s++){let e=i[s],t=(e="object"!=typeof e?{name:e}:e).name,a=(0===t.indexOf(":")&&(t=t.substr(1)),this.getLanguage().translate(t,"fields",this.scope));t!==e.name&&(a+=" *"),~l.indexOf(a)&&o.push(a),l.push(a),this.enabledFields.push({name:e.name,labelText:a}),this.enabledFieldsList.push(e.name)}for(let a=0;a<s.length;a++)if(!_.contains(this.enabledFieldsList,s[a])){let e=this.getLanguage().translate(s[a],"fields",this.scope);~l.indexOf(e)&&o.push(e),l.push(e);var n=s[a];let t=n;0===t.indexOf(":")&&(t=t.substr(1)),e=this.getLanguage().translate(t,"fields",this.scope),t!==n&&(e+=" *");var d={name:n,labelText:e},n=this.getMetadata().get(["entityDefs",this.scope,"fields",n,"type"]);n&&this.getMetadata().get(["fields",n,"notSortable"])&&(d.notSortable=!0),this.disabledFields.push(d)}this.enabledFields.forEach(e=>{~o.indexOf(e.label)&&(e.labelText+=" ("+e.name+")")}),this.disabledFields.forEach(e=>{~o.indexOf(e.label)&&(e.labelText+=" ("+e.name+")")}),this.rowLayout=i;for(let a in this.rowLayout){let t=this.getLanguage().translate(this.rowLayout[a].name,"fields",this.scope);this.enabledFields.forEach(e=>{e.name===this.rowLayout[a].name&&(t=e.labelText)}),this.rowLayout[a].labelText=t,this.itemsData[this.rowLayout[a].name]=Espo.Utils.cloneDeep(this.rowLayout[a])}}checkFieldType(e){return!0}isFieldEnabled(e,t){var a;return!~["modifiedAt","createdAt","modifiedBy","createdBy"].indexOf(t)&&!((a=e.getFieldParam(t,"layoutAvailabilityList"))&&!a.includes(this.type)||(e.getFieldParam(t,"layoutIgnoreList")||[]).includes(this.type)||e.getFieldParam(t,"disabled")||e.getFieldParam(t,"utility")||e.getFieldParam(t,"layoutDefaultSidePanelDisabled")||e.getFieldParam(t,"layoutDetailDisabled"))}}e.default=s}),define("views/admin/layouts/bottom-panels-edit-small",["exports","views/admin/layouts/bottom-panels-edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{viewType="editSmall"}e.default=s}),define("views/admin/layouts/bottom-panels-detail-small",["exports","views/admin/layouts/bottom-panels-detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{viewType="detailSmall"}e.default=s}),define("views/admin/layouts/record/edit-attributes",["exports","views/record/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/layouts/record/edit-attributes";mode="edit";data(){return{attributeDataList:this.getAttributeDataList()}}getAttributeDataList(){let s=[];return this.attributeList.forEach(e=>{var t=this.attributeDefs[e]||{},a=t.type,a=!["enum","bool","int","float","varchar"].includes(a)&&"widthComplex"!==e;s.push({name:e,viewKey:e+"Field",isWide:a,label:this.translate(t.label||e,"fields","LayoutManager")})}),s}setup(){super.setup(),this.attributeList=this.options.attributeList||[],this.attributeDefs=this.options.attributeDefs||{},this.attributeList.forEach(e=>{var t=this.attributeDefs[e]||{},a=t.type||"base",a=t.view||this.getFieldManager().getViewName(a);this.createField(e,a,t)})}}e.default=s}),define("views/admin/layouts/modals/panel-attributes",["exports","views/modal","model"],function(e,t,s){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=a(t),s=a(s);class i extends t.default{templateContent=`
        <div class="panel panel-default no-side-margin">
            <div class="panel-body">
                <div class="edit-container">{{{edit}}}</div>
            </div>
        </div>
    `;className="dialog dialog-record";shortcutKeys={"Control+Enter":function(e){document.activeElement instanceof HTMLInputElement&&document.activeElement.dispatchEvent(new Event("change",{bubbles:!0})),this.actionSave(),e.preventDefault(),e.stopPropagation()}};setup(){this.buttonList=[{name:"save",text:this.translate("Apply"),style:"primary"},{name:"cancel",label:"Cancel"}];var e=new s.default,t=(e.name="LayoutManager",e.set(this.options.attributes||{}),this.options.attributeList),a=this.options.attributeDefs;this.createView("edit","views/admin/layouts/record/edit-attributes",{selector:".edit-container",attributeList:t,attributeDefs:a,model:e,dynamicLogicDefs:this.options.dynamicLogicDefs})}actionSave(){var e=this.getView("edit"),t=e.fetch();if(e.model.set(t,{silent:!0}),!e.validate())return t=e.model.attributes,this.trigger("after:save",t),!0}}e.default=i}),define("views/admin/layouts/modals/edit-attributes",["exports","views/modal","model"],function(e,t,s){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=a(t),s=a(s);class i extends t.default{templateContent=`
        <div class="panel panel-default no-side-margin">
            <div class="panel-body">
                <div class="edit-container">{{{edit}}}</div>
            </div>
        </div>
    `;className="dialog dialog-record";shortcutKeys={"Control+Enter":function(e){this.actionSave(),e.preventDefault(),e.stopPropagation()}};setup(){this.buttonList=[{name:"save",text:this.translate("Apply"),style:"primary"},{name:"cancel",text:this.translate("Cancel")}];var e=new s.default;e.name="LayoutManager",e.set(this.options.attributes||{}),this.headerText=void 0,this.options.languageCategory&&(this.headerText=this.translate(this.options.name,this.options.languageCategory,this.options.scope));let t=Espo.Utils.clone(this.options.attributeList||[]),a=[];t.forEach(e=>{var t=this.options.attributeDefs[e]||{};t.readOnly||t.hidden||a.push(e)}),t=a,this.createView("edit","views/admin/layouts/record/edit-attributes",{selector:".edit-container",attributeList:t,attributeDefs:this.options.attributeDefs,dynamicLogicDefs:this.options.dynamicLogicDefs,model:e})}actionSave(){var e=this.getView("edit"),t=e.fetch();if(e.model.set(t,{silent:!0}),!e.validate())return t=e.model.attributes,this.trigger("after:save",t),!0}}e.default=i}),define("views/admin/layouts/fields/width-complex",["exports","views/fields/base","views/fields/enum","model","views/fields/float"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{editTemplateContent=`
        <div class="row">
            <div data-name="value" class="col-sm-6">{{{value}}}</div>
            <div data-name="unit" class="col-sm-6">{{{unit}}}</div>
        </div>

    `;getAttributeList(){return["width","widthPx"]}setup(){this.auxModel=new s.default,this.syncAuxModel(),this.listenTo(this.model,"change",(e,t)=>{t.ui||this.syncAuxModel()});var e=new a.default({name:"unit",mode:"edit",model:this.auxModel,params:{options:["%","px"]}}),t=this.valueView=new i.default({name:"value",mode:"edit",model:this.auxModel,params:{min:this.getMinValue(),max:this.getMaxValue()},labelText:this.translate("Value")});this.assignView("unit",e,'[data-name="unit"]'),this.assignView("value",t,'[data-name="value"]'),this.listenTo(this.auxModel,"change",(e,t)=>{t.ui&&(this.valueView.params.max=this.getMaxValue(),this.valueView.params.min=this.getMinValue(),this.model.set(this.fetch(),{ui:!0}))})}getMinValue(){return"px"===this.auxModel.attributes.unit?30:5}getMaxValue(){return"px"===this.auxModel.attributes.unit?768:95}validate(){return this.valueView.validate()}fetch(){return"px"===this.auxModel.attributes.unit?{width:null,widthPx:this.auxModel.attributes.value}:{width:this.auxModel.attributes.value,widthPx:null}}syncAuxModel(){var e=this.model.attributes.width,t=this.model.attributes.widthPx,a=e||!t?"%":"px";this.auxModel.set({unit:a,value:"px"==a?t:e})}}e.default=o}),define("views/admin/label-manager/index",["exports","view","ui/select"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/label-manager/index";scopeList=null;scope=null;language=null;languageList=null;events={'click [data-action="selectScope"]':function(e){let t=$(e.currentTarget).data("name");this.getRouter().checkConfirmLeaveOut(()=>{this.selectScope(t)})},'change select[data-name="language"]':function(e){let t=$(e.currentTarget).val();this.getRouter().checkConfirmLeaveOut(()=>{this.selectLanguage(t)})}};data(){return{scopeList:this.scopeList,languageList:this.languageList,scope:this.scope,language:this.language}}setup(){this.languageList=this.getMetadata().get(["app","language","list"])||["en_US"],this.languageList.sort((e,t)=>this.getLanguage().translateOption(e,"language").localeCompare(this.getLanguage().translateOption(t,"language"))),this.wait(!0),Espo.Ajax.postRequest("LabelManager/action/getScopeList").then(e=>{this.scopeList=e,this.scopeList.sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))),this.scopeList=this.scopeList.filter(e=>{if(!("Global"===e||this.getMetadata().get(["scopes",e])&&this.getMetadata().get(["scopes",e,"disabled"])))return!0}),this.scopeList.unshift("Global"),this.wait(!1)}),this.scope=this.options.scope||"Global",this.language=this.options.language||this.getConfig().get("language"),this.once("after:render",()=>{this.selectScope(this.scope,!0)})}afterRender(){a.default.init(this.element.querySelector('select[data-name="language"]'))}selectLanguage(e){this.language=e,this.scope?this.getRouter().navigate("#Admin/labelManager/scope="+this.scope+"&language="+this.language,{trigger:!1}):this.getRouter().navigate("#Admin/labelManager/language="+this.language,{trigger:!1}),this.createRecordView()}selectScope(e,t){this.scope=e,t||this.getRouter().navigate("#Admin/labelManager/scope="+e+"&language="+this.language,{trigger:!1}),this.$el.find('[data-action="selectScope"]').removeClass("disabled").removeAttr("disabled"),this.$el.find('[data-name="'+e+'"][data-action="selectScope"]').addClass("disabled").attr("disabled","disabled"),this.createRecordView()}createRecordView(){Espo.Ui.notifyWait(),this.createView("record","views/admin/label-manager/edit",{selector:".language-record",scope:this.scope,language:this.language},e=>{e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0)})}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Label Manager","labels","Admin"))}}e.default=i}),define("views/admin/label-manager/edit",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/label-manager/edit";scopeData;events={'click [data-action="toggleCategory"]':function(e){e=$(e.currentTarget).data("name");this.toggleCategory(e)},'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)},'click [data-action="showCategory"]':function(e){e=$(e.currentTarget).data("name");this.showCategory(e)},'click [data-action="hideCategory"]':function(e){e=$(e.currentTarget).data("name");this.hideCategory(e)},'click [data-action="cancel"]':function(){this.actionCancel()},'click [data-action="save"]':function(){this.actionSave()},"change input.label-value":function(e){var t=$(e.currentTarget).data("name"),e=$(e.currentTarget).val();this.setLabelValue(t,e)}};data(){return{categoryList:this.getCategoryList(),scope:this.scope}}setup(){this.scope=this.options.scope,this.language=this.options.language,this.categoryShownMap={},this.dirtyLabelList=[],this.wait(Espo.Ajax.postRequest("LabelManager/action/getScopeData",{scope:this.scope,language:this.language}).then(e=>{this.scopeData=e,this.scopeDataInitial=Espo.Utils.cloneDeep(this.scopeData),Object.keys(this.scopeData).forEach(e=>{this.createView(e,"views/admin/label-manager/category",{selector:`.panel-body[data-name="${e}"]`,categoryData:this.getCategoryData(e),scope:this.scope,language:this.language})})}))}getCategoryList(){return Object.keys(this.scopeData).sort((e,t)=>e.localeCompare(t))}setLabelValue(e,t){var a=e.split("[.]")[0];t=(t=t.replace(/\\\\n/i,"\n")).trim(),this.scopeData[a][e]=t,this.dirtyLabelList.push(e),this.setConfirmLeaveOut(!0),this.getCategoryView(a)&&(this.getCategoryView(a).categoryData[e]=t)}getCategoryView(e){return this.getView(e)}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}afterRender(){this.$save=this.$el.find('button[data-action="save"]'),this.$cancel=this.$el.find('button[data-action="cancel"]'),this.$panels=this.$el.find(".category-panel"),this.$noData=this.$el.find(".no-data")}actionSave(){this.$save.addClass("disabled").attr("disabled"),this.$cancel.addClass("disabled").attr("disabled");let a={};this.dirtyLabelList.forEach(e=>{var t=e.split("[.]")[0];a[e]=this.scopeData[t][e]}),Espo.Ui.notify(this.translate("saving","messages")),Espo.Ajax.postRequest("LabelManager/action/saveLabels",{scope:this.scope,language:this.language,labels:a}).then(e=>{for(var t in this.scopeDataInitial=Espo.Utils.cloneDeep(this.scopeData),this.dirtyLabelList=[],this.setConfirmLeaveOut(!1),this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled"),e){var a=t.split("[.]").splice(1).join("[.]");this.$el.find(`input.label-value[data-name="${a}"]`).val(e[t])}Espo.Ui.success(this.translate("Saved")),this.getHelper().broadcastChannel.postMessage("update:language"),this.getLanguage().loadSkipCache()}).catch(()=>{this.$save.removeClass("disabled").removeAttr("disabled"),this.$cancel.removeClass("disabled").removeAttr("disabled")})}actionCancel(){this.scopeData=Espo.Utils.cloneDeep(this.scopeDataInitial),this.dirtyLabelList=[],this.setConfirmLeaveOut(!1),this.getCategoryList().forEach(e=>{this.getCategoryView(e)&&(this.getCategoryView(e).categoryData=this.scopeData[e],this.getCategoryView(e).reRender())})}toggleCategory(e){this.categoryShownMap[e]?this.hideCategory(e):this.showCategory(e)}showCategory(e){this.$el.find(`a[data-action="showCategory"][data-name="${e}"]`).addClass("hidden"),this.$el.find(`a[data-action="hideCategory"][data-name="${e}"]`).removeClass("hidden"),this.$el.find(`.panel-body[data-name="${e}"]`).removeClass("hidden"),this.categoryShownMap[e]=!0}hideCategory(e){this.$el.find(`.panel-body[data-name="${e}"]`).addClass("hidden"),this.$el.find(`a[data-action="showCategory"][data-name="${e}"]`).removeClass("hidden"),this.$el.find(`a[data-action="hideCategory"][data-name="${e}"]`).addClass("hidden"),this.categoryShownMap[e]=!1}getCategoryData(e){return this.scopeData[e]||{}}processQuickSearch(e){if(e=e.trim()){let l=[],o={},n=e.toLowerCase(),d=!1;Object.keys(this.scopeData).forEach(i=>{o[i]=[],Object.keys(this.scopeData[i]).forEach(e=>{let t=!1;var a,s=this.scopeData[i][e];if(!(t=0!==s.toLowerCase().indexOf(n)&&0!==e.toLowerCase().indexOf(n)?t:!0))for(a of s.split(" ").concat(s.split(" ")))if(0===a.toLowerCase().indexOf(n)){t=!0;break}t&&(d=!0,o[i].push(e),l.includes(i)||l.push(i))})}),d?(this.$noData.addClass("hidden"),Object.keys(this.scopeData).forEach(a=>{let s=this.$panels.filter(`[data-name="${a}"]`);Object.keys(this.scopeData[a]).forEach(e=>{var t=s.find(`.row[data-name="${e}"]`);o[a].includes(e)?t.removeClass("hidden"):t.addClass("hidden")}),l.includes(a)?s.removeClass("hidden"):s.addClass("hidden")})):(this.$panels.addClass("hidden"),this.$panels.find(".row").addClass("hidden"),this.$noData.removeClass("hidden"))}else this.$panels.removeClass("hidden"),this.$panels.find(".row").removeClass("hidden"),this.$noData.addClass("hidden")}}e.default=s}),define("views/admin/label-manager/category",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/label-manager/category";events={};data(){return{categoryDataList:this.getCategoryDataList()}}setup(){this.scope=this.options.scope,this.language=this.options.language,this.categoryData=this.options.categoryData}getCategoryDataList(){var e=Object.keys(this.categoryData);e.sort((e,t)=>e.localeCompare(t));let s=[];return e.forEach(e=>{let t=this.categoryData[e];var a={name:e,value:t=(t=null===t?"":t).replace?t.replace(/\n/i,"\\n"):t},e=e.split("[.]");a.label=e.slice(1).join(" . "),s.push(a)}),s}}e.default=s}),define("views/admin/job/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{createButton=!1;setup(){super.setup(),this.getHelper().getAppParam("isRestrictedMode")&&!this.getUser().isSuperAdmin()||this.addMenuItem("buttons",{link:"#Admin/jobsSettings",text:this.translate("Settings","labels","Admin")})}getHeader(){return this.buildHeaderHtml([$("<a>").attr("href","#Admin").text(this.translate("Administration")),$("<span>").text(this.getLanguage().translate("Jobs","labels","Admin"))])}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Jobs","labels","Admin"))}}e.default=s}),define("views/admin/job/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{rowActionsView="views/record/row-actions/view-and-remove";massActionList=["remove"];forceSettings=!0}e.default=s}),define("views/admin/job/record/detail-small",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideView=null;isWide=!0}e.default=s}),define("views/admin/job/modals/detail",["exports","views/modals/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{editDisabled=!0;fullFormDisabled=!0}e.default=s}),define("views/admin/job/fields/name",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getValueForDisplay(){if("list"===this.mode||"detail"===this.mode||"listLink"===this.mode)return this.model.get("name")?this.model.get("name"):this.model.get("serviceName")+": "+this.model.get("methodName")}}e.default=s}),define("views/admin/integrations/oauth2",["exports","views/admin/integrations/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/integrations/oauth2";data(){var e=this.redirectUri||this.getConfig().get("siteUrl")+"?entryPoint=oauthCallback";return{...super.data(),redirectUri:e}}}e.default=s}),define("views/admin/integrations/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/integrations/index";integrationList;integration=null;data(){return{integrationDataList:this.getIntegrationDataList(),integration:this.integration}}setup(){this.addHandler("click","a.integration-link",(e,t)=>{this.openIntegration(t.dataset.name)}),this.integrationList=Object.keys(this.getMetadata().get("integrations")||{}).sort((e,t)=>this.translate(e,"titles","Integration").localeCompare(this.translate(t,"titles","Integration"))),this.integration=this.options.integration||null,this.integration&&this.createIntegrationView(this.integration),this.on("after:render",()=>{this.renderHeader(),this.integration||this.renderDefaultPage()})}getIntegrationDataList(){return this.integrationList.map(e=>({name:e,active:this.integration===e}))}createIntegrationView(e){var t=this.getMetadata().get(`integrations.${e}.view`)||"views/admin/integrations/"+Espo.Utils.camelCaseToHyphen(this.getMetadata().get(`integrations.${e}.authMethod`));return this.createView("content",t,{fullSelector:"#integration-content",integration:e})}async openIntegration(e){this.integration=e,this.getRouter().navigate("#Admin/integrations/name="+e,{trigger:!1}),Espo.Ui.notifyWait(),await this.createIntegrationView(e),this.renderHeader(),await this.reRender(),Espo.Ui.notify(!1),$(window).scrollTop(0)}afterRender(){this.$header=$("#integration-header")}renderDefaultPage(){this.$header.html("").hide();let e;e=this.integrationList.length?this.translate("selectIntegration","messages","Integration"):'<p class="lead">'+this.translate("noIntegrations","messages","Integration")+"</p>",$("#integration-content").html(e)}renderHeader(){this.integration?this.$header.show().html(this.translate(this.integration,"titles","Integration")):this.$header.html("")}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Integrations","labels","Admin"))}}e.default=s}),define("views/admin/integrations/google-maps",["exports","views/admin/integrations/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/admin/formula-sandbox/index",["exports","model","view"],function(e,s,t){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,s=a(s),t=a(t);class i extends t.default{template="admin/formula-sandbox/index";targetEntityType=null;storageKey="formulaSandbox";setup(){var e=[""].concat(this.getMetadata().getScopeEntityList().filter(e=>this.getMetadata().get(["scopes",e,"object"]))),t={script:null,targetId:null,targetType:null,output:null},a=(this.getSessionStorage().has(this.storageKey)&&(a=this.getSessionStorage().get(this.storageKey),t.script=a.script||null,t.targetId=a.targetId||null,t.targetName=a.targetName||null,t.targetType=a.targetType||null),this.model=new s.default);a.name="Formula",a.setDefs({fields:{targetType:{type:"enum",options:e,translation:"Global.scopeNames",view:"views/fields/entity-type"},target:{type:"link",entity:t.targetType},script:{type:"formula",view:"views/fields/formula"},output:{type:"text",readOnly:!0,displayRawText:!0,tooltip:!0},errorMessage:{type:"text",readOnly:!0,displayRawText:!0}}}),a.set(t),this.createRecordView(),this.listenTo(this.model,"change:targetType",(e,t,a)=>{a.ui&&setTimeout(()=>{this.targetEntityType=this.model.get("targetType"),this.model.set({targetId:null,targetName:null},{silent:!0});var e=Espo.Utils.cloneDeep(this.model.attributes);this.clearView("record"),this.model.set(e,{silent:!0}),this.model.defs.fields.target.entity=this.targetEntityType,this.createRecordView().then(e=>e.render())},10)}),this.listenTo(this.model,"run",()=>this.run()),this.listenTo(this.model,"change",(e,t)=>{t.ui&&(t={script:this.model.get("script"),targetType:this.model.get("targetType"),targetId:this.model.get("targetId"),targetName:this.model.get("targetName")},this.getSessionStorage().set(this.storageKey,t))})}createRecordView(){return this.createView("record","views/admin/formula-sandbox/record/edit",{selector:".record",model:this.model,targetEntityType:this.targetEntityType,confirmLeaveDisabled:!0,shortcutKeysEnabled:!0})}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Formula Sandbox","labels","Admin"))}run(){var e=this.model.get("script");this.model.set({output:null,errorMessage:null}),""===e||null===e?(this.model.set("output",null),Espo.Ui.warning(this.translate("emptyScript","messages","Formula"))):Espo.Ajax.postRequest("Formula/action/run",{expression:e,targetId:this.model.get("targetId"),targetType:this.model.get("targetType")}).then(t=>{this.model.set("output",t.output||null);let e=null;if(t.isSuccess||(e=t.message||null),this.model.set("errorMessage",e),t.isSuccess)Espo.Ui.success(this.translate("runSuccess","messages","Formula"));else if(t.isSyntaxError){let e=this.translate("checkSyntaxError","messages","Formula");t.message&&(e+=" "+t.message),void Espo.Ui.error(e)}else{let e=this.translate("runError","messages","Formula");t.message&&(e+=" "+t.message),Espo.Ui.error(e)}})}}e.default=i}),define("views/admin/formula-sandbox/record/edit",["exports","views/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{scriptAreaHeight=400;bottomView=null;sideView=null;dropdownItemList=[];isWide=!0;accessControlDisabled=!0;saveAndContinueEditingAction=!1;saveAndNewAction=!1;shortcutKeyCtrlEnterAction="run";setup(){this.scope="Formula",this.buttonList=[{name:"run",label:"Run",style:"danger",title:"Ctrl+Enter",onClick:()=>this.actionRun()}];this.detailLayout=[{rows:[[!1,{name:"targetType",labelText:this.translate("targetType","fields","Formula")},{name:"target",labelText:this.translate("target","fields","Formula")}]]},{rows:[[{name:"script",noLabel:!0,options:{targetEntityType:this.model.get("targetType"),height:this.scriptAreaHeight,additionalFunctionDataList:[{name:"output\\print",insertText:"output\\print(VALUE)"},{name:"output\\printLine",insertText:"output\\printLine(VALUE)"}]}}]]},{name:"output",rows:[[{name:"errorMessage",labelText:this.translate("error","fields","Formula")}],[{name:"output",labelText:this.translate("output","fields","Formula")}]]}],super.setup(),this.model.get("targetType")?this.showField("target"):this.hideField("target"),this.controlTargetTypeField(),this.listenTo(this.model,"change:targetId",()=>this.controlTargetTypeField()),this.controlOutputField(),this.listenTo(this.model,"change",()=>this.controlOutputField())}controlTargetTypeField(){this.model.get("targetId")?this.setFieldReadOnly("targetType"):this.setFieldNotReadOnly("targetType")}controlOutputField(){this.model.get("errorMessage")?this.showField("errorMessage"):this.hideField("errorMessage")}actionRun(){this.model.trigger("run")}}e.default=s}),define("views/admin/formula/modals/add-function",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/formula/modals/add-function";backdrop=!0;data(){var e=this.translate("formulaFunctions","messages","Admin").replace("{documentationUrl}",this.documentationUrl),e=this.getHelper().transformMarkdownText(e,{linksInNewTab:!0}).toString();return{functionDataList:this.functionDataList,text:e}}setup(){this.addActionHandler("add",(e,t)=>{this.trigger("add",t.dataset.value)}),this.headerText=this.translate("Function"),this.documentationUrl="https://docs.espocrm.com/administration/formula/",this.functionDataList=this.options.functionDataList||this.getMetadata().get("app.formula.functionList")||[]}}e.default=s}),define("views/admin/formula/modals/add-attribute",["exports","views/modal","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{templateContent='<div class="attribute" data-name="attribute">{{{attribute}}}</div>';backdrop=!0;setup(){this.headerText=this.translate("Attribute"),this.scope=this.options.scope;let t=new a.default;this.createView("attribute","views/admin/formula/fields/attribute",{selector:'[data-name="attribute"]',model:t,mode:"edit",scope:this.scope,defs:{name:"attribute",params:{}},attributeList:this.options.attributeList},e=>{this.listenTo(e,"change",()=>{var e=t.get("attribute")||[];e.length&&this.trigger("add",e[0])})})}}e.default=i}),define("views/admin/formula/fields/attribute",["exports","views/fields/multi-enum","ui/multi-select"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{setupOptions(){if(super.setupOptions(),this.options.attributeList)this.params.options=this.options.attributeList;else{let a=this.getFieldManager().getEntityTypeAttributeList(this.options.scope).concat(["id"]).sort(),s=this.getMetadata().get(["entityDefs",this.options.scope,"links"])||{},i=[];Object.keys(s).forEach(e=>{var t=s[e].type,a=s[e].entity;t&&a&&!s[e].disabled&&!s[e].utility&&~["belongsToParent","hasOne","belongsTo"].indexOf(t)&&i.push(e)}),i.sort(),i.forEach(t=>{var e=s[t].entity;this.getFieldManager().getEntityTypeAttributeList(e).sort().forEach(e=>{a.push(t+"."+e)})}),this.params.options=a}}afterRender(){super.afterRender(),this.$element&&a.default.focus(this.$element)}}e.default=i}),define("views/admin/field-manager/list",["exports","view","views/admin/field-manager/modals/view-details"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/field-manager/list";data(){return{scope:this.scope,fieldDefsArray:this.fieldDefsArray,typeList:this.typeList,hasAddField:this.hasAddField}}events={'click [data-action="removeField"]':function(e){e=$(e.currentTarget).data("name");this.removeField(e)},'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)}};setup(){this.addActionHandler("viewDetails",(e,t)=>this.viewDetails(t.dataset.name)),this.scope=this.options.scope,this.isCustomizable=!!this.getMetadata().get(`scopes.${this.scope}.customizable`)&&!1!==this.getMetadata().get(`scopes.${this.scope}.entityManager.fields`),this.hasAddField=!0;var e=this.getMetadata().get(["scopes",this.scope,"entityManager"])||{};"addField"in e&&(this.hasAddField=e.addField),this.wait(this.buildFieldDefs())}afterRender(){this.$noData=this.$el.find(".no-data"),this.$el.find('input[data-name="quick-search"]').focus()}buildFieldDefs(){return this.getModelFactory().create(this.scope).then(e=>{this.fields=e.defs.fields,this.fieldList=Object.keys(this.fields).sort(),this.fieldDefsArray=[],this.fieldList.forEach(e=>{var t=this.fields[e];this.fieldDefsArray.push({name:e,isCustom:t.isCustom||!1,type:t.type,label:this.translate(e,"fields",this.scope),isEditable:!t.customizationDisabled&&this.isCustomizable})})})}removeField(e){var t=this.translate("confirmRemove","messages","FieldManager").replace("{field}",e);this.confirm(t,()=>{Espo.Ui.notifyWait(),Espo.Ajax.deleteRequest("Admin/fieldManager/"+this.scope+"/"+e).then(()=>{Espo.Ui.success(this.translate("Removed")),this.$el.find(`tr[data-name="${e}"]`).remove(),this.getMetadata().loadSkipCache().then(()=>{this.buildFieldDefs().then(()=>(this.broadcastUpdate(),this.reRender())).then(()=>Espo.Ui.success(this.translate("Removed")))})})})}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata"),this.getHelper().broadcastChannel.postMessage("update:language")}processQuickSearch(e){e=e.trim();var t=this.$noData;if(t.addClass("hidden"),e){let a=[],s=e.toLowerCase();this.fieldDefsArray.forEach(e=>{let t=!1;(t=0!==e.label.toLowerCase().indexOf(s)&&0!==e.name.toLowerCase().indexOf(s)?t:!0)||e.label.split(" ").concat(e.label.split(" ")).forEach(e=>{0===e.toLowerCase().indexOf(s)&&(t=!0)}),t&&a.push(e.name)}),0===a.length?(this.$el.find("table tr.field-row").addClass("hidden"),t.removeClass("hidden")):this.fieldDefsArray.map(e=>e.name).forEach(e=>{var t=this.$el.find(`table tr.field-row[data-name="${e}"]`);~a.indexOf(e)?t.removeClass("hidden"):t.addClass("hidden")})}else this.$el.find("table tr.field-row").removeClass("hidden")}async viewDetails(e){e=new a.default({field:e,entityType:this.scope});await this.assignView("modal",e),await e.render()}}e.default=i}),define("views/admin/field-manager/index",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/field-manager/index";scopeList=null;scope=null;type=null;data(){return{scopeList:this.scopeList,scope:this.scope}}events={"click #scopes-menu a.scope-link":function(e){e=$(e.currentTarget).data("scope");this.openScope(e)},"click #fields-content a.field-link":function(e){e.preventDefault();var t=$(e.currentTarget).data("scope"),e=$(e.currentTarget).data("field");this.openField(t,e)},'click [data-action="addField"]':function(){this.createView("dialog","views/admin/field-manager/modals/add-field",{},e=>{e.render(),this.listenToOnce(e,"add-field",e=>{this.createField(this.scope,e)})})}};setup(){this.scopeList=[],Object.keys(this.getMetadata().get("scopes")).sort((e,t)=>this.translate(e,"scopeNamesPlural").localeCompare(this.translate(t,"scopeNamesPlural"))).forEach(e=>{this.getMetadata().get("scopes."+e+".entity")&&this.getMetadata().get("scopes."+e+".customizable")&&this.scopeList.push(e)}),this.scope=this.options.scope||null,this.field=this.options.field||null,this.on("after:render",()=>{this.scope?this.field?this.openField(this.scope,this.field):this.openScope(this.scope):this.renderDefaultPage()}),this.createView("header","views/admin/field-manager/header",{selector:"> .page-header",scope:this.scope,field:this.field})}openScope(e){this.scope=e,this.field=null,this.getHeaderView().setField(null),this.getRouter().navigate("#Admin/fieldManager/scope="+e,{trigger:!1}),Espo.Ui.notifyWait(),this.createView("content","views/admin/field-manager/list",{fullSelector:"#fields-content",scope:e},e=>{e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0)})}getHeaderView(){return this.getView("header")}openField(e,t){this.scope=e,this.field=t,this.getHeaderView().setField(t),this.getRouter().navigate("#Admin/fieldManager/scope="+e+"&field="+t,{trigger:!1}),Espo.Ui.notifyWait(),this.createView("content","views/admin/field-manager/edit",{fullSelector:"#fields-content",scope:e,field:t},e=>{e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0),this.listenTo(e,"after:save",()=>{Espo.Ui.success(this.translate("Saved"))})})}createField(e,t){this.scope=e,this.type=t,this.getRouter().navigate("#Admin/fieldManager/scope="+e+"&type="+t+"&create=true",{trigger:!1}),Espo.Ui.notifyWait(),this.createView("content","views/admin/field-manager/edit",{fullSelector:"#fields-content",scope:e,type:t},e=>{e.render(),Espo.Ui.notify(!1),$(window).scrollTop(0),e.once("after:save",()=>{if(this.openScope(this.scope),this.getMetadata().get(`scopes.${this.scope}.layouts`)){let e=this.translate("fieldCreatedAddToLayouts","messages","FieldManager").replace("{link}",`#Admin/layouts/scope=${this.scope}&em=true`);setTimeout(()=>{Espo.Ui.notify(e,"success",void 0,{closeButton:!0})},100)}else Espo.Ui.success(this.translate("Created"),{suppress:!0})})})}renderDefaultPage(){$("#fields-content").html(this.translate("selectEntityType","messages","Admin"))}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Field Manager","labels","Admin"))}}e.default=s}),define("views/admin/field-manager/header",["exports","view"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/field-manager/header";data(){return{scope:this.scope,field:this.field}}setup(){this.scope=this.options.scope,this.field=this.options.field}setField(e){this.field=e,this.isRendered()&&this.reRender()}}e.default=s}),define("views/admin/field-manager/edit",["exports","view","model"],function(e,t,s){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=a(t),s=a(s);class i extends t.default{template="admin/field-manager/edit";paramWithTooltipList=["audited","required","default","min","max","maxLength","after","before","readOnly","readOnlyAfterCreate"];globalRestriction=null;hasAnyGlobalRestriction=!1;globalRestrictionTypeList=["forbidden","internal","onlyAdmin","readOnly","nonAdminReadOnly"];model;paramList;data(){return{scope:this.scope,field:this.field,defs:this.defs,paramList:this.paramList,type:this.type,fieldList:this.fieldList,isCustom:this.defs.isCustom,isNew:this.isNew,hasDynamicLogicPanel:this.hasDynamicLogicPanel,hasResetToDefault:!this.defs.isCustom&&!this.entityTypeIsCustom&&!this.isNew}}events={'click button[data-action="close"]':function(){this.actionClose()},'click button[data-action="save"]':function(){this.save()},'click button[data-action="resetToDefault"]':function(){this.resetToDefault()},"keydown.form":function(e){var t=Espo.Utils.getKeyFromKeyEvent(e);"Control+KeyS"!==t&&"Control+Enter"!==t||(this.save(),e.preventDefault(),e.stopPropagation())}};setupFieldData(a){var e;this.defs={},this.fieldList=[],this.model=new s.default,this.model.name="Admin",this.model.urlRoot="Admin/fieldManager/"+this.scope,this.model.defs={fields:{name:{required:!0,maxLength:50},label:{required:!0},tooltipText:{}}},this.entityTypeIsCustom=!!this.getMetadata().get(["scopes",this.scope,"isCustom"]),this.globalRestriction={},this.isNew?(this.model.scope=this.scope,this.model.set("type",this.type)):(this.model.id=this.field,this.model.scope=this.scope,this.model.set("name",this.field),this.model.set("label",this.getLanguage().translate(this.field,"fields",this.scope)),this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"tooltip"])&&this.model.set("tooltipText",this.getLanguage().translate(this.field,"tooltips",this.scope)),this.globalRestriction=this.getMetadata().get(["entityAcl",this.scope,"fields",this.field])||{},(e=this.globalRestrictionTypeList.filter(e=>this.globalRestriction[e])).length&&(this.model.set("globalRestrictions",e),this.hasAnyGlobalRestriction=!0)),this.listenTo(this.model,"change:readOnly",()=>{this.readOnlyControl()});let i=!1;this.getModelFactory().create(this.scope,e=>{this.isNew||(this.type=e.getFieldType(this.field)),this.getMetadata().get(["scopes",this.scope,"hasPersonalData"])&&this.getMetadata().get(["fields",this.type,"personalData"])&&(this.hasPersonalData=!0),this.hasInlineEditDisabled=!["foreign","autoincrement"].includes(this.type)&&!this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"customizationInlineEditDisabledDisabled"]),this.hasTooltipText=!this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"customizationTooltipTextDisabled"]),new Promise(t=>{this.isNew?t():Espo.Ajax.getRequest("Admin/fieldManager/"+this.scope+"/"+this.field).then(e=>{this.defs=e,t()})}).then(()=>{let e=[],t=(this.paramList=[],Espo.Utils.clone(this.getFieldManager().getParamList(this.type)||[]));this.isNew||(this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"fieldManagerAdditionalParamList"])||[]).forEach(e=>{t.push(e)});let s=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"fieldManagerParamList"]);t.forEach(e=>{var t,a=e.name;s&&-1===s.indexOf(a)||"readOnly"===a&&this.globalRestriction&&this.globalRestriction.readOnly||("required"===a&&(i=!0),"createButton"===a&&["assignedUser","assignedUsers","teams","collaborators"].includes(this.field))||"autocompleteOnEmpty"===a&&["assignedUser"].includes(this.field)||(t="customization"+Espo.Utils.upperCaseFirst(a)+"Disabled",this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,t]))||(t="customization"+Espo.Utils.upperCaseFirst(a)+"View",(a=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,t]))&&(e.view=a),this.paramList.push(e))}),this.hasPersonalData&&this.paramList.push({name:"isPersonalData",type:"bool"}),this.hasInlineEditDisabled&&!this.globalRestriction.readOnly&&this.paramList.push({name:"inlineEditDisabled",type:"bool"}),this.hasTooltipText&&this.paramList.push({name:"tooltipText",type:"text",rowsMin:1,trim:!0}),s&&(this.paramList=this.paramList.filter(e=>-1!==s.indexOf(e.name))),this.paramList=this.paramList.filter(e=>!(this.globalRestriction.readOnly&&"required"===e.name)),(this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"customizationDisabled"])||this.globalRestriction.forbidden)&&(this.paramList=[]),this.hasAnyGlobalRestriction&&this.paramList.push({name:"globalRestrictions",type:"array",readOnly:!0,displayAsList:!0,translation:"FieldManager.options.globalRestrictions",options:this.globalRestrictionTypeList}),this.paramList.forEach(e=>{this.model.defs.fields[e.name]=e}),this.model.set(this.defs),this.isNew&&this.model.populateDefaults(),e.push(this.createFieldView("varchar","name",!this.isNew,{trim:!0})),e.push(this.createFieldView("varchar","label",null,{trim:!0})),this.hasDynamicLogicPanel=!1,e.push(this.setupDynamicLogicFields(i)),this.model.fetchedAttributes=this.model.getClonedAttributes(),this.paramList.forEach(t=>{if(!t.hidden){var a={};if(t.tooltip||~this.paramWithTooltipList.indexOf(t.name)){a.tooltip=!0;let e=t.name;"string"==typeof t.tooltip&&(e=t.tooltip),a.tooltipText=this.translate(e,"tooltips","FieldManager")}t.readOnlyNotNew&&!this.isNew&&(a.readOnly=!0),e.push(this.createFieldView(t.type,t.name,null,t,a))}}),Promise.all(e).then(()=>a())})}),this.listenTo(this.model,"change",(e,t)=>{t.ui&&this.setIsChanged()})}setup(){if(this.scope=this.options.scope,this.field=this.options.field,this.type=this.options.type,this.isNew=!this.field,!this.getMetadata().get(["scopes",this.scope,"customizable"])||!1===this.getMetadata().get(`scopes.${this.scope}.entityManager.fields`)||this.field&&this.getMetadata().get(`entityDefs.${this.scope}.fields.${this.field}.customizationDisabled`))throw Espo.Ui.notify(!1),new Espo.Exceptions.NotFound("Entity type is not customizable.");this.wait(!0),this.setupFieldData(()=>{this.wait(!1)})}setupDynamicLogicFields(e){var t,a,s=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field])||{};return s.disabled||s.dynamicLogicDisabled||s.layoutDetailDisabled||s.utility?Promise.resolve():(t=[],s.dynamicLogicVisibleDisabled||(a=this.getMetadata().get(["logicDefs",this.scope,"fields",this.field,"visible"]),this.model.set("dynamicLogicVisible",a),t.push(this.createFieldView(null,"dynamicLogicVisible",null,{view:"views/admin/field-manager/fields/dynamic-logic-conditions",scope:this.scope})),this.hasDynamicLogicPanel=!0),a=this.getMetadata().get(["fields",this.type,"readOnly"]),s.dynamicLogicRequiredDisabled||a||!e||(e=this.getMetadata().get(["logicDefs",this.scope,"fields",this.field,"required"]),this.model.set("dynamicLogicRequired",e),t.push(this.createFieldView(null,"dynamicLogicRequired",null,{view:"views/admin/field-manager/fields/dynamic-logic-conditions",scope:this.scope})),this.hasDynamicLogicPanel=!0),s.dynamicLogicReadOnlyDisabled||a||(e=this.getMetadata().get(["logicDefs",this.scope,"fields",this.field,"readOnly"]),this.model.set("dynamicLogicReadOnly",e),t.push(this.createFieldView(null,"dynamicLogicReadOnly",null,{view:"views/admin/field-manager/fields/dynamic-logic-conditions",scope:this.scope})),this.hasDynamicLogicPanel=!0),this.getMetadata().get(["fields",this.type,"dynamicLogicOptions"])&&!s.dynamicLogicOptionsDisabled&&(e=this.getMetadata().get(["logicDefs",this.scope,"options",this.field]),this.model.set("dynamicLogicOptions",e),t.push(this.createFieldView(null,"dynamicLogicOptions",null,{view:"views/admin/field-manager/fields/dynamic-logic-options",scope:this.scope})),this.hasDynamicLogicPanel=!0),s.dynamicLogicInvalidDisabled||a||(e=this.getMetadata().get(["logicDefs",this.scope,"fields",this.field,"invalid"]),this.model.set("dynamicLogicInvalid",e),t.push(this.createFieldView(null,"dynamicLogicInvalid",null,{view:"views/admin/field-manager/fields/dynamic-logic-conditions",scope:this.scope})),this.hasDynamicLogicPanel=!0),s.dynamicLogicReadOnlySavedDisabled||a||(e=this.getMetadata().get(["logicDefs",this.scope,"fields",this.field,"readOnlySaved"]),this.model.set("dynamicLogicReadOnlySaved",e),t.push(this.createFieldView(null,"dynamicLogicReadOnlySaved",null,{view:"views/admin/field-manager/fields/dynamic-logic-conditions",scope:this.scope})),this.hasDynamicLogicPanel=!0),Promise.all(t))}afterRender(){this.getView("name").on("change",()=>{let e=this.model.get("name"),t=e;t.length&&(t=t.charAt(0).toUpperCase()+t.slice(1)),this.model.set("label",t),e&&(e=e.replace(/-/g,"").replace(/_/g,"").replace(/[^\w\s]/gi,"").replace(/ (.)/g,(e,t)=>t.toUpperCase()).replace(" ","")).length&&(e=e.charAt(0).toLowerCase()+e.slice(1)),this.model.set("name",e)})}readOnlyControl(){this.model.get("readOnly")?(this.hideField("dynamicLogicReadOnly"),this.hideField("dynamicLogicRequired"),this.hideField("dynamicLogicOptions"),this.hideField("dynamicLogicInvalid"),this.hideField("dynamicLogicPreSave")):(this.showField("dynamicLogicReadOnly"),this.showField("dynamicLogicRequired"),this.showField("dynamicLogicOptions"),this.showField("dynamicLogicInvalid"),this.showField("dynamicLogicPreSave"))}hideField(t){var e=()=>{var e=this.getView(t);e&&(this.$el.find('.cell[data-name="'+t+'"]').addClass("hidden"),e.setDisabled())};this.isRendered()?e():this.once("after:render",e)}showField(t){var e=()=>{var e=this.getView(t);e&&(this.$el.find('.cell[data-name="'+t+'"]').removeClass("hidden"),e.setNotDisabled())};this.isRendered()?e():this.once("after:render",e)}createFieldView(e,t,a,s,i,l){e=(s||{}).view||this.getFieldManager().getViewName(e),s={model:this.model,selector:`.field[data-name="${t}"]`,defs:{name:t,params:s},mode:a?"detail":"edit",readOnly:a,scope:this.scope,field:this.field},_.extend(s,i||{}),a=this.createView(t,e,s,l);return this.fieldList.push(t),a}disableButtons(){this.$el.find('[data-action="save"]').attr("disabled","disabled").addClass("disabled"),this.$el.find('[data-action="resetToDefault"]').attr("disabled","disabled").addClass("disabled")}enableButtons(){this.$el.find('[data-action="save"]').removeAttr("disabled").removeClass("disabled"),this.$el.find('[data-action="resetToDefault"]').removeAttr("disabled").removeClass("disabled")}save(){this.disableButtons(),this.fieldList.forEach(e=>{e=this.getView(e);e.readOnly||e.fetchToModel()});let t=!1;if(this.fieldList.forEach(e=>{e=this.getView(e);t=e.validate()||t}),t)Espo.Ui.error(this.translate("Not valid")),this.enableButtons();else{this.model.get("tooltipText")&&""!==this.model.get("tooltipText")?this.model.set("tooltip",!0):this.model.set("tooltip",!1);let e=()=>{Espo.Ui.notify(!1),this.setIsNotChanged(),this.enableButtons(),Promise.all([this.getMetadata().loadSkipCache(),this.getLanguage().loadSkipCache()]).then(()=>this.trigger("after:save")),this.model.fetchedAttributes=this.model.getClonedAttributes(),this.broadcastUpdate()};var a;Espo.Ui.notifyWait(),(this.isNew?this.model.save():(a=this.model.getClonedAttributes(),this.model.fetchedAttributes.label===a.label&&delete a.label,this.model.fetchedAttributes.tooltipText!==a.tooltipText&&(this.model.fetchedAttributes.tooltipText||a.tooltipText)||delete a.tooltipText,"translatedOptions"in a&&_.isEqual(this.model.fetchedAttributes.translatedOptions,a.translatedOptions)&&delete a.translatedOptions,this.model.save(a,{patch:!0}))).then(()=>e()).catch(()=>this.enableButtons())}}resetToDefault(){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ui.notify(this.translate("pleaseWait","messages")),Espo.Ajax.postRequest("FieldManager/action/resetToDefault",{scope:this.scope,name:this.field}).then(()=>{Promise.all([this.getMetadata().loadSkipCache(),this.getLanguage().loadSkipCache()]).then(()=>{this.setIsNotChanged(),this.setupFieldData(()=>{Espo.Ui.success(this.translate("Done")),this.reRender(),this.broadcastUpdate()})})})})}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata"),this.getHelper().broadcastChannel.postMessage("update:language"),this.getHelper().broadcastChannel.postMessage("update:settings")}actionClose(){this.setIsNotChanged(),this.getRouter().navigate("#Admin/fieldManager/scope="+this.scope,{trigger:!0})}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}setIsChanged(){this.isChanged=!0,this.setConfirmLeaveOut(!0)}setIsNotChanged(){this.isChanged=!1,this.setConfirmLeaveOut(!1)}}e.default=i}),define("views/admin/field-manager/modals/add-field",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{backdrop=!0;template="admin/field-manager/modals/add-field";data(){return{typeList:this.typeList}}setup(){this.addActionHandler("addField",(e,t)=>this.addField(t.dataset.type)),this.addHandler("keyup",'input[data-name="quick-search"]',(e,t)=>{this.processQuickSearch(t.value)}),this.headerText=this.translate("Add Field","labels","Admin"),this.typeList=[];let t=this.getMetadata().get("fields");Object.keys(this.getMetadata().get("fields")).forEach(e=>{e in t&&!t[e].notCreatable&&this.typeList.push(e)}),this.typeDataList=this.typeList.map(e=>({type:e,label:this.translate(e,"fieldTypes","Admin")})),this.typeList.sort((e,t)=>this.translate(e,"fieldTypes","Admin").localeCompare(this.translate(t,"fieldTypes","Admin")))}addField(e){this.trigger("add-field",e),this.remove()}afterRender(){this.$noData=this.$el.find(".no-data"),this.typeList.forEach(e=>{var t=this.translate(e,"fieldInfo","FieldManager"),a=this.$el.find('a.info[data-name="'+e+'"]');t===e?a.addClass("hidden"):(t=this.getHelper().transformMarkdownText(t,{linksInNewTab:!0}).toString(),Espo.Ui.popover(a,{content:t,placement:"left"},this))}),setTimeout(()=>this.$el.find('input[data-name="quick-search"]').focus(),50)}processQuickSearch(e){e=e.trim();var s=this.$noData;if(s.addClass("hidden"),e){let a=[],t=e.toLowerCase();this.typeDataList.forEach(e=>{0!==e.label.toLowerCase().indexOf(t)&&0!==e.type.toLowerCase().indexOf(t)||a.push(e.type)}),0===a.length?(this.$el.find("ul .list-group-item").addClass("hidden"),s.removeClass("hidden")):this.typeDataList.forEach(e=>{var t=this.$el.find(`ul .list-group-item[data-name="${e.type}"]`);~a.indexOf(e.type)?t.removeClass("hidden"):t.addClass("hidden")})}else this.$el.find("ul .list-group-item").removeClass("hidden")}}e.default=s}),define("views/admin/field-manager/fields/source-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=Espo.Utils.clone(this.getMetadata().get("entityDefs.Attachment.sourceList")||[]),this.translatedOptions={},this.params.options.forEach(e=>{this.translatedOptions[e]=this.translate(e,"scopeNamesPlural")})}}e.default=s}),define("views/admin/field-manager/fields/pattern",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{noSpellCheck=!0;setupOptions(){let t=this.getMetadata().get(["app","regExpPatterns"])||{};var e=Object.keys(t).filter(e=>!t[e].isSystem).map(e=>"$"+e);this.setOptionList(e)}}e.default=s}),define("views/admin/field-manager/fields/options-with-style",["exports","views/admin/field-manager/fields/options"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.optionsStyleMap=this.model.get("style")||{},this.styleList=["default","success","danger","warning","info","primary"],this.addActionHandler("selectOptionItemStyle",(e,t)=>{var a=t.dataset.style,t=t.dataset.value;this.changeStyle(t,a)})}changeStyle(e,t){var a=CSS.escape(e);this.$el.find(`[data-action="selectOptionItemStyle"][data-value="${a}"] .check-icon`).addClass("hidden"),this.$el.find(`[data-action="selectOptionItemStyle"][data-value="${a}"][data-style="${t}"] .check-icon`).removeClass("hidden");let s=this.$el.find(`.list-group-item[data-value="${a}"]`).find(".item-text");this.styleList.forEach(e=>{s.removeClass("text-"+e)}),s.addClass("text-"+t),this.optionsStyleMap[e]=t="default"===t?null:t}getItemHtml(s){var e=super.getItemHtml(s),t=this.styleList;let i=this.optionsStyleMap,l="default",o=[];t.forEach(e=>{let t=!0;i[s]===e?(l=e,t=!1):"default"!==e||i[s]||(t=!1);var a=this.getLanguage().translateOption(e,"style","LayoutManager"),e=$("<li>").append($("<a>").attr("role","button").attr("tabindex","0").attr("data-action","selectOptionItemStyle").attr("data-style",e).attr("data-value",s).append($("<span>").addClass("check-icon fas fa-check pull-right").addClass(t?"hidden":""),$("<div>").addClass("text-"+e).text(a)));o.push(e)});t=$("<div>").addClass("btn-group pull-right").append($("<button>").addClass("btn btn-link btn-sm dropdown-toggle").attr("type","button").attr("data-toggle","dropdown").append($("<span>").addClass("caret")),$("<ul>").addClass("dropdown-menu pull-right").append(o)),e=$(e);return e.find(".item-content > input").after(t),e.find(".item-text").addClass("text-"+l),e.addClass("link-group-item-with-columns"),e.get(0).outerHTML}fetch(){let t=super.fetch();return t.style={},(t.options||[]).forEach(e=>{t.style[e]=this.optionsStyleMap[e]||null}),t}}e.default=s}),define("views/admin/field-manager/fields/options-reference",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{enumFieldTypeList=["enum","multiEnum","array","checklist","varchar"];setupOptions(){this.params.options=[""];var e=Object.keys(this.getMetadata().get(["entityDefs"])).filter(e=>this.getMetadata().get(["scopes",e,"object"])).sort((e,t)=>this.getLanguage().translate(e,"scopesName").localeCompare(this.getLanguage().translate(t,"scopesName")));this.translatedOptions={},e.forEach(l=>{Object.keys(this.getMetadata().get(["entityDefs",l,"fields"])||[]).filter(e=>l!==this.model.scope||e!==this.model.get("name")).sort((e,t)=>this.getLanguage().translate(e,"fields",l).localeCompare(this.getLanguage().translate(t,"fields",l))).forEach(e=>{var{type:t,options:a,optionsPath:s,optionsReference:i}=this.getMetadata().get(["entityDefs",l,"fields",e])||{};!this.enumFieldTypeList.includes(t)||s||i||a&&(this.params.options.push(t=l+"."+e),this.translatedOptions[t]=this.translate(l,"scopeName")+" · "+this.translate(e,"fields",l))})})}}e.default=s}),define("views/admin/field-manager/fields/not-actual-options",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.params.options=Espo.Utils.clone(this.model.get("options"))||[],this.listenTo(this.model,"change:options",e=>{this.params.options=Espo.Utils.clone(e.get("options"))||[],this.reRender()})}}e.default=s}),define("views/admin/field-manager/fields/entity-list",["exports","views/fields/entity-type-list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/admin/field-manager/fields/dynamic-logic-options",["exports","views/fields/base","model"],function(e,t,s){function a(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=a(t),s=a(s);class i extends t.default{editTemplate="admin/field-manager/fields/dynamic-logic-options/edit";data(){return{itemDataList:this.itemDataList}}setup(){this.addActionHandler("editConditions",(e,t)=>this.edit(parseInt(t.dataset.index))),this.addActionHandler("removeOptionList",(e,t)=>this.removeItem(parseInt(t.dataset.index))),this.addActionHandler("addOptionList",()=>this.addOptionList()),this.optionsDefsList=Espo.Utils.cloneDeep(this.model.get(this.name))||[],this.scope=this.options.scope,this.setupItems(),this.setupItemViews()}setupItems(){this.itemDataList=[],this.optionsDefsList.forEach((e,t)=>{this.itemDataList.push({conditionGroupViewKey:"conditionGroup"+t.toString(),optionsViewKey:"options"+t.toString(),index:t})})}setupItemViews(){this.optionsDefsList.forEach((e,t)=>{this.createStringView(t),this.createOptionsView(t)})}createOptionsView(a){var e="options"+a.toString();if(this.optionsDefsList[a]){let t=new s.default;t.set("options",this.optionsDefsList[a].optionList||[]),this.createView(e,"views/fields/multi-enum",{selector:`.options-container[data-key="${e}"]`,model:t,name:"options",mode:"edit",params:{options:this.model.get("options"),translatedOptions:this.model.get("translatedOptions")}},e=>{this.isRendered()&&e.render(),this.listenTo(this.model,"change:options",()=>{e.setTranslatedOptions(this.getTranslatedOptions()),e.setOptionList(this.model.get("options"))}),this.listenTo(t,"change",()=>{this.optionsDefsList[a].optionList=t.get("options")||[]})})}}getTranslatedOptions(){if(this.model.get("translatedOptions"))return this.model.get("translatedOptions");let t={};return(this.model.get("options")||[]).forEach(e=>{t[e]=this.getLanguage().translateOption(e,this.options.field,this.options.scope)}),t}createStringView(e){var t="conditionGroup"+e.toString();this.optionsDefsList[e]&&this.createView(t,"views/admin/dynamic-logic/conditions-string/group-base",{selector:`.string-container[data-key="${t}"]`,itemData:{value:this.optionsDefsList[e].conditionGroup},operator:"and",scope:this.scope},e=>{this.isRendered()&&e.render()})}edit(t){this.createView("modal","views/admin/dynamic-logic/modals/edit",{conditionGroup:this.optionsDefsList[t].conditionGroup,scope:this.options.scope},e=>{e.render(),this.listenTo(e,"apply",e=>{this.optionsDefsList[t].conditionGroup=e,this.trigger("change"),this.createStringView(t)})})}addOptionList(){this.optionsDefsList.push({optionList:this.model.get("options")||[],conditionGroup:null}),this.setupItems(),this.reRender(),this.setupItemViews(),this.trigger("change")}removeItem(e){this.optionsDefsList.splice(e,1),this.setupItems(),this.reRender(),this.setupItemViews(),this.trigger("change")}fetch(){var e={};return e[this.name]=this.optionsDefsList,this.optionsDefsList.length||(e[this.name]=null),e}}e.default=i}),define("views/admin/field-manager/fields/dynamic-logic-conditions",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplate="admin/field-manager/fields/dynamic-logic-conditions/detail";editTemplate="admin/field-manager/fields/dynamic-logic-conditions/edit";data(){return{isSet:this.model.has(this.name),isNotEmpty:this.conditionGroup&&this.conditionGroup.length}}setup(){this.addActionHandler("editConditions",()=>this.edit()),this.conditionGroup=Espo.Utils.cloneDeep((this.model.get(this.name)||{}).conditionGroup||[]),this.scope=this.params.scope||this.options.scope,this.createStringView()}createStringView(){this.createView("conditionGroup","views/admin/dynamic-logic/conditions-string/group-base",{selector:".top-group-string-container",itemData:{value:this.conditionGroup},operator:"and",scope:this.scope},e=>{this.isRendered()&&e.render()})}edit(){this.createView("modal","views/admin/dynamic-logic/modals/edit",{conditionGroup:this.conditionGroup,scope:this.scope},e=>{e.render(),this.listenTo(e,"apply",e=>{this.conditionGroup=e,this.trigger("change"),this.createStringView()})})}fetch(){var e={};return e[this.name]={conditionGroup:this.conditionGroup},0===this.conditionGroup.length&&(e[this.name]=null),e}}e.default=s}),define("views/admin/field-manager/fields/currency-default",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetchEmptyValueAsNull=!0;setupOptions(){this.params.options=[""],(this.getConfig().get("currencyList")||[]).forEach(e=>{this.params.options.push(e)})}}e.default=s}),define("views/admin/field-manager/fields/text/attachment-field",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){var e=this.options.scope,e=(this.params.translation=e+".fields",this.getFieldManager().getEntityTypeFieldList(e,{typeList:["attachmentMultiple"]}));this.setOptionList(["",...e])}}e.default=s}),define("views/admin/field-manager/fields/phone/default",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.setOptionList(this.model.get("typeList")||[""]),this.listenTo(this.model,"change:typeList",()=>{this.setOptionList(this.model.get("typeList")||[""])})}}e.default=s}),define("views/admin/field-manager/fields/options/default",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.validations.push(()=>this.validateListed()),this.updateAvailableOptions(),this.listenTo(this.model,"change",()=>{(this.model.hasChanged("options")||this.model.hasChanged("optionsReference"))&&this.updateAvailableOptions()})}updateAvailableOptions(){this.setOptionList(this.getAvailableOptions())}getAvailableOptions(){var e,t=this.model.get("optionsReference");return t?([t,e]=t.split("."),t=this.getMetadata().get(`entityDefs.${t}.fields.${e}.options`)||[""],Espo.Utils.clone(t)):this.model.get("options")||[""]}validateListed(){var e=this.model.get(this.name)??"";return!!this.params.options&&-1===this.getAvailableOptions().indexOf(e)&&(e=this.translate("fieldInvalid","messages").replace("{field}",this.getLabelText()),this.showValidationMessage(e),!0)}}e.default=s}),define("views/admin/field-manager/fields/options/default-multi",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.validations.push(()=>this.validateListed()),this.updateAvailableOptions(),this.listenTo(this.model,"change",()=>{(this.model.hasChanged("options")||this.model.hasChanged("optionsReference"))&&this.updateAvailableOptions()})}updateAvailableOptions(){this.setOptionList(this.getAvailableOptions())}getAvailableOptions(){var e,t=this.model.get("optionsReference");return t?([t,e]=t.split("."),t=this.getMetadata().get(`entityDefs.${t}.fields.${e}.options`)||[],Espo.Utils.clone(t)):this.model.get("options")||[]}validateListed(){var e=this.model.get(this.name)??[];if(this.params.options){var t,a,s=this.getAvailableOptions();for(t of e)if(-1===s.indexOf(t))return a=this.translate("fieldInvalid","messages").replace("{field}",this.getLabelText()),this.showValidationMessage(a),!0}return!1}}e.default=s}),define("views/admin/field-manager/fields/link-multiple/default",["exports","views/fields/link-multiple"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{data(){var e=this.model.get("defaultAttributes")||{},t=e[this.options.field+"Names"]||{},e=e[this.options.field+"Ids"]||[],a=super.data();return a.nameHash=t,a.idValues=e,a}setup(){super.setup(),this.foreignScope=this.getMetadata().get(["entityDefs",this.options.scope,"links",this.options.field,"entity"])}fetch(){var e=super.fetch();let t={};return t[this.options.field+"Ids"]=e[this.idsName],t[this.options.field+"Names"]=e[this.nameHashName],{defaultAttributes:t=null!==e[this.idsName]&&0!==e[this.idsName].length?t:null}}copyValuesFromModel(){var e=this.model.get("defaultAttributes")||{},t=e[this.options.field+"Ids"]||[],e=e[this.options.field+"Names"]||{};this.ids=t,this.nameHash=e}}e.default=s}),define("views/admin/field-manager/fields/link/default",["exports","views/fields/link"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{data(){var e=this.model.get("defaultAttributes")||{},t=e[this.options.field+"Name"]||null,e=e[this.options.field+"Id"]||null,a=super.data();return a.nameValue=t,a.idValue=e,a}setup(){super.setup(),this.foreignScope=this.getMetadata().get(["entityDefs",this.options.scope,"links",this.options.field,"entity"])}fetch(){var e=super.fetch();let t={};return t[this.options.field+"Id"]=e[this.idName],t[this.options.field+"Name"]=e[this.nameName],{defaultAttributes:t=null===e[this.idName]?null:t}}}e.default=s}),define("views/admin/field-manager/fields/int/max",["exports","views/fields/int"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupAutoNumericOptions(){super.setupAutoNumericOptions(),null==this.params.max&&(this.autoNumericOptions.maximumValue="9223372036854775807"),null==this.params.min&&(this.autoNumericOptions.minimumValue="-9223372036854775808")}}e.default=s}),define("views/admin/field-manager/fields/foreign/link",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.model.isNew()||this.setReadOnly(!0)}setupOptions(){let t=this.getMetadata().get(["entityDefs",this.options.scope,"links"])||{},a=(this.params.options=Object.keys(Espo.Utils.clone(t)).filter(e=>{if(!("belongsTo"!==t[e].type&&"hasOne"!==t[e].type||t[e].noJoin||t[e].disabled||t[e].utility))return!0}),this.options.scope);this.translatedOptions={},this.params.options.forEach(e=>{this.translatedOptions[e]=this.translate(e,"links",a)}),this.params.options=this.params.options.sort((e,t)=>this.translate(e,"links",a).localeCompare(this.translate(t,"links",a))),this.params.options.unshift("")}}e.default=s}),define("views/admin/field-manager/fields/foreign/field",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.model.isNew()||this.setReadOnly(!0),this.listenTo(this.model,"change:field",()=>{this.manageField()}),this.viewValue=this.model.get("view")}setupOptions(){this.listenTo(this.model,"change:link",()=>{this.setupOptionsByLink(),this.reRender()}),this.setupOptionsByLink()}setupOptionsByLink(){this.typeList=this.getMetadata().get(["fields","foreign","fieldTypeList"]);var e=this.model.get("link");if(e){let s=this.getMetadata().get(["entityDefs",this.options.scope,"links",e,"entity"]);if(s){let a=this.getMetadata().get(["entityDefs",s,"fields"])||{};this.params.options=Object.keys(Espo.Utils.clone(a)).filter(e=>{var t=a[e].type;if(~this.typeList.indexOf(t)&&!(a[e].disabled||a[e].utility||a[e].directAccessDisabled||a[e].notStorable))return!0}),this.translatedOptions={},this.params.options.forEach(e=>{this.translatedOptions[e]=this.translate(e,"fields",s)}),this.params.options=this.params.options.sort((e,t)=>this.translate(e,"fields",s).localeCompare(this.translate(t,"fields",s))),this.params.options.unshift("")}else this.params.options=[""]}else this.params.options=[""]}manageField(){var e,t;this.model.isNew()&&(t=this.model.get("link"),e=this.model.get("field"),t)&&e&&(t=this.getMetadata().get(["entityDefs",this.options.scope,"links",t,"entity"]))&&(t=this.getMetadata().get(["entityDefs",t,"fields",e,"type"]),this.viewValue=this.getMetadata().get(["fields","foreign","fieldTypeViewMap",t]))}fetch(){var e=super.fetch();return this.model.isNew()&&this.viewValue&&(e.view=this.viewValue),e}}e.default=s}),define("views/admin/field-manager/fields/date/default",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetch(){var e=super.fetch();return""===e[this.name]&&(e[this.name]=null),e}setupOptions(){super.setupOptions();var e=this.model.get(this.name);this.params.options&&e&&!~this.params.options.indexOf(e)&&this.params.options.push(e)}}e.default=s}),define("views/admin/field-manager/fields/date/after-before",["exports","views/fields/varchar"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){if(super.setupOptions(),this.options.scope){let e=this.getFieldManager().getEntityTypeFieldList(this.options.scope,{typeList:["date","datetime","datetimeOptional"]});this.model.get("name")&&(e=e.filter(e=>e!==this.model.get("name"))),this.params.options=e}}}e.default=s}),define("views/admin/extensions/ready",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/extensions/ready";cssName="ready-modal";createButton=!0;data(){return{version:this.upgradeData.version,text:this.translate("installExtension","messages","Admin").replace("{version}",this.upgradeData.version).replace("{name}",this.upgradeData.name)}}setup(){this.buttonList=[{name:"run",text:this.translate("Install","labels","Admin"),style:"danger",onClick:()=>this.actionRun()},{name:"cancel",label:"Cancel"}],this.upgradeData=this.options.upgradeData,this.headerText=this.getLanguage().translate("Ready for installation","labels","Admin")}actionRun(){this.trigger("run"),this.remove()}}e.default=s}),define("views/admin/extensions/index",["exports","view","helpers/list/select-provider"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/extensions/index";packageContents=null;events={'change input[name="package"]':function(e){this.$el.find('button[data-action="upload"]').addClass("disabled").attr("disabled","disabled"),this.$el.find(".message-container").html("");e=e.currentTarget.files;e.length&&this.selectFile(e[0])},'click button[data-action="upload"]':function(){this.upload()},'click [data-action="install"]':function(e){var e=$(e.currentTarget).data("id"),t=this.collection.get(e).get("name"),a=this.collection.get(e).get("version");this.run(e,t,a)},'click [data-action="uninstall"]':function(e){let t=$(e.currentTarget).data("id");this.confirm(this.translate("uninstallConfirmation","messages","Admin"),()=>{Espo.Ui.notify(this.translate("Uninstalling...","labels","Admin")),Espo.Ajax.postRequest("Extension/action/uninstall",{id:t},{timeout:0,bypassAppReload:!0}).then(()=>{Espo.Ui.success(this.translate("Done")),setTimeout(()=>window.location.reload(),500)}).catch(e=>{e=e.getResponseHeader("X-Status-Reason");this.showErrorNotification(this.translate("Error")+": "+e)})})}};setup(){let e=new a.default(this.getHelper().layoutManager,this.getHelper().metadata,this.getHelper().fieldManager);this.wait(this.getCollectionFactory().create("Extension").then(e=>{this.collection=e,this.collection.maxSize=this.getConfig().get("recordsPerPage")}).then(()=>e.get("Extension")).then(e=>{this.collection.data.select=e.join(",")}).then(()=>this.collection.fetch()).then(()=>{this.createView("list","views/extension/record/list",{collection:this.collection,selector:"> .list-container"}),0===this.collection.length&&this.once("after:render",()=>{this.$el.find(".list-container").addClass("hidden")})}))}selectFile(t){var e=new FileReader;e.onload=e=>{this.packageContents=e.target.result,this.$el.find('button[data-action="upload"]').removeClass("disabled").removeAttr("disabled");var e=this.getHelper().getAppParam("maxUploadSize")||0;t.size>1024*e*1024&&(e=this.translate("fileExceedsMaxUploadSize","messages","Extension").replace("{maxSize}",e+"MB"),Espo.Ui.dialog({body:this.getHelper().transformMarkdownText(e).toString(),buttonList:[{name:"close",text:this.translate("Close"),onClick:e=>e.close()}]}).show())},e.readAsDataURL(t)}showError(e){e=this.translate(e,"errors","Admin"),this.$el.find(".message-container").html(e)}showErrorNotification(e){e?(e=this.translate(e,"errors","Admin"),this.$el.find(".notify-text").html(e),this.$el.find(".notify-text").removeClass("hidden")):this.$el.find(".notify-text").addClass("hidden")}upload(){this.$el.find('button[data-action="upload"]').addClass("disabled").attr("disabled","disabled"),Espo.Ui.notify(this.translate("Uploading...")),Espo.Ajax.postRequest("Extension/action/upload",this.packageContents,{timeout:0,contentType:"application/zip"}).then(t=>{t.id?(Espo.Ui.notify(!1),this.createView("popup","views/admin/extensions/ready",{upgradeData:t},e=>{e.render(),this.$el.find('button[data-action="upload"]').removeClass("disabled").removeAttr("disabled"),e.once("run",()=>{e.close(),this.$el.find(".panel.upload").addClass("hidden"),this.run(t.id,t.version,t.name)})})):this.showError(this.translate("Error occurred"))}).catch(e=>{this.showError(e.getResponseHeader("X-Status-Reason")),Espo.Ui.notify(!1)})}run(e,t,a){Espo.Ui.notify(this.translate("pleaseWait","messages")),this.showError(!1),this.showErrorNotification(!1),Espo.Ajax.postRequest("Extension/action/install",{id:e},{timeout:0,bypassAppReload:!0}).then(()=>{var e=this.getCache();e&&e.clear(),this.createView("popup","views/admin/extensions/done",{version:t,name:a},e=>{this.collection.length&&this.collection.fetch({bypassAppReload:!0}),this.$el.find(".list-container").removeClass("hidden"),this.$el.find(".panel.upload").removeClass("hidden"),Espo.Ui.notify(!1),e.render()})}).catch(e=>{this.$el.find(".panel.upload").removeClass("hidden");e=e.getResponseHeader("X-Status-Reason");this.showErrorNotification(this.translate("Error")+": "+e)})}}e.default=i}),define("views/admin/extensions/done",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/extensions/done";cssName="done-modal";createButton=!0;data(){return{version:this.options.version,name:this.options.name,text:this.translate("extensionInstalled","messages","Admin").replace("{version}",this.options.version).replace("{name}",this.options.name)}}setup(){this.on("remove",()=>{window.location.reload()}),this.buttonList=[{name:"close",label:"Close"}],this.headerText=this.getLanguage().translate("Installed successfully","labels","Admin")}}e.default=s}),define("views/admin/entity-manager/scope",["exports","view","views/record/detail","model","views/admin/entity-manager/fields/primary-filters"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{template="admin/entity-manager/scope";scope;data(){return{scope:this.scope,isEditable:this.isEditable,isRemovable:this.isRemovable,isCustomizable:this.isCustomizable,type:this.type,hasLayouts:this.hasLayouts,label:this.label,hasFormula:this.hasFormula,hasFields:this.hasFields,hasRelationships:this.hasRelationships}}events={'click [data-action="editEntity"]':function(){this.getRouter().navigate("#Admin/entityManager/edit&scope="+this.scope,{trigger:!0})},'click [data-action="removeEntity"]':function(){this.removeEntity()},'click [data-action="editFormula"]':function(){this.editFormula()}};setup(){this.scope=this.options.scope,this.setupScopeData(),this.model=new s.default({name:this.scope,type:this.type,label:this.label,primaryFilters:this.getPrimaryFilters()}),this.model.setDefs({fields:{name:{type:"varchar"},type:{type:"varchar"},label:{type:"varchar"},primaryFilters:{type:"array"}}}),this.recordView=new a.default({model:this.model,inlineEditDisabled:!0,buttonsDisabled:!0,readOnly:!0,detailLayout:[{tabBreak:!0,tabLabel:this.translate("General","labels","Settings"),rows:[[{name:"name",labelText:this.translate("name","fields","EntityManager")},{name:"type",labelText:this.translate("type","fields","EntityManager")}],[{name:"label",labelText:this.translate("label","fields","EntityManager")},!1]]},{tabBreak:!0,tabLabel:this.translate("Details"),rows:[[{view:new i.default({name:"primaryFilters",labelText:this.translate("primaryFilters","fields","EntityManager"),targetEntityType:this.scope})},!1]]}]}),this.assignView("record",this.recordView,".record-container"),this.type||this.recordView.hideField("type")}setupScopeData(){var e=this.getMetadata().get(["scopes",this.scope]),t=this.getMetadata().get(["scopes",this.scope,"entityManager"])||{};if(!e)throw new Espo.Exceptions.NotFound;this.isRemovable=!!e.isCustom,e.isNotRemovable&&(this.isRemovable=!1),this.isCustomizable=!!e.customizable,this.type=e.type,this.isEditable=!0,this.hasLayouts=e.layouts,this.hasFormula=this.isCustomizable,this.hasFields=this.isCustomizable,this.hasRelationships=this.isCustomizable,e.customizable||(this.isEditable=!1),"edit"in t&&(this.isEditable=t.edit),"layouts"in t&&(this.hasLayouts=t.layouts),"formula"in t&&(this.hasFormula=t.formula),"fields"in t&&(this.hasFields=t.fields),"relationships"in t&&(this.hasRelationships=t.relationships),this.label=this.getLanguage().translate(this.scope,"scopeNames")}editFormula(){Espo.Ui.notifyWait(),Espo.loader.requirePromise("views/admin/entity-manager/modals/select-formula").then(e=>{let t=new e({scope:this.scope});this.assignView("dialog",t).then(()=>{Espo.Ui.notify(!1),t.render()})})}removeEntity(){let e=this.scope;this.confirm(this.translate("confirmRemove","messages","EntityManager"),()=>{Espo.Ui.notify(this.translate("pleaseWait","messages")),this.disableButtons(),Espo.Ajax.postRequest("EntityManager/action/removeEntity",{name:e}).then(()=>{this.getMetadata().loadSkipCache().then(()=>{this.getConfig().load().then(()=>{Espo.Ui.notify(!1),this.broadcastUpdate(),this.getRouter().navigate("#Admin/entityManager",{trigger:!0})})})}).catch(()=>this.enableButtons())})}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Entity Manager","labels","Admin"))}disableButtons(){this.$el.find(".btn.action").addClass("disabled").attr("disabled","disabled"),this.$el.find(".item-dropdown-button").addClass("disabled").attr("disabled","disabled")}enableButtons(){this.$el.find(".btn.action").removeClass("disabled").removeAttr("disabled"),this.$el.find('.item-dropdown-button"]').removeClass("disabled").removeAttr("disabled")}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata"),this.getHelper().broadcastChannel.postMessage("update:settings")}getPrimaryFilters(){var e=this.getMetadata().get(`clientDefs.${this.scope}.filterList`,[]).map(e=>"object"==typeof e&&e.name?e.name:e.toString());return this.getMetadata().get(`scopes.${this.scope}.stars`)&&e.unshift("starred"),e}}e.default=o}),define("views/admin/entity-manager/index",["exports","view","views/admin/entity-manager/modals/export"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/entity-manager/index";scopeDataList=null;scope=null;data(){return{scopeDataList:this.scopeDataList}}events={'click button[data-action="createEntity"]':function(){this.getRouter().navigate("#Admin/entityManager/create&",{trigger:!0})},'keyup input[data-name="quick-search"]':function(e){this.processQuickSearch(e.currentTarget.value)}};setupScopeData(){this.scopeDataList=[];let e=Object.keys(this.getMetadata().get("scopes")).sort((e,t)=>e.localeCompare(t)),a=[];e.forEach(e=>{var t=this.getMetadata().get("scopes."+e);t.entity&&t.customizable&&a.push(e)}),e.forEach(e=>{var t=this.getMetadata().get("scopes."+e);t.entity&&!t.customizable&&a.push(e)}),(e=a).forEach(e=>{var t=this.getMetadata().get("scopes."+e);let a=!!t.isCustom;t.isNotRemovable&&(a=!1),this.scopeDataList.push({name:e,isCustom:t.isCustom,isRemovable:a,hasView:t.customizable,type:t.type,label:this.getLanguage().translate(e,"scopeNames"),layouts:t.layouts,module:"Crm"!==t.module?t.module:null})})}setup(){this.setupScopeData(),this.addActionHandler("export",()=>this.actionExport())}afterRender(){this.$noData=this.$el.find(".no-data"),this.$el.find('input[data-name="quick-search"]').focus()}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Entity Manager","labels","Admin"))}processQuickSearch(e){e=e.trim();var t=this.$noData;if(t.addClass("hidden"),e){let a=[],s=e.toLowerCase();this.scopeDataList.forEach(e=>{let t=!1;(t=0!==e.label.toLowerCase().indexOf(s)&&0!==e.name.toLowerCase().indexOf(s)?t:!0)||e.label.split(" ").concat(e.label.split(" ")).forEach(e=>{0===e.toLowerCase().indexOf(s)&&(t=!0)}),t&&a.push(e.name)}),0===a.length?(this.$el.find("table tr.scope-row").addClass("hidden"),t.removeClass("hidden")):this.scopeDataList.map(e=>e.name).forEach(e=>{~a.indexOf(e)?this.$el.find('table tr.scope-row[data-scope="'+e+'"]').removeClass("hidden"):this.$el.find('table tr.scope-row[data-scope="'+e+'"]').addClass("hidden")})}else this.$el.find("table tr.scope-row").removeClass("hidden")}actionExport(){let e=new a.default;this.assignView("dialog",e).then(()=>{e.render()})}}e.default=i}),define("views/admin/entity-manager/formula",["exports","view","model","views/admin/entity-manager/record/edit-formula","underscore"],function(e,t,a,s,i){function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=l(t),a=l(a),s=l(s),i=l(i);class o extends t.default{template="admin/entity-manager/formula";scope;attributes;data(){return{scope:this.scope,type:this.type}}setup(){if(this.addActionHandler("save",()=>this.actionSave()),this.addActionHandler("close",()=>this.actionClose()),this.addActionHandler("resetToDefault",()=>this.actionResetToDefault()),this.addHandler("keydown.form","","onKeyDown"),this.scope=this.options.scope,this.type=this.options.type,!this.scope||!this.type)throw Error("No scope or type.");if(!this.getMetadata().get(["scopes",this.scope,"customizable"])||!1===this.getMetadata().get(`scopes.${this.scope}.entityManager.formula`))throw new Espo.Exceptions.NotFound("Entity type is not customizable.");if(!["beforeSaveCustomScript","beforeSaveApiScript"].includes(this.type))throw Espo.Ui.error("No allowed formula type.",!0),new Espo.Exceptions.NotFound("No allowed formula type specified.");this.model=new a.default,this.model.name="EntityManager",this.wait(this.loadFormula().then(()=>{this.recordView=new s.default({model:this.model,targetEntityType:this.scope,type:this.type}),this.assignView("record",this.recordView,".record")})),this.listenTo(this.model,"change",(e,t)=>{t.ui&&this.setIsChanged()})}async loadFormula(){await Espo.Ajax.getRequest("Metadata/action/get",{key:"formula."+this.scope}).then(e=>{this.model.set(this.type,(e=e||{})[this.type]||null),this.updateAttributes()})}afterRender(){this.$save=this.$el.find('[data-action="save"]')}disableButtons(){this.$save.addClass("disabled").attr("disabled","disabled")}enableButtons(){this.$save.removeClass("disabled").removeAttr("disabled")}updateAttributes(){this.attributes=Espo.Utils.clone(this.model.attributes)}actionSave(){var e=this.recordView.fetch();i.default.isEqual(e,this.attributes)?Espo.Ui.warning(this.translate("notModified","messages")):this.recordView.validate()||(this.disableButtons(),Espo.Ui.notifyWait(),Espo.Ajax.postRequest("EntityManager/action/formula",{data:e,scope:this.scope}).then(()=>{Espo.Ui.success(this.translate("Saved")),this.enableButtons(),this.setIsNotChanged(),this.updateAttributes()}).catch(()=>this.enableButtons()))}actionClose(){this.setIsNotChanged(),this.getRouter().navigate("#Admin/entityManager/scope="+this.scope,{trigger:!0})}async actionResetToDefault(){await this.confirm(this.translate("confirmation","messages")),this.disableButtons(),Espo.Ui.notifyWait();try{await Espo.Ajax.postRequest("EntityManager/action/resetFormulaToDefault",{scope:this.scope,type:this.type})}catch(e){return void this.enableButtons()}await this.loadFormula(),await this.recordView.reRender(),this.enableButtons(),this.setIsNotChanged(),Espo.Ui.success(this.translate("Done"))}setConfirmLeaveOut(e){this.getRouter().confirmLeaveOut=e}setIsChanged(){this.isChanged=!0,this.setConfirmLeaveOut(!0)}setIsNotChanged(){this.isChanged=!1,this.setConfirmLeaveOut(!1)}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Formula","labels","EntityManager"))}onKeyDown(e){var t=Espo.Utils.getKeyFromKeyEvent(e);"Control+KeyS"!==t&&"Control+Enter"!==t||(this.actionSave(),e.preventDefault(),e.stopPropagation())}}e.default=o}),define("views/admin/entity-manager/edit",["exports","view","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{template="admin/entity-manager/edit";additionalParams;defaultParamLocation="scopes";data(){return{isNew:this.isNew,scope:this.scope}}setupData(){let i=this.scope;var e=this.getMetadata().get(["scopes",i,"type"])||null;if(this.hasStreamField=!0,i&&(this.hasStreamField=this.getMetadata().get(["scopes",i,"customizable"])&&this.getMetadata().get(["scopes",i,"object"])||!1),"User"===i&&(this.hasStreamField=!1),this.hasColorField=!this.getConfig().get("scopeColorsDisabled"),i)for(var t in this.additionalParams=Espo.Utils.cloneDeep({...this.getMetadata().get(["app","entityManagerParams","Global"]),...this.getMetadata().get(["app","entityManagerParams","@"+(e||"_")]),...this.getMetadata().get(["app","entityManagerParams",i])}),this.model.set("name",i),this.model.set("labelSingular",this.translate(i,"scopeNames")),this.model.set("labelPlural",this.translate(i,"scopeNamesPlural")),this.model.set("type",this.getMetadata().get("scopes."+i+".type")||""),this.model.set("stream",this.getMetadata().get("scopes."+i+".stream")||!1),this.model.set("disabled",this.getMetadata().get("scopes."+i+".disabled")||!1),this.model.set("sortBy",this.getMetadata().get("entityDefs."+i+".collection.orderBy")),this.model.set("sortDirection",this.getMetadata().get("entityDefs."+i+".collection.order")),this.model.set("textFilterFields",this.getMetadata().get(["entityDefs",i,"collection","textFilterFields"])||["name"]),this.model.set("fullTextSearch",this.getMetadata().get(["entityDefs",i,"collection","fullTextSearch"])||!1),this.model.set("countDisabled",this.getMetadata().get(["entityDefs",i,"collection","countDisabled"])||!1),this.model.set("statusField",this.getMetadata().get("scopes."+i+".statusField")||null),this.hasColorField&&this.model.set("color",this.getMetadata().get(["clientDefs",i,"color"])||null),this.model.set("iconClass",this.getMetadata().get(["clientDefs",i,"iconClass"])||null),this.model.set("kanbanViewMode",this.getMetadata().get(["clientDefs",i,"kanbanViewMode"])||!1),this.model.set("kanbanStatusIgnoreList",this.getMetadata().get(["scopes",i,"kanbanStatusIgnoreList"])||[]),this.additionalParams){var a=this.additionalParams[t],s=a.location||this.defaultParamLocation,l="bool"!==a.fieldDefs.type&&null,a=a.param||t,s=this.getMetadata().get([s,i,a])||l;this.model.set(t,s)}if(i){let t=this.getMetadata().get("entityDefs."+i+".fields")||{};this.orderableFieldList=Object.keys(t).filter(e=>!!this.getFieldManager().isEntityTypeFieldAvailable(i,e)&&!t[e].orderDisabled).sort((e,t)=>this.translate(e,"fields",i).localeCompare(this.translate(t,"fields",i))),this.sortByTranslation={},this.orderableFieldList.forEach(e=>{this.sortByTranslation[e]=this.translate(e,"fields",i)}),this.filtersOptionList=this.getTextFiltersOptionList(i),this.textFilterFieldsTranslation={},this.filtersOptionList.forEach(e=>{var t,a,s;~e.indexOf(".")?(t=e.split(".")[0],a=e.split(".")[1],s=this.getMetadata().get(["entityDefs",i,"links",t,"entity"]),this.textFilterFieldsTranslation[e]=this.translate(t,"links",i)+" . "+this.translate(a,"fields",s)):this.textFilterFieldsTranslation[e]=this.translate(e,"fields",i)}),this.enumFieldList=Object.keys(t).filter(e=>{if(!t[e].disabled)return"enum"===t[e].type||void 0}).sort((e,t)=>this.translate(e,"fields",i).localeCompare(this.translate(t,"fields",i))),this.translatedStatusFields={},this.enumFieldList.forEach(e=>{this.translatedStatusFields[e]=this.translate(e,"fields",i)}),this.enumFieldList.unshift(""),this.translatedStatusFields[""]="-"+this.translate("None")+"-",this.statusOptionList=[],this.translatedStatusOptions={}}if(this.detailLayout=[{rows:[[{name:"name"},{name:"type",options:{tooltipText:this.translate("entityType","tooltips","EntityManager")}}],[{name:"labelSingular"},{name:"labelPlural"}],[{name:"iconClass"},{name:"color"}],[{name:"disabled"},{name:"stream"}],[{name:"sortBy",options:{translatedOptions:this.sortByTranslation}},{name:"sortDirection"}],[{name:"textFilterFields",options:{translatedOptions:this.textFilterFieldsTranslation}},{name:"statusField",options:{translatedOptions:this.translatedStatusFields}}],[{name:"fullTextSearch"},{name:"countDisabled"}],[{name:"kanbanViewMode"},{name:"kanbanStatusIgnoreList",options:{translatedOptions:this.translatedStatusOptions}}]]}],this.scope){var e=[],o=[];let t=Object.keys(this.additionalParams).filter(e=>!!this.getMetadata().get(["app","entityManagerParams","Global",e]));var n=Object.keys(this.additionalParams).filter(e=>!t.includes(e)),d=function(s,i){i.forEach((e,t)=>{t%2==0&&s.push([]);var a=s[s.length-1];a.push({name:e}),t===i.length-1&&1===a.length&&a.push(!1)})};d(e,t),d(o,n),e.length&&this.detailLayout.push({rows:e}),o.length&&this.detailLayout.push({rows:o})}}setup(){var e=this.scope=this.options.scope||!1;if(this.isNew=!e,this.model=new a.default,this.model.name="EntityManager",this.isNew||(this.isCustom=this.getMetadata().get(["scopes",e,"isCustom"])),this.scope&&(!this.getMetadata().get(`scopes.${e}.customizable`)||!1===this.getMetadata().get(`scopes.${e}.entityManager.edit`)))throw new Espo.Exceptions.NotFound("The entity type is not customizable.");this.setupData(),this.setupDefs(),this.model.fetchedAttributes=this.model.getClonedAttributes(),this.createRecordView()}setupDefs(){var e,t=this.scope,a={fields:{type:{type:"enum",required:!0,options:this.getMetadata().get("app.entityTemplateList")||["Base"],readOnly:!1!==t,tooltip:!0},stream:{type:"bool",required:!0,tooltip:!0},disabled:{type:"bool",tooltip:!0},name:{type:"varchar",required:!0,trim:!0,maxLength:64,readOnly:!1!==t},labelSingular:{type:"varchar",required:!0,trim:!0},labelPlural:{type:"varchar",required:!0,trim:!0},color:{type:"varchar",view:"views/fields/colorpicker"},iconClass:{type:"varchar",view:"views/admin/entity-manager/fields/icon-class"},sortBy:{type:"enum",options:this.orderableFieldList},sortDirection:{type:"enum",options:["asc","desc"]},fullTextSearch:{type:"bool",tooltip:!0},countDisabled:{type:"bool",tooltip:!0},kanbanViewMode:{type:"bool"},textFilterFields:{type:"multiEnum",options:this.filtersOptionList,tooltip:!0},statusField:{type:"enum",options:this.enumFieldList,tooltip:!0},kanbanStatusIgnoreList:{type:"multiEnum",options:this.statusOptionList}}};for(e in this.getMetadata().get(["scopes",this.scope,"statusFieldLocked"])&&(a.fields.statusField.readOnly=!0),this.additionalParams)a.fields[e]=this.additionalParams[e].fieldDefs;this.model.setDefs(a)}createRecordView(){return this.createView("record","views/admin/entity-manager/record/edit",{selector:".record",model:this.model,detailLayout:this.detailLayout,isNew:this.isNew,hasColorField:this.hasColorField,hasStreamField:this.hasStreamField,isCustom:this.isCustom,subjectEntityType:this.scope,shortcutKeysEnabled:!0}).then(e=>{this.listenTo(e,"save",()=>this.actionSave()),this.listenTo(e,"cancel",()=>this.actionCancel()),this.listenTo(e,"reset-to-default",()=>this.actionResetToDefault())})}hideField(e){this.getRecordView().hideField(e)}showField(e){this.getRecordView().showField(e)}toPlural(e){return"y"===e.slice(-1)?e.substr(0,e.length-1)+"ies":"s"===e.slice(-1)?e+"es":e+"s"}afterRender(){this.getFieldView("name").on("change",()=>{let e=this.model.get("name");e=e.charAt(0).toUpperCase()+e.slice(1),this.model.set("labelSingular",e),this.model.set("labelPlural",this.toPlural(e)),e&&(e=e.replace(/-/g," ").replace(/_/g," ").replace(/[^\w\s]/gi,"").replace(/ (.)/g,(e,t)=>t.toUpperCase()).replace(" ","")).length&&(e=e.charAt(0).toUpperCase()+e.slice(1)),this.model.set("name",e)})}actionSave(){let e=["name","type","labelSingular","labelPlural","disabled","statusField","iconClass"],s=(this.hasStreamField&&e.push("stream"),this.scope&&(e.push("sortBy"),e.push("sortDirection"),e.push("kanbanViewMode"),e.push("kanbanStatusIgnoreList"),e=e.concat(Object.keys(this.additionalParams))),this.hasColorField&&e.push("color"),Espo.Utils.cloneDeep(this.model.fetchedAttributes)||{}),t=!1;if(e.forEach(e=>{this.getFieldView(e)&&"edit"===this.getFieldView(e).mode&&this.getFieldView(e).fetchToModel()}),e.forEach(e=>{this.getFieldView(e)&&"edit"===this.getFieldView(e).mode&&(t=this.getFieldView(e).validate()||t)}),!t){this.disableButtons();let e="EntityManager/action/createEntity";this.scope&&(e="EntityManager/action/updateEntity");let a={name:this.model.get("name"),labelSingular:this.model.get("labelSingular"),labelPlural:this.model.get("labelPlural"),type:this.model.get("type"),stream:this.model.get("stream"),disabled:this.model.get("disabled"),textFilterFields:this.model.get("textFilterFields"),fullTextSearch:this.model.get("fullTextSearch"),countDisabled:this.model.get("countDisabled"),statusField:this.model.get("statusField"),iconClass:this.model.get("iconClass")};if(this.hasColorField&&(a.color=this.model.get("color")||null),""===a.statusField&&(a.statusField=null),this.scope)for(var i in a.sortBy=this.model.get("sortBy"),a.sortDirection=this.model.get("sortDirection"),a.kanbanViewMode=this.model.get("kanbanViewMode"),a.kanbanStatusIgnoreList=this.model.get("kanbanStatusIgnoreList"),this.additionalParams){var l=this.additionalParams[i].fieldDefs.type;this.getFieldManager().getAttributeList(l,i).forEach(e=>{a[e]=this.model.get(e)})}this.isNew||(this.model.fetchedAttributes.labelPlural===a.labelPlural&&delete a.labelPlural,this.model.fetchedAttributes.labelSingular===a.labelSingular&&delete a.labelSingular),Espo.Ui.notify(this.translate("pleaseWait","messages")),Espo.Ajax.postRequest(e,a).then(t=>{this.model.fetchedAttributes=this.model.getClonedAttributes(),this.scope?Espo.Ui.success(this.translate("Saved")):Espo.Ui.success(this.translate("entityCreated","messages","EntityManager")),this.getMetadata().loadSkipCache().then(()=>Promise.all([this.getConfig().load(),this.getLanguage().loadSkipCache()])).then(()=>{var e=a.fullTextSearch&&!s.fullTextSearch;this.broadcastUpdate(),e&&this.createView("dialog","views/modal",{templateContent:"{{complexText viewObject.options.msg}}{{complexText viewObject.options.msgRebuild}}",headerText:this.translate("rebuildRequired","strings","Admin"),backdrop:"static",msg:this.translate("rebuildRequired","messages","Admin"),msgRebuild:"```php rebuild.php```",buttonList:[{name:"close",label:this.translate("Close")}]}).then(e=>e.render()),this.enableButtons(),this.getRecordView().setIsNotChanged(),this.isNew&&this.getRouter().navigate("#Admin/entityManager/scope="+t.name,{trigger:!0})})}).catch(()=>{this.enableButtons()})}}actionCancel(){this.getRecordView().setConfirmLeaveOut(!1),this.isNew?this.getRouter().navigate("#Admin/entityManager",{trigger:!0}):this.getRouter().navigate("#Admin/entityManager/scope="+this.scope,{trigger:!0})}actionResetToDefault(){this.confirm(this.translate("confirmation","messages"),()=>{Espo.Ui.notify(this.translate("pleaseWait","messages")),this.disableButtons(),Espo.Ajax.postRequest("EntityManager/action/resetToDefault",{scope:this.scope}).then(()=>{this.getMetadata().loadSkipCache().then(()=>this.getLanguage().loadSkipCache()).then(()=>{this.setupData(),this.model.fetchedAttributes=this.model.getClonedAttributes(),Espo.Ui.success(this.translate("Done")),this.enableButtons(),this.broadcastUpdate(),this.getRecordView().setIsNotChanged()})})})}getRecordView(){return this.getView("record")}getTextFiltersOptionList(s){let a=this.getMetadata().get(["entityDefs",s,"fields"])||{},i=Object.keys(a).filter(e=>{var t=a[e].type;return!!this.getMetadata().get(["fields",t,"textFilter"])&&!!this.getFieldManager().isEntityTypeFieldAvailable(s,e)&&!this.getMetadata().get(["entityDefs",s,"fields",e,"textFilterDisabled"])});i.unshift("id");var e=Object.keys(this.getMetadata().get(["entityDefs",s,"links"])||{});return e.sort((e,t)=>this.translate(e,"links",s).localeCompare(this.translate(t,"links",s))),e.forEach(t=>{var e=this.getMetadata().get(["entityDefs",s,"links",t,"type"]);if("belongsTo"===e){let a=this.getMetadata().get(["entityDefs",s,"links",t,"entity"]);a&&"Attachment"!==a&&(e=this.getMetadata().get(["entityDefs",a,"fields"])||{},(e=Object.keys(e)).sort((e,t)=>this.translate(e,"fields",a).localeCompare(this.translate(t,"fields",a))),e.filter(e=>{var t=this.getMetadata().get(["entityDefs",a,"fields",e,"type"]);return!!this.getMetadata().get(["fields",t,"textFilter"])&&!(!this.getMetadata().get(["fields",t,"textFilterForeign"])||!this.getFieldManager().isEntityTypeFieldAvailable(a,e)||this.getMetadata().get(["entityDefs",a,"fields",e,"textFilterDisabled"])||this.getMetadata().get(["entityDefs",a,"fields",e,"foreignAccessDisabled"]))}).forEach(e=>{i.push(t+"."+e)}))}}),i}getFieldView(e){return this.getRecordView().getFieldView(e)}disableButtons(){this.getRecordView().disableActionItems()}enableButtons(){this.getRecordView().enableActionItems()}broadcastUpdate(){this.getHelper().broadcastChannel.postMessage("update:metadata"),this.getHelper().broadcastChannel.postMessage("update:language"),this.getHelper().broadcastChannel.postMessage("update:config")}}e.default=i}),define("views/admin/entity-manager/record/edit",["exports","views/record/edit"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{bottomView=null;sideView=null;dropdownItemList=[];accessControlDisabled=!0;saveAndContinueEditingAction=!1;saveAndNewAction=!1;shortcutKeys={"Control+Enter":"save","Control+KeyS":"save"};setup(){this.isCreate=this.options.isNew,this.scope="EntityManager",this.subjectEntityType=this.options.subjectEntityType,this.isCreate?this.buttonList=[{name:"save",style:"danger",label:"Create",onClick:()=>this.actionSave()},{name:"cancel",label:"Cancel"}]:this.buttonList=[{name:"save",style:"danger",label:"Save",onClick:()=>this.actionSave()},{name:"cancel",label:"Cancel"}],this.isCreate||this.options.isCustom||this.buttonList.push({name:"resetToDefault",text:this.translate("Reset to Default","labels","Admin"),onClick:()=>this.actionResetToDefault()}),super.setup(),this.isCreate&&(this.hideField("sortBy"),this.hideField("sortDirection"),this.hideField("textFilterFields"),this.hideField("statusField"),this.hideField("fullTextSearch"),this.hideField("countDisabled"),this.hideField("kanbanViewMode"),this.hideField("kanbanStatusIgnoreList"),this.hideField("disabled")),this.options.hasColorField||this.hideField("color"),this.options.hasStreamField||this.hideField("stream"),this.isCreate||(this.manageKanbanFields({}),this.listenTo(this.model,"change:statusField",(e,t,a)=>{this.manageKanbanFields(a)}),this.manageKanbanViewModeField(),this.listenTo(this.model,"change:kanbanViewMode",()=>{this.manageKanbanViewModeField()}))}actionSave(e){this.trigger("save")}actionCancel(){this.trigger("cancel")}actionResetToDefault(){this.trigger("reset-to-default")}manageKanbanViewModeField(){this.model.get("kanbanViewMode")?this.showField("kanbanStatusIgnoreList"):this.hideField("kanbanStatusIgnoreList")}manageKanbanFields(e){e.ui&&this.model.set("kanbanStatusIgnoreList",[]),this.model.get("statusField")?(this.setKanbanStatusIgnoreListOptions(),this.showField("kanbanViewMode"),this.model.get("kanbanViewMode")?this.showField("kanbanStatusIgnoreList"):this.hideField("kanbanStatusIgnoreList")):(this.hideField("kanbanViewMode"),this.hideField("kanbanStatusIgnoreList"))}setKanbanStatusIgnoreListOptions(){var e=this.model.get("statusField"),e=this.getMetadata().get(["entityDefs",this.subjectEntityType,"fields",e,"options"])||[],e=(this.setFieldOptionList("kanbanStatusIgnoreList",e),this.getFieldView("kanbanStatusIgnoreList"));e?this.setKanbanStatusIgnoreListTranslation():this.once("after:render",()=>this.setKanbanStatusIgnoreListTranslation())}setKanbanStatusIgnoreListTranslation(){var e=this.getFieldView("kanbanStatusIgnoreList"),t=this.model.get("statusField");e.params.translation=this.getMetadata().get(["entityDefs",this.subjectEntityType,"fields",t,"translation"])||this.subjectEntityType+".options."+t,e.setupTranslation()}}e.default=s}),define("views/admin/entity-manager/modals/select-icon",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/entity-manager/modals/select-icon";buttonList=[{name:"cancel",label:"Cancel"}];data(){return{iconDataList:this.getIconDataList()}}setup(){this.addHandler("keyup",'input[data-name="quick-search"]',(e,t)=>{this.processQuickSearch(t.value)}),this.itemCache={},this.iconList=["fas fa-0","fas fa-1","fas fa-2","fas fa-3","fas fa-4","fas fa-5","fas fa-6","fas fa-7","fas fa-8","fas fa-9","fas fa-a","fas fa-address-book","fas fa-address-card","fas fa-align-center","fas fa-align-justify","fas fa-align-left","fas fa-align-right","fas fa-anchor","fas fa-anchor-circle-check","fas fa-anchor-circle-exclamation","fas fa-anchor-circle-xmark","fas fa-anchor-lock","fas fa-angle-down","fas fa-angle-left","fas fa-angle-right","fas fa-angle-up","fas fa-angles-down","fas fa-angles-left","fas fa-angles-right","fas fa-angles-up","fas fa-ankh","fas fa-apple-whole","fas fa-archway","fas fa-arrow-down","fas fa-arrow-down-1-9","fas fa-arrow-down-9-1","fas fa-arrow-down-a-z","fas fa-arrow-down-long","fas fa-arrow-down-short-wide","fas fa-arrow-down-up-across-line","fas fa-arrow-down-up-lock","fas fa-arrow-down-wide-short","fas fa-arrow-down-z-a","fas fa-arrow-left","fas fa-arrow-left-long","fas fa-arrow-pointer","fas fa-arrow-right","fas fa-arrow-right-arrow-left","fas fa-arrow-right-from-bracket","fas fa-arrow-right-long","fas fa-arrow-right-to-bracket","fas fa-arrow-right-to-city","fas fa-arrow-rotate-left","fas fa-arrow-rotate-right","fas fa-arrow-trend-down","fas fa-arrow-trend-up","fas fa-arrow-turn-down","fas fa-arrow-turn-up","fas fa-arrow-up","fas fa-arrow-up-1-9","fas fa-arrow-up-9-1","fas fa-arrow-up-a-z","fas fa-arrow-up-from-bracket","fas fa-arrow-up-from-ground-water","fas fa-arrow-up-from-water-pump","fas fa-arrow-up-long","fas fa-arrow-up-right-dots","fas fa-arrow-up-right-from-square","fas fa-arrow-up-short-wide","fas fa-arrow-up-wide-short","fas fa-arrow-up-z-a","fas fa-arrows-down-to-line","fas fa-arrows-down-to-people","fas fa-arrows-left-right","fas fa-arrows-left-right-to-line","fas fa-arrows-rotate","fas fa-arrows-spin","fas fa-arrows-split-up-and-left","fas fa-arrows-to-circle","fas fa-arrows-to-dot","fas fa-arrows-to-eye","fas fa-arrows-turn-right","fas fa-arrows-turn-to-dots","fas fa-arrows-up-down","fas fa-arrows-up-down-left-right","fas fa-arrows-up-to-line","fas fa-asterisk","fas fa-at","fas fa-atom","fas fa-audio-description","fas fa-austral-sign","fas fa-award","fas fa-b","fas fa-baby","fas fa-baby-carriage","fas fa-backward","fas fa-backward-fast","fas fa-backward-step","fas fa-bacon","fas fa-bacteria","fas fa-bacterium","fas fa-bag-shopping","fas fa-bahai","fas fa-baht-sign","fas fa-ban","fas fa-ban-smoking","fas fa-bandage","fas fa-bangladeshi-taka-sign","fas fa-barcode","fas fa-bars","fas fa-bars-progress","fas fa-bars-staggered","fas fa-baseball","fas fa-baseball-bat-ball","fas fa-basket-shopping","fas fa-basketball","fas fa-bath","fas fa-battery-empty","fas fa-battery-full","fas fa-battery-half","fas fa-battery-quarter","fas fa-battery-three-quarters","fas fa-bed","fas fa-bed-pulse","fas fa-beer-mug-empty","fas fa-bell","fas fa-bell-concierge","fas fa-bell-slash","fas fa-bezier-curve","fas fa-bicycle","fas fa-binoculars","fas fa-biohazard","fas fa-bitcoin-sign","fas fa-blender","fas fa-blender-phone","fas fa-blog","fas fa-bold","fas fa-bolt","fas fa-bolt-lightning","fas fa-bomb","fas fa-bone","fas fa-bong","fas fa-book","fas fa-book-atlas","fas fa-book-bible","fas fa-book-bookmark","fas fa-book-journal-whills","fas fa-book-medical","fas fa-book-open","fas fa-book-open-reader","fas fa-book-quran","fas fa-book-skull","fas fa-book-tanakh","fas fa-bookmark","fas fa-border-all","fas fa-border-none","fas fa-border-top-left","fas fa-bore-hole","fas fa-bottle-droplet","fas fa-bottle-water","fas fa-bowl-food","fas fa-bowl-rice","fas fa-bowling-ball","fas fa-box","fas fa-box-archive","fas fa-box-open","fas fa-box-tissue","fas fa-boxes-packing","fas fa-boxes-stacked","fas fa-braille","fas fa-brain","fas fa-brazilian-real-sign","fas fa-bread-slice","fas fa-bridge","fas fa-bridge-circle-check","fas fa-bridge-circle-exclamation","fas fa-bridge-circle-xmark","fas fa-bridge-lock","fas fa-bridge-water","fas fa-briefcase","fas fa-briefcase-medical","fas fa-broom","fas fa-broom-ball","fas fa-brush","fas fa-bucket","fas fa-bug","fas fa-bug-slash","fas fa-bugs","fas fa-building","fas fa-building-circle-arrow-right","fas fa-building-circle-check","fas fa-building-circle-exclamation","fas fa-building-circle-xmark","fas fa-building-columns","fas fa-building-flag","fas fa-building-lock","fas fa-building-ngo","fas fa-building-shield","fas fa-building-un","fas fa-building-user","fas fa-building-wheat","fas fa-bullhorn","fas fa-bullseye","fas fa-burger","fas fa-burst","fas fa-bus","fas fa-bus-simple","fas fa-business-time","fas fa-c","fas fa-cable-car","fas fa-cake-candles","fas fa-calculator","fas fa-calendar","fas fa-calendar-check","fas fa-calendar-day","fas fa-calendar-days","fas fa-calendar-minus","fas fa-calendar-plus","fas fa-calendar-week","fas fa-calendar-xmark","fas fa-camera","fas fa-camera-retro","fas fa-camera-rotate","fas fa-campground","fas fa-candy-cane","fas fa-cannabis","fas fa-capsules","fas fa-car","fas fa-car-battery","fas fa-car-burst","fas fa-car-on","fas fa-car-rear","fas fa-car-side","fas fa-car-tunnel","fas fa-caravan","fas fa-caret-down","fas fa-caret-left","fas fa-caret-right","fas fa-caret-up","fas fa-carrot","fas fa-cart-arrow-down","fas fa-cart-flatbed","fas fa-cart-flatbed-suitcase","fas fa-cart-plus","fas fa-cart-shopping","fas fa-cash-register","fas fa-cat","fas fa-cedi-sign","fas fa-cent-sign","fas fa-certificate","fas fa-chair","fas fa-chalkboard","fas fa-chalkboard-user","fas fa-champagne-glasses","fas fa-charging-station","fas fa-chart-area","fas fa-chart-bar","fas fa-chart-column","fas fa-chart-gantt","fas fa-chart-line","fas fa-chart-pie","fas fa-chart-simple","fas fa-check","fas fa-check-double","fas fa-check-to-slot","fas fa-cheese","fas fa-chess","fas fa-chess-bishop","fas fa-chess-board","fas fa-chess-king","fas fa-chess-knight","fas fa-chess-pawn","fas fa-chess-queen","fas fa-chess-rook","fas fa-chevron-down","fas fa-chevron-left","fas fa-chevron-right","fas fa-chevron-up","fas fa-child","fas fa-child-combatant","fas fa-child-dress","fas fa-child-reaching","fas fa-children","fas fa-church","fas fa-circle","fas fa-circle-arrow-down","fas fa-circle-arrow-left","fas fa-circle-arrow-right","fas fa-circle-arrow-up","fas fa-circle-check","fas fa-circle-chevron-down","fas fa-circle-chevron-left","fas fa-circle-chevron-right","fas fa-circle-chevron-up","fas fa-circle-dollar-to-slot","fas fa-circle-dot","fas fa-circle-down","fas fa-circle-exclamation","fas fa-circle-h","fas fa-circle-half-stroke","fas fa-circle-info","fas fa-circle-left","fas fa-circle-minus","fas fa-circle-nodes","fas fa-circle-notch","fas fa-circle-pause","fas fa-circle-play","fas fa-circle-plus","fas fa-circle-question","fas fa-circle-radiation","fas fa-circle-right","fas fa-circle-stop","fas fa-circle-up","fas fa-circle-user","fas fa-circle-xmark","fas fa-city","fas fa-clapperboard","fas fa-clipboard","fas fa-clipboard-check","fas fa-clipboard-list","fas fa-clipboard-question","fas fa-clipboard-user","fas fa-clock","fas fa-clock-rotate-left","fas fa-clone","fas fa-closed-captioning","fas fa-cloud","fas fa-cloud-arrow-down","fas fa-cloud-arrow-up","fas fa-cloud-bolt","fas fa-cloud-meatball","fas fa-cloud-moon","fas fa-cloud-moon-rain","fas fa-cloud-rain","fas fa-cloud-showers-heavy","fas fa-cloud-showers-water","fas fa-cloud-sun","fas fa-cloud-sun-rain","fas fa-clover","fas fa-code","fas fa-code-branch","fas fa-code-commit","fas fa-code-compare","fas fa-code-fork","fas fa-code-merge","fas fa-code-pull-request","fas fa-coins","fas fa-colon-sign","fas fa-comment","fas fa-comment-dollar","fas fa-comment-dots","fas fa-comment-medical","fas fa-comment-slash","fas fa-comment-sms","fas fa-comments","fas fa-comments-dollar","fas fa-compact-disc","fas fa-compass","fas fa-compass-drafting","fas fa-compress","fas fa-computer","fas fa-computer-mouse","fas fa-cookie","fas fa-cookie-bite","fas fa-copy","fas fa-copyright","fas fa-couch","fas fa-cow","fas fa-credit-card","fas fa-crop","fas fa-crop-simple","fas fa-cross","fas fa-crosshairs","fas fa-crow","fas fa-crown","fas fa-crutch","fas fa-cruzeiro-sign","fas fa-cube","fas fa-cubes","fas fa-cubes-stacked","fas fa-d","fas fa-database","fas fa-delete-left","fas fa-democrat","fas fa-desktop","fas fa-dharmachakra","fas fa-diagram-next","fas fa-diagram-predecessor","fas fa-diagram-project","fas fa-diagram-successor","fas fa-diamond","fas fa-diamond-turn-right","fas fa-dice","fas fa-dice-d20","fas fa-dice-d6","fas fa-dice-five","fas fa-dice-four","fas fa-dice-one","fas fa-dice-six","fas fa-dice-three","fas fa-dice-two","fas fa-disease","fas fa-display","fas fa-divide","fas fa-dna","fas fa-dog","fas fa-dollar-sign","fas fa-dolly","fas fa-dong-sign","fas fa-door-closed","fas fa-door-open","fas fa-dove","fas fa-down-left-and-up-right-to-center","fas fa-down-long","fas fa-download","fas fa-dragon","fas fa-draw-polygon","fas fa-droplet","fas fa-droplet-slash","fas fa-drum","fas fa-drum-steelpan","fas fa-drumstick-bite","fas fa-dumbbell","fas fa-dumpster","fas fa-dumpster-fire","fas fa-dungeon","fas fa-e","fas fa-ear-deaf","fas fa-ear-listen","fas fa-earth-africa","fas fa-earth-americas","fas fa-earth-asia","fas fa-earth-europe","fas fa-earth-oceania","fas fa-egg","fas fa-eject","fas fa-elevator","fas fa-ellipsis","fas fa-ellipsis-vertical","fas fa-envelope","fas fa-envelope-circle-check","fas fa-envelope-open","fas fa-envelope-open-text","fas fa-envelopes-bulk","fas fa-equals","fas fa-eraser","fas fa-ethernet","fas fa-euro-sign","fas fa-exclamation","fas fa-expand","fas fa-explosion","fas fa-eye","fas fa-eye-dropper","fas fa-eye-low-vision","fas fa-eye-slash","fas fa-f","fas fa-face-angry","fas fa-face-dizzy","fas fa-face-flushed","fas fa-face-frown","fas fa-face-frown-open","fas fa-face-grimace","fas fa-face-grin","fas fa-face-grin-beam","fas fa-face-grin-beam-sweat","fas fa-face-grin-hearts","fas fa-face-grin-squint","fas fa-face-grin-squint-tears","fas fa-face-grin-stars","fas fa-face-grin-tears","fas fa-face-grin-tongue","fas fa-face-grin-tongue-squint","fas fa-face-grin-tongue-wink","fas fa-face-grin-wide","fas fa-face-grin-wink","fas fa-face-kiss","fas fa-face-kiss-beam","fas fa-face-kiss-wink-heart","fas fa-face-laugh","fas fa-face-laugh-beam","fas fa-face-laugh-squint","fas fa-face-laugh-wink","fas fa-face-meh","fas fa-face-meh-blank","fas fa-face-rolling-eyes","fas fa-face-sad-cry","fas fa-face-sad-tear","fas fa-face-smile","fas fa-face-smile-beam","fas fa-face-smile-wink","fas fa-face-surprise","fas fa-face-tired","fas fa-fan","fas fa-faucet","fas fa-faucet-drip","fas fa-fax","fas fa-feather","fas fa-feather-pointed","fas fa-ferry","fas fa-file","fas fa-file-arrow-down","fas fa-file-arrow-up","fas fa-file-audio","fas fa-file-circle-check","fas fa-file-circle-exclamation","fas fa-file-circle-minus","fas fa-file-circle-plus","fas fa-file-circle-question","fas fa-file-circle-xmark","fas fa-file-code","fas fa-file-contract","fas fa-file-csv","fas fa-file-excel","fas fa-file-export","fas fa-file-image","fas fa-file-import","fas fa-file-invoice","fas fa-file-invoice-dollar","fas fa-file-lines","fas fa-file-medical","fas fa-file-pdf","fas fa-file-pen","fas fa-file-powerpoint","fas fa-file-prescription","fas fa-file-shield","fas fa-file-signature","fas fa-file-video","fas fa-file-waveform","fas fa-file-word","fas fa-file-zipper","fas fa-fill","fas fa-fill-drip","fas fa-film","fas fa-filter","fas fa-filter-circle-dollar","fas fa-filter-circle-xmark","fas fa-fingerprint","fas fa-fire","fas fa-fire-burner","fas fa-fire-extinguisher","fas fa-fire-flame-curved","fas fa-fire-flame-simple","fas fa-fish","fas fa-fish-fins","fas fa-flag","fas fa-flag-checkered","fas fa-flag-usa","fas fa-flask","fas fa-flask-vial","fas fa-floppy-disk","fas fa-florin-sign","fas fa-folder","fas fa-folder-closed","fas fa-folder-minus","fas fa-folder-open","fas fa-folder-plus","fas fa-folder-tree","fas fa-font","fas fa-font-awesome","fas fa-football","fas fa-forward","fas fa-forward-fast","fas fa-forward-step","fas fa-franc-sign","fas fa-frog","fas fa-futbol","fas fa-g","fas fa-gamepad","fas fa-gas-pump","fas fa-gauge","fas fa-gauge-high","fas fa-gauge-simple","fas fa-gauge-simple-high","fas fa-gavel","fas fa-gear","fas fa-gears","fas fa-gem","fas fa-genderless","fas fa-ghost","fas fa-gift","fas fa-gifts","fas fa-glass-water","fas fa-glass-water-droplet","fas fa-glasses","fas fa-globe","fas fa-golf-ball-tee","fas fa-gopuram","fas fa-graduation-cap","fas fa-greater-than","fas fa-greater-than-equal","fas fa-grip","fas fa-grip-lines","fas fa-grip-lines-vertical","fas fa-grip-vertical","fas fa-group-arrows-rotate","fas fa-guarani-sign","fas fa-guitar","fas fa-gun","fas fa-h","fas fa-hammer","fas fa-hamsa","fas fa-hand","fas fa-hand-back-fist","fas fa-hand-dots","fas fa-hand-fist","fas fa-hand-holding","fas fa-hand-holding-dollar","fas fa-hand-holding-droplet","fas fa-hand-holding-hand","fas fa-hand-holding-heart","fas fa-hand-holding-medical","fas fa-hand-lizard","fas fa-hand-middle-finger","fas fa-hand-peace","fas fa-hand-point-down","fas fa-hand-point-left","fas fa-hand-point-right","fas fa-hand-point-up","fas fa-hand-pointer","fas fa-hand-scissors","fas fa-hand-sparkles","fas fa-hand-spock","fas fa-handcuffs","fas fa-hands","fas fa-hands-asl-interpreting","fas fa-hands-bound","fas fa-hands-bubbles","fas fa-hands-clapping","fas fa-hands-holding","fas fa-hands-holding-child","fas fa-hands-holding-circle","fas fa-hands-praying","fas fa-handshake","fas fa-handshake-angle","fas fa-handshake-simple","fas fa-handshake-simple-slash","fas fa-handshake-slash","fas fa-hanukiah","fas fa-hard-drive","fas fa-hashtag","fas fa-hat-cowboy","fas fa-hat-cowboy-side","fas fa-hat-wizard","fas fa-head-side-cough","fas fa-head-side-cough-slash","fas fa-head-side-mask","fas fa-head-side-virus","fas fa-heading","fas fa-headphones","fas fa-headphones-simple","fas fa-headset","fas fa-heart","fas fa-heart-circle-bolt","fas fa-heart-circle-check","fas fa-heart-circle-exclamation","fas fa-heart-circle-minus","fas fa-heart-circle-plus","fas fa-heart-circle-xmark","fas fa-heart-crack","fas fa-heart-pulse","fas fa-helicopter","fas fa-helicopter-symbol","fas fa-helmet-safety","fas fa-helmet-un","fas fa-highlighter","fas fa-hill-avalanche","fas fa-hill-rockslide","fas fa-hippo","fas fa-hockey-puck","fas fa-holly-berry","fas fa-horse","fas fa-horse-head","fas fa-hospital","fas fa-hospital-user","fas fa-hot-tub-person","fas fa-hotdog","fas fa-hotel","fas fa-hourglass","fas fa-hourglass-end","fas fa-hourglass-half","fas fa-hourglass-start","fas fa-house","fas fa-house-chimney","fas fa-house-chimney-crack","fas fa-house-chimney-medical","fas fa-house-chimney-user","fas fa-house-chimney-window","fas fa-house-circle-check","fas fa-house-circle-exclamation","fas fa-house-circle-xmark","fas fa-house-crack","fas fa-house-fire","fas fa-house-flag","fas fa-house-flood-water","fas fa-house-flood-water-circle-arrow-right","fas fa-house-laptop","fas fa-house-lock","fas fa-house-medical","fas fa-house-medical-circle-check","fas fa-house-medical-circle-exclamation","fas fa-house-medical-circle-xmark","fas fa-house-medical-flag","fas fa-house-signal","fas fa-house-tsunami","fas fa-house-user","fas fa-hryvnia-sign","fas fa-hurricane","fas fa-i","fas fa-i-cursor","fas fa-ice-cream","fas fa-icicles","fas fa-icons","fas fa-id-badge","fas fa-id-card","fas fa-id-card-clip","fas fa-igloo","fas fa-image","fas fa-image-portrait","fas fa-images","fas fa-inbox","fas fa-indent","fas fa-indian-rupee-sign","fas fa-industry","fas fa-infinity","fas fa-info","fas fa-italic","fas fa-j","fas fa-jar","fas fa-jar-wheat","fas fa-jedi","fas fa-jet-fighter","fas fa-jet-fighter-up","fas fa-joint","fas fa-jug-detergent","fas fa-k","fas fa-kaaba","fas fa-key","fas fa-keyboard","fas fa-khanda","fas fa-kip-sign","fas fa-kit-medical","fas fa-kitchen-set","fas fa-kiwi-bird","fas fa-l","fas fa-land-mine-on","fas fa-landmark","fas fa-landmark-dome","fas fa-landmark-flag","fas fa-language","fas fa-laptop","fas fa-laptop-code","fas fa-laptop-file","fas fa-laptop-medical","fas fa-lari-sign","fas fa-layer-group","fas fa-leaf","fas fa-left-long","fas fa-left-right","fas fa-lemon","fas fa-less-than","fas fa-less-than-equal","fas fa-life-ring","fas fa-lightbulb","fas fa-lines-leaning","fas fa-link","fas fa-link-slash","fas fa-lira-sign","fas fa-list","fas fa-list-check","fas fa-list-ol","fas fa-list-ul","fas fa-litecoin-sign","fas fa-location-arrow","fas fa-location-crosshairs","fas fa-location-dot","fas fa-location-pin","fas fa-location-pin-lock","fas fa-lock","fas fa-lock-open","fas fa-locust","fas fa-lungs","fas fa-lungs-virus","fas fa-m","fas fa-magnet","fas fa-magnifying-glass","fas fa-magnifying-glass-arrow-right","fas fa-magnifying-glass-chart","fas fa-magnifying-glass-dollar","fas fa-magnifying-glass-location","fas fa-magnifying-glass-minus","fas fa-magnifying-glass-plus","fas fa-manat-sign","fas fa-map","fas fa-map-location","fas fa-map-location-dot","fas fa-map-pin","fas fa-marker","fas fa-mars","fas fa-mars-and-venus","fas fa-mars-and-venus-burst","fas fa-mars-double","fas fa-mars-stroke","fas fa-mars-stroke-right","fas fa-mars-stroke-up","fas fa-martini-glass","fas fa-martini-glass-citrus","fas fa-martini-glass-empty","fas fa-mask","fas fa-mask-face","fas fa-mask-ventilator","fas fa-masks-theater","fas fa-mattress-pillow","fas fa-maximize","fas fa-medal","fas fa-memory","fas fa-menorah","fas fa-mercury","fas fa-message","fas fa-meteor","fas fa-microchip","fas fa-microphone","fas fa-microphone-lines","fas fa-microphone-lines-slash","fas fa-microphone-slash","fas fa-microscope","fas fa-mill-sign","fas fa-minimize","fas fa-minus","fas fa-mitten","fas fa-mobile","fas fa-mobile-button","fas fa-mobile-retro","fas fa-mobile-screen","fas fa-mobile-screen-button","fas fa-money-bill","fas fa-money-bill-1","fas fa-money-bill-1-wave","fas fa-money-bill-transfer","fas fa-money-bill-trend-up","fas fa-money-bill-wave","fas fa-money-bill-wheat","fas fa-money-bills","fas fa-money-check","fas fa-money-check-dollar","fas fa-monument","fas fa-moon","fas fa-mortar-pestle","fas fa-mosque","fas fa-mosquito","fas fa-mosquito-net","fas fa-motorcycle","fas fa-mound","fas fa-mountain","fas fa-mountain-city","fas fa-mountain-sun","fas fa-mug-hot","fas fa-mug-saucer","fas fa-music","fas fa-n","fas fa-naira-sign","fas fa-network-wired","fas fa-neuter","fas fa-newspaper","fas fa-not-equal","fas fa-notdef","fas fa-note-sticky","fas fa-notes-medical","fas fa-o","fas fa-object-group","fas fa-object-ungroup","fas fa-oil-can","fas fa-oil-well","fas fa-om","fas fa-otter","fas fa-outdent","fas fa-p","fas fa-pager","fas fa-paint-roller","fas fa-paintbrush","fas fa-palette","fas fa-pallet","fas fa-panorama","fas fa-paper-plane","fas fa-paperclip","fas fa-parachute-box","fas fa-paragraph","fas fa-passport","fas fa-paste","fas fa-pause","fas fa-paw","fas fa-peace","fas fa-pen","fas fa-pen-clip","fas fa-pen-fancy","fas fa-pen-nib","fas fa-pen-ruler","fas fa-pen-to-square","fas fa-pencil","fas fa-people-arrows","fas fa-people-carry-box","fas fa-people-group","fas fa-people-line","fas fa-people-pulling","fas fa-people-robbery","fas fa-people-roof","fas fa-pepper-hot","fas fa-percent","fas fa-person","fas fa-person-arrow-down-to-line","fas fa-person-arrow-up-from-line","fas fa-person-biking","fas fa-person-booth","fas fa-person-breastfeeding","fas fa-person-burst","fas fa-person-cane","fas fa-person-chalkboard","fas fa-person-circle-check","fas fa-person-circle-exclamation","fas fa-person-circle-minus","fas fa-person-circle-plus","fas fa-person-circle-question","fas fa-person-circle-xmark","fas fa-person-digging","fas fa-person-dots-from-line","fas fa-person-dress","fas fa-person-dress-burst","fas fa-person-drowning","fas fa-person-falling","fas fa-person-falling-burst","fas fa-person-half-dress","fas fa-person-harassing","fas fa-person-hiking","fas fa-person-military-pointing","fas fa-person-military-rifle","fas fa-person-military-to-person","fas fa-person-praying","fas fa-person-pregnant","fas fa-person-rays","fas fa-person-rifle","fas fa-person-running","fas fa-person-shelter","fas fa-person-skating","fas fa-person-skiing","fas fa-person-skiing-nordic","fas fa-person-snowboarding","fas fa-person-swimming","fas fa-person-through-window","fas fa-person-walking","fas fa-person-walking-arrow-loop-left","fas fa-person-walking-arrow-right","fas fa-person-walking-dashed-line-arrow-right","fas fa-person-walking-luggage","fas fa-person-walking-with-cane","fas fa-peseta-sign","fas fa-peso-sign","fas fa-phone","fas fa-phone-flip","fas fa-phone-slash","fas fa-phone-volume","fas fa-photo-film","fas fa-piggy-bank","fas fa-pills","fas fa-pizza-slice","fas fa-place-of-worship","fas fa-plane","fas fa-plane-arrival","fas fa-plane-circle-check","fas fa-plane-circle-exclamation","fas fa-plane-circle-xmark","fas fa-plane-departure","fas fa-plane-lock","fas fa-plane-slash","fas fa-plane-up","fas fa-plant-wilt","fas fa-plate-wheat","fas fa-play","fas fa-plug","fas fa-plug-circle-bolt","fas fa-plug-circle-check","fas fa-plug-circle-exclamation","fas fa-plug-circle-minus","fas fa-plug-circle-plus","fas fa-plug-circle-xmark","fas fa-plus","fas fa-plus-minus","fas fa-podcast","fas fa-poo","fas fa-poo-storm","fas fa-poop","fas fa-power-off","fas fa-prescription","fas fa-prescription-bottle","fas fa-prescription-bottle-medical","fas fa-print","fas fa-pump-medical","fas fa-pump-soap","fas fa-puzzle-piece","fas fa-q","fas fa-qrcode","fas fa-question","fas fa-quote-left","fas fa-quote-right","fas fa-r","fas fa-radiation","fas fa-radio","fas fa-rainbow","fas fa-ranking-star","fas fa-receipt","fas fa-record-vinyl","fas fa-rectangle-ad","fas fa-rectangle-list","fas fa-rectangle-xmark","fas fa-recycle","fas fa-registered","fas fa-repeat","fas fa-reply","fas fa-reply-all","fas fa-republican","fas fa-restroom","fas fa-retweet","fas fa-ribbon","fas fa-right-from-bracket","fas fa-right-left","fas fa-right-long","fas fa-right-to-bracket","fas fa-ring","fas fa-road","fas fa-road-barrier","fas fa-road-bridge","fas fa-road-circle-check","fas fa-road-circle-exclamation","fas fa-road-circle-xmark","fas fa-road-lock","fas fa-road-spikes","fas fa-robot","fas fa-rocket","fas fa-rotate","fas fa-rotate-left","fas fa-rotate-right","fas fa-route","fas fa-rss","fas fa-ruble-sign","fas fa-rug","fas fa-ruler","fas fa-ruler-combined","fas fa-ruler-horizontal","fas fa-ruler-vertical","fas fa-rupee-sign","fas fa-rupiah-sign","fas fa-s","fas fa-sack-dollar","fas fa-sack-xmark","fas fa-sailboat","fas fa-satellite","fas fa-satellite-dish","fas fa-scale-balanced","fas fa-scale-unbalanced","fas fa-scale-unbalanced-flip","fas fa-school","fas fa-school-circle-check","fas fa-school-circle-exclamation","fas fa-school-circle-xmark","fas fa-school-flag","fas fa-school-lock","fas fa-scissors","fas fa-screwdriver","fas fa-screwdriver-wrench","fas fa-scroll","fas fa-scroll-torah","fas fa-sd-card","fas fa-section","fas fa-seedling","fas fa-server","fas fa-shapes","fas fa-share","fas fa-share-from-square","fas fa-share-nodes","fas fa-sheet-plastic","fas fa-shekel-sign","fas fa-shield","fas fa-shield-cat","fas fa-shield-dog","fas fa-shield-halved","fas fa-shield-heart","fas fa-shield-virus","fas fa-ship","fas fa-shirt","fas fa-shoe-prints","fas fa-shop","fas fa-shop-lock","fas fa-shop-slash","fas fa-shower","fas fa-shrimp","fas fa-shuffle","fas fa-shuttle-space","fas fa-sign-hanging","fas fa-signal","fas fa-signature","fas fa-signs-post","fas fa-sim-card","fas fa-sink","fas fa-sitemap","fas fa-skull","fas fa-skull-crossbones","fas fa-slash","fas fa-sleigh","fas fa-sliders","fas fa-smog","fas fa-smoking","fas fa-snowflake","fas fa-snowman","fas fa-snowplow","fas fa-soap","fas fa-socks","fas fa-solar-panel","fas fa-sort","fas fa-sort-down","fas fa-sort-up","fas fa-spa","fas fa-spaghetti-monster-flying","fas fa-spell-check","fas fa-spider","fas fa-spinner","fas fa-splotch","fas fa-spoon","fas fa-spray-can","fas fa-spray-can-sparkles","fas fa-square","fas fa-square-arrow-up-right","fas fa-square-caret-down","fas fa-square-caret-left","fas fa-square-caret-right","fas fa-square-caret-up","fas fa-square-check","fas fa-square-envelope","fas fa-square-full","fas fa-square-h","fas fa-square-minus","fas fa-square-nfi","fas fa-square-parking","fas fa-square-pen","fas fa-square-person-confined","fas fa-square-phone","fas fa-square-phone-flip","fas fa-square-plus","fas fa-square-poll-horizontal","fas fa-square-poll-vertical","fas fa-square-root-variable","fas fa-square-rss","fas fa-square-share-nodes","fas fa-square-up-right","fas fa-square-virus","fas fa-square-xmark","fas fa-staff-snake","fas fa-stairs","fas fa-stamp","fas fa-stapler","fas fa-star","fas fa-star-and-crescent","fas fa-star-half","fas fa-star-half-stroke","fas fa-star-of-david","fas fa-star-of-life","fas fa-sterling-sign","fas fa-stethoscope","fas fa-stop","fas fa-stopwatch","fas fa-stopwatch-20","fas fa-store","fas fa-store-slash","fas fa-street-view","fas fa-strikethrough","fas fa-stroopwafel","fas fa-subscript","fas fa-suitcase","fas fa-suitcase-medical","fas fa-suitcase-rolling","fas fa-sun","fas fa-sun-plant-wilt","fas fa-superscript","fas fa-swatchbook","fas fa-synagogue","fas fa-syringe","fas fa-t","fas fa-table","fas fa-table-cells","fas fa-table-cells-column-lock","fas fa-table-cells-large","fas fa-table-cells-row-lock","fas fa-table-cells-row-unlock","fas fa-table-columns","fas fa-table-list","fas fa-table-tennis-paddle-ball","fas fa-tablet","fas fa-tablet-button","fas fa-tablet-screen-button","fas fa-tablets","fas fa-tachograph-digital","fas fa-tag","fas fa-tags","fas fa-tape","fas fa-tarp","fas fa-tarp-droplet","fas fa-taxi","fas fa-teeth","fas fa-teeth-open","fas fa-temperature-arrow-down","fas fa-temperature-arrow-up","fas fa-temperature-empty","fas fa-temperature-full","fas fa-temperature-half","fas fa-temperature-high","fas fa-temperature-low","fas fa-temperature-quarter","fas fa-temperature-three-quarters","fas fa-tenge-sign","fas fa-tent","fas fa-tent-arrow-down-to-line","fas fa-tent-arrow-left-right","fas fa-tent-arrow-turn-left","fas fa-tent-arrows-down","fas fa-tents","fas fa-terminal","fas fa-text-height","fas fa-text-slash","fas fa-text-width","fas fa-thermometer","fas fa-thumbs-down","fas fa-thumbs-up","fas fa-thumbtack","fas fa-thumbtack-slash","fas fa-ticket","fas fa-ticket-simple","fas fa-timeline","fas fa-toggle-off","fas fa-toggle-on","fas fa-toilet","fas fa-toilet-paper","fas fa-toilet-paper-slash","fas fa-toilet-portable","fas fa-toilets-portable","fas fa-toolbox","fas fa-tooth","fas fa-torii-gate","fas fa-tornado","fas fa-tower-broadcast","fas fa-tower-cell","fas fa-tower-observation","fas fa-tractor","fas fa-trademark","fas fa-traffic-light","fas fa-trailer","fas fa-train","fas fa-train-subway","fas fa-train-tram","fas fa-transgender","fas fa-trash","fas fa-trash-arrow-up","fas fa-trash-can","fas fa-trash-can-arrow-up","fas fa-tree","fas fa-tree-city","fas fa-triangle-exclamation","fas fa-trophy","fas fa-trowel","fas fa-trowel-bricks","fas fa-truck","fas fa-truck-arrow-right","fas fa-truck-droplet","fas fa-truck-fast","fas fa-truck-field","fas fa-truck-field-un","fas fa-truck-front","fas fa-truck-medical","fas fa-truck-monster","fas fa-truck-moving","fas fa-truck-pickup","fas fa-truck-plane","fas fa-truck-ramp-box","fas fa-tty","fas fa-turkish-lira-sign","fas fa-turn-down","fas fa-turn-up","fas fa-tv","fas fa-u","fas fa-umbrella","fas fa-umbrella-beach","fas fa-underline","fas fa-universal-access","fas fa-unlock","fas fa-unlock-keyhole","fas fa-up-down","fas fa-up-down-left-right","fas fa-up-long","fas fa-up-right-and-down-left-from-center","fas fa-up-right-from-square","fas fa-upload","fas fa-user","fas fa-user-astronaut","fas fa-user-check","fas fa-user-clock","fas fa-user-doctor","fas fa-user-gear","fas fa-user-graduate","fas fa-user-group","fas fa-user-injured","fas fa-user-large","fas fa-user-large-slash","fas fa-user-lock","fas fa-user-minus","fas fa-user-ninja","fas fa-user-nurse","fas fa-user-pen","fas fa-user-plus","fas fa-user-secret","fas fa-user-shield","fas fa-user-slash","fas fa-user-tag","fas fa-user-tie","fas fa-user-xmark","fas fa-users","fas fa-users-between-lines","fas fa-users-gear","fas fa-users-line","fas fa-users-rays","fas fa-users-rectangle","fas fa-users-slash","fas fa-users-viewfinder","fas fa-utensils","fas fa-v","fas fa-van-shuttle","fas fa-vault","fas fa-vector-square","fas fa-venus","fas fa-venus-double","fas fa-venus-mars","fas fa-vest","fas fa-vest-patches","fas fa-vial","fas fa-vial-circle-check","fas fa-vial-virus","fas fa-vials","fas fa-video","fas fa-video-slash","fas fa-vihara","fas fa-virus","fas fa-virus-covid","fas fa-virus-covid-slash","fas fa-virus-slash","fas fa-viruses","fas fa-voicemail","fas fa-volcano","fas fa-volleyball","fas fa-volume-high","fas fa-volume-low","fas fa-volume-off","fas fa-volume-xmark","fas fa-vr-cardboard","fas fa-w","fas fa-walkie-talkie","fas fa-wallet","fas fa-wand-magic","fas fa-wand-magic-sparkles","fas fa-wand-sparkles","fas fa-warehouse","fas fa-water","fas fa-water-ladder","fas fa-wave-square","fas fa-weight-hanging","fas fa-weight-scale","fas fa-wheat-awn","fas fa-wheat-awn-circle-exclamation","fas fa-wheelchair","fas fa-wheelchair-move","fas fa-whiskey-glass","fas fa-wifi","fas fa-wind","fas fa-window-maximize","fas fa-window-minimize","fas fa-window-restore","fas fa-wine-bottle","fas fa-wine-glass","fas fa-wine-glass-empty","fas fa-won-sign","fas fa-worm","fas fa-wrench","fas fa-x","fas fa-x-ray","fas fa-xmark","fas fa-xmarks-lines","fas fa-y","fas fa-yen-sign","fas fa-yin-yang","fas fa-z","far fa-address-book","far fa-address-card","far fa-bell","far fa-bell-slash","far fa-bookmark","far fa-building","far fa-calendar","far fa-calendar-check","far fa-calendar-days","far fa-calendar-minus","far fa-calendar-plus","far fa-calendar-xmark","far fa-chart-bar","far fa-chess-bishop","far fa-chess-king","far fa-chess-knight","far fa-chess-pawn","far fa-chess-queen","far fa-chess-rook","far fa-circle","far fa-circle-check","far fa-circle-dot","far fa-circle-down","far fa-circle-left","far fa-circle-pause","far fa-circle-play","far fa-circle-question","far fa-circle-right","far fa-circle-stop","far fa-circle-up","far fa-circle-user","far fa-circle-xmark","far fa-clipboard","far fa-clock","far fa-clone","far fa-closed-captioning","far fa-comment","far fa-comment-dots","far fa-comments","far fa-compass","far fa-copy","far fa-copyright","far fa-credit-card","far fa-envelope","far fa-envelope-open","far fa-eye","far fa-eye-slash","far fa-face-angry","far fa-face-dizzy","far fa-face-flushed","far fa-face-frown","far fa-face-frown-open","far fa-face-grimace","far fa-face-grin","far fa-face-grin-beam","far fa-face-grin-beam-sweat","far fa-face-grin-hearts","far fa-face-grin-squint","far fa-face-grin-squint-tears","far fa-face-grin-stars","far fa-face-grin-tears","far fa-face-grin-tongue","far fa-face-grin-tongue-squint","far fa-face-grin-tongue-wink","far fa-face-grin-wide","far fa-face-grin-wink","far fa-face-kiss","far fa-face-kiss-beam","far fa-face-kiss-wink-heart","far fa-face-laugh","far fa-face-laugh-beam","far fa-face-laugh-squint","far fa-face-laugh-wink","far fa-face-meh","far fa-face-meh-blank","far fa-face-rolling-eyes","far fa-face-sad-cry","far fa-face-sad-tear","far fa-face-smile","far fa-face-smile-beam","far fa-face-smile-wink","far fa-face-surprise","far fa-face-tired","far fa-file","far fa-file-audio","far fa-file-code","far fa-file-excel","far fa-file-image","far fa-file-lines","far fa-file-pdf","far fa-file-powerpoint","far fa-file-video","far fa-file-word","far fa-file-zipper","far fa-flag","far fa-floppy-disk","far fa-folder","far fa-folder-closed","far fa-folder-open","far fa-font-awesome","far fa-futbol","far fa-gem","far fa-hand","far fa-hand-back-fist","far fa-hand-lizard","far fa-hand-peace","far fa-hand-point-down","far fa-hand-point-left","far fa-hand-point-right","far fa-hand-point-up","far fa-hand-pointer","far fa-hand-scissors","far fa-hand-spock","far fa-handshake","far fa-hard-drive","far fa-heart","far fa-hospital","far fa-hourglass","far fa-hourglass-half","far fa-id-badge","far fa-id-card","far fa-image","far fa-images","far fa-keyboard","far fa-lemon","far fa-life-ring","far fa-lightbulb","far fa-map","far fa-message","far fa-money-bill-1","far fa-moon","far fa-newspaper","far fa-note-sticky","far fa-object-group","far fa-object-ungroup","far fa-paper-plane","far fa-paste","far fa-pen-to-square","far fa-rectangle-list","far fa-rectangle-xmark","far fa-registered","far fa-share-from-square","far fa-snowflake","far fa-square","far fa-square-caret-down","far fa-square-caret-left","far fa-square-caret-right","far fa-square-caret-up","far fa-square-check","far fa-square-full","far fa-square-minus","far fa-square-plus","far fa-star","far fa-star-half","far fa-star-half-stroke","far fa-sun","far fa-thumbs-down","far fa-thumbs-up","far fa-trash-can","far fa-user","far fa-window-maximize","far fa-window-minimize","far fa-window-restore"],this.iconList.push(...this.getMetadata().get("app.clientIcons.classList",[]))}actionSelect(e){this.trigger("select",e.value)}getIconDataList(){let a=[];return this.iconList.forEach((e,t)=>{t%12==0&&a.push([]),a[a.length-1].push(e)}),a}processQuickSearch(s){if(s){let a=this.$el.find(".icons");this.iconList.forEach(e=>{let t=this.itemCache[e];t||(t=a.find(`> .icon-container[data-name="${e}"]`),this.itemCache[e]=t),~e.indexOf(s)?t.removeClass("hidden"):t.addClass("hidden")})}else this.$el.find(".icon-container").removeClass("hidden")}}e.default=s}),define("views/admin/entity-manager/modals/select-formula",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{templateContent=`
        <div class="panel no-side-margin">
            <table class="table table-bordered">
                {{#each typeList}}
                <tr>
                    <td style="width: 40%">
                        <a
                            class="btn btn-default btn-lg btn-full-wide"
                            href="#Admin/entityManager/formula&scope={{../scope}}&type={{this}}"
                        >
                        {{translate this category='fields' scope='EntityManager'}}
                        </a>
                    </td>
                    <td style="width: 60%">
                        <div class="complex-text">{{complexText (translate this category='messages' scope='EntityManager')}}
                    </td>
                </tr>
                {{/each}}
            </table>
        </div>
    `;backdrop=!0;data(){return{typeList:this.typeList,scope:this.scope}}setup(){this.scope=this.options.scope,this.typeList=["beforeSaveCustomScript","beforeSaveApiScript"],this.headerText=this.translate("Formula","labels","EntityManager")}}e.default=s}),define("views/admin/entity-manager/fields/icon-class",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{editTemplate="admin/entity-manager/fields/icon-class/edit";setup(){super.setup(),this.addActionHandler("selectIcon",()=>this.selectIcon())}selectIcon(){this.createView("dialog","views/admin/entity-manager/modals/select-icon",{},t=>{t.render(),this.listenToOnce(t,"select",e=>{this.model.set(this.name,e=""===e?null:e),t.close()})})}fetch(){var e={};return e[this.name]=this.model.get(this.name),e}}e.default=s}),define("views/admin/entity-manager/fields/duplicate-check-field-list",["exports","views/fields/multi-enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fieldTypeList=["varchar","personName","email","phone","url","barcode"];setupOptions(){let t=this.model.get("name");var e=this.getFieldManager().getEntityTypeFieldList(t,{typeList:this.fieldTypeList,onlyAvailable:!0}).sort((e,t)=>this.getLanguage().translate(e,"fields",this.entityType).localeCompare(this.getLanguage().translate(t,"fields",this.entityType)));this.translatedOptions={},e.forEach(e=>{this.translatedOptions[e]=this.translate(e,"fields",t)}),this.params.options=e}}e.default=s}),define("views/admin/entity-manager/fields/acl-account-link",["exports","views/admin/entity-manager/fields/acl-contact-link"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{targetEntityType="Account"}e.default=s}),define("views/admin/dynamic-logic/modals/edit",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/modals/edit";className="dialog dialog-record";data(){return{}}setup(){this.conditionGroup=Espo.Utils.cloneDeep(this.options.conditionGroup||[]),this.scope=this.options.scope,this.buttonList=[{name:"apply",label:"Apply",style:"primary",onClick:()=>this.actionApply()},{name:"cancel",label:"Cancel"}],this.createView("conditionGroup","views/admin/dynamic-logic/conditions/and",{selector:".top-group-container",itemData:{value:this.conditionGroup},scope:this.options.scope})}actionApply(){var e=this.getView("conditionGroup").fetch().value;this.trigger("apply",e),this.close()}}e.default=s}),define("views/admin/dynamic-logic/modals/add-field",["exports","views/modal","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{templateContent='<div class="field" data-name="field">{{{field}}}</div>';setup(){this.addActionHandler("addField",(e,t)=>{this.trigger("add-field",t.dataset.name)}),this.headerText=this.translate("Add Field"),this.scope=this.options.scope;let t=new a.default;this.createView("field","views/admin/dynamic-logic/fields/field",{selector:'[data-name="field"]',model:t,mode:"edit",scope:this.scope,defs:{name:"field",params:{}}},e=>{this.listenTo(e,"change",()=>{var e=t.get("field")||[];e.length&&this.trigger("add-field",e[0])})})}}e.default=i}),define("views/admin/dynamic-logic/fields/user-id",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent=`
        <a href="#User/view/{{id}}">{{name}}</a>
    `;data(){return{id:this.model.get("$user.id"),name:this.model.get("name")}}}e.default=s}),define("views/admin/dynamic-logic/fields/field",["exports","views/fields/multi-enum","ui/multi-select"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{getFieldList(){let a=this.getMetadata().get(`entityDefs.${this.options.scope}.fields`);var e=Object.keys(a).filter(e=>{var t=a[e].type||null;if(!a[e].disabled&&!a[e].utility&&t&&this.getMetadata().get(["clientDefs","DynamicLogic","fieldTypes",t]))return!0});return e.push("id"),e.sort((e,t)=>this.translate(e,"fields",this.options.scope).localeCompare(this.translate(t,"fields",this.options.scope))),e}setupTranslatedOptions(){this.translatedOptions={},this.params.options.forEach(e=>{this.translatedOptions[e]=this.translate(e,"fields",this.options.scope)})}setupOptions(){super.setupOptions(),this.params.options=this.getFieldList(),this.setupTranslatedOptions()}afterRender(){super.afterRender(),this.$element&&a.default.focus(this.$element)}}e.default=i}),define("views/admin/dynamic-logic/conditions-string/item-value-varchar",["exports","views/admin/dynamic-logic/conditions-string/item-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-base";createValueFieldView(){var e=this.getValueViewKey();this.createView("value","views/fields/varchar",{model:this.model,name:this.field,selector:`[data-view-key="${e}"]`,readOnly:!0})}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-value-link",["exports","views/admin/dynamic-logic/conditions-string/item-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-base";createValueFieldView(){var e=this.getValueViewKey(),t=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"entity"])||this.getMetadata().get(["entityDefs",this.scope,"links",this.field,"entity"]);this.createView("value","views/fields/link",{model:this.model,name:"link",selector:`[data-view-key="${e}"]`,readOnly:!0,foreignScope:t})}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-value-enum",["exports","views/admin/dynamic-logic/conditions-string/item-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-base";createValueFieldView(){var e=this.getValueViewKey();this.createView("value","views/fields/enum",{model:this.model,name:this.field,selector:`[data-view-key="${e}"]`,params:{options:this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"options"])||[]},readOnly:!0})}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-multiple-values-base",["exports","views/admin/dynamic-logic/conditions-string/item-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/item-multiple-values-base";data(){return{valueViewDataList:this.valueViewDataList,scope:this.scope,operator:this.operator,operatorString:this.operatorString,field:this.field}}populateValues(){}getValueViewKey(e){return`view-${this.level.toString()}-${this.number.toString()}-`+e.toString()}createValueFieldView(){let s=this.itemData.value||[];var e=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"type"])||"base";let i=this.getMetadata().get(["entityDefs",this.scope,"fields",this.field,"view"])||this.getFieldManager().getViewName(e);this.valueViewDataList=[],s.forEach((e,t)=>{var a=this.model.clone(),e=(a.set(this.itemData.attribute,e),this.getValueViewKey(t));this.valueViewDataList.push({key:e,isEnd:t===s.length-1}),this.createView(e,i,{model:a,name:this.field,selector:`[data-view-key="${e}"]`,readOnly:!0})})}}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-is-today",["exports","views/admin/dynamic-logic/conditions-string/item-operator-only-date"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dateValue="today"}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-in-past",["exports","views/admin/dynamic-logic/conditions-string/item-operator-only-date"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dateValue="past"}e.default=s}),define("views/admin/dynamic-logic/conditions-string/item-in-future",["exports","views/admin/dynamic-logic/conditions-string/item-operator-only-date"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{dateValue="future"}e.default=s}),define("views/admin/dynamic-logic/conditions-string/group-not",["exports","views/admin/dynamic-logic/conditions-string/group-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions-string/group-not";data(){return{viewKey:this.viewKey,operator:this.operator}}setup(){this.level=this.options.level||0,this.number=this.options.number||0,this.scope=this.options.scope,this.operator=this.options.operator||this.operator,this.itemData=this.options.itemData||{},this.viewList=[];var e=`view-${this.level.toString()}-${this.number.toString()}-`+0..toString();this.createItemView(0,e,this.itemData.value),this.viewKey=e}}e.default=s}),define("views/admin/dynamic-logic/conditions/or",["exports","views/admin/dynamic-logic/conditions/group-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{operator="or"}e.default=s}),define("views/admin/dynamic-logic/conditions/not",["exports","views/admin/dynamic-logic/conditions/group-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/dynamic-logic/conditions/not";operator="not";data(){return{viewKey:this.viewKey,operator:this.operator,hasItem:this.hasView(this.viewKey),level:this.level,groupOperator:this.getGroupOperator()}}setup(){this.level=this.options.level||0,this.number=this.options.number||0,this.scope=this.options.scope,this.itemData=this.options.itemData||{},this.viewList=[];var e=this.getKey();this.itemData.value&&this.createItemView(0,e,this.itemData.value),this.viewKey=e}removeItem(){var e=this.getKey();this.clearView(e),this.controlAddItemVisibility()}getKey(){return`view-${this.level.toString()}-${this.number.toString()}-`+0..toString()}getIndexForNewItem(){return 0}addItemContainer(){}addViewDataListItem(){}fetch(){var e=this.getView(this.viewKey);return e?(e=e.fetch(),{type:this.operator,value:e}):{type:"and",value:[]}}controlAddItemVisibility(){this.getView(this.getKey())?this.$el.find(" > .group-bottom").addClass("hidden"):this.$el.find(" > .group-bottom").removeClass("hidden")}}e.default=s}),define("views/admin/dynamic-logic/conditions/and",["exports","views/admin/dynamic-logic/conditions/group-base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{operator="and"}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/multi-enum",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetch(){var e=this.getView("value"),t={type:this.type,attribute:this.field};return e&&(e.fetchToModel(),t.value=this.model.get(this.field)),t}getValueViewName(){let e=super.getValueViewName();return e=["has","notHas"].includes(this.type)?"views/fields/enum":e}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/link",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetch(){var e=this.getView("value"),t={type:this.type,attribute:this.field+"Id",data:{field:this.field}};return e&&(e.fetchToModel(),t.value=this.model.get(this.field+"Id"),(e={})[this.field+"Name"]=this.model.get(this.field+"Name"),t.data.values=e),t}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/link-parent",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetch(){var e,t=this.getView("value");let a;return t&&t.fetchToModel(),a="equals"===this.type||"notEquals"===this.type?((e={})[this.field+"Id"]=t.model.get(this.field+"Id"),e[this.field+"Name"]=t.model.get(this.field+"Name"),e[this.field+"Type"]=t.model.get(this.field+"Type"),"equals"===this.type?{type:"and",value:[{type:"equals",attribute:this.field+"Id",value:t.model.get(this.field+"Id")},{type:"equals",attribute:this.field+"Type",value:t.model.get(this.field+"Type")}],data:{field:this.field,type:"equals",values:e}}:{type:"or",value:[{type:"notEquals",attribute:this.field+"Id",value:t.model.get(this.field+"Id")},{type:"notEquals",attribute:this.field+"Type",value:t.model.get(this.field+"Type")}],data:{field:this.field,type:"notEquals",values:e}}):{type:this.type,attribute:this.field+"Id",data:{field:this.field}}}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/enum",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{fetch(){var e=this.getView("value"),t={type:this.type,attribute:this.field};return e&&(e.fetchToModel(),t.value=this.model.get(this.field)),t}getValueViewName(){let e=super.getValueViewName();return e=["in","notIn"].includes(this.type)?"views/fields/multi-enum":e}}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/date",["exports","views/admin/dynamic-logic/conditions/field-types/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{}e.default=s}),define("views/admin/dynamic-logic/conditions/field-types/current-user",["exports","views/admin/dynamic-logic/conditions/field-types/base","model"],function(e,t,a){function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=s(t),a=s(a);class i extends t.default{getValueViewName(){return"views/fields/user"}getValueFieldName(){return"link"}createModel(){var e=new a.default;return e.setDefs({fields:{link:{type:"link",entity:"User"}}}),Promise.resolve(e)}populateValues(){this.itemData.attribute&&this.model.set("linkId",this.itemData.value);var e=(this.additionalData.values||{}).name;this.model.set("linkName",e)}translateLeftString(){return"$"+this.translate("User","scopeNames")}fetch(){return this.getView("value").fetchToModel(),{type:this.type,attribute:"$user.id",data:{values:{name:this.model.get("linkName")}},value:this.model.get("linkId")}}}e.default=i}),define("views/admin/dynamic-logic/conditions/field-types/current-user-teams",["exports","views/admin/dynamic-logic/conditions/field-types/link-multiple"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{translateLeftString(){return"$"+this.translate("User","scopeNames")+"."+super.translateLeftString()}fetch(){var e=super.fetch();return e.attribute="$user.teamsIds",e}}e.default=s}),define("views/admin/complex-expression/modals/add-function",["exports","views/modal"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{template="admin/formula/modals/add-function";backdrop=!0;data(){var e=this.translate("formulaFunctions","messages","Admin").replace("{documentationUrl}",this.documentationUrl),e=this.getHelper().transformMarkdownText(e,{linksInNewTab:!0}).toString();return{functionDataList:this.functionDataList,text:e}}setup(){this.addActionHandler("add",(e,t)=>{this.trigger("add",t.dataset.value)}),this.headerText=this.translate("Function"),this.documentationUrl="https://docs.espocrm.com/user-guide/complex-expressions/",this.functionDataList=this.options.functionDataList||this.getMetadata().get("app.complexExpression.functionList")||[]}}e.default=s}),define("views/admin/authentication/fields/test-connection",["exports","views/fields/base"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{templateContent=`
        <button
            class="btn btn-default"
            data-action="testConnection"
        >{{translate 'Test Connection' scope='Settings'}}</button>
    `;fetch(){return{}}setup(){super.setup(),this.addActionHandler("testConnection",()=>this.testConnection())}getConnectionData(){return{host:this.model.get("ldapHost"),port:this.model.get("ldapPort"),useSsl:this.model.get("ldapSecurity"),useStartTls:this.model.get("ldapSecurity"),username:this.model.get("ldapUsername"),password:this.model.get("ldapPassword"),bindRequiresDn:this.model.get("ldapBindRequiresDn"),accountDomainName:this.model.get("ldapAccountDomainName"),accountDomainNameShort:this.model.get("ldapAccountDomainNameShort"),accountCanonicalForm:this.model.get("ldapAccountCanonicalForm")}}testConnection(){var e=this.getConnectionData();this.$el.find("button").prop("disabled",!0),Espo.Ui.notify(this.translate("Connecting","labels","Settings")),Espo.Ajax.postRequest("Ldap/action/testConnection",e).then(()=>{this.$el.find("button").prop("disabled",!1),Espo.Ui.success(this.translate("ldapTestConnection","messages","Settings"))}).catch(e=>{let t=e.getResponseHeader("X-Status-Reason")||"",a=(t=(t=t.replace(/ $/,"")).replace(/,$/,""),this.translate("Error")+" "+e.status);t&&(a+=": "+t),Espo.Ui.error(a,!0),console.error(a),e.errorIsHandled=!0,this.$el.find("button").prop("disabled",!1)})}}e.default=s}),define("views/admin/auth-token/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{createButton=!1;getHeader(){var e=document.createElement("a"),t=(e.href="#Admin",e.innerText=this.translate("Administration"),document.createElement("span"));return t.innerText=this.getLanguage().translate("Auth Tokens","labels","Admin"),this.buildHeaderHtml([e,t])}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Auth Tokens","labels","Admin"))}}e.default=s}),define("views/admin/auth-token/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{rowActionsView="views/admin/auth-token/record/row-actions/default";massActionList=["remove","setInactive"];checkAllResultMassActionList=["remove","setInactive"];massActionSetInactive(){let e=null;this.allResultIsChecked||(e=this.checkedList);Espo.Ajax.postRequest("MassAction",{action:"update",entityType:this.entityType,params:{ids:e||null,where:e&&0!==e.length?null:this.collection.getWhere(),searchParams:e&&0!==e.length?null:this.collection.data},data:{isActive:!1}}).then(()=>{this.collection.fetch().then(()=>{Espo.Ui.success(this.translate("Done")),e&&e.forEach(e=>{this.checkRecord(e)})})})}actionSetInactive(e){e.id&&(e=this.collection.get(e.id))&&(Espo.Ui.notify(this.translate("pleaseWait","messages")),e.save({isActive:!1},{patch:!0}).then(()=>{Espo.Ui.notify(!1)}))}}e.default=s}),define("views/admin/auth-token/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;readOnly=!0}e.default=s}),define("views/admin/auth-token/record/detail-small",["exports","views/record/detail-small"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;isWide=!0;bottomView="views/record/detail-bottom"}e.default=s}),define("views/admin/auth-token/record/row-actions/default",["exports","views/record/row-actions/default"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setup(){super.setup(),this.listenTo(this.model,"change:isActive",()=>{setTimeout(()=>this.reRender(),10)})}getActionList(){var e=[];return e.push({action:"quickView",label:"View",data:{id:this.model.id}}),this.model.get("isActive")&&e.push({action:"setInactive",label:"Set Inactive",data:{id:this.model.id}}),e.push({action:"quickRemove",label:"Remove",data:{id:this.model.id}}),e}}e.default=s}),define("views/admin/auth-token/modals/detail",["exports","views/modals/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;editDisabled=!0}e.default=s}),define("views/admin/auth-log-record/list",["exports","views/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{getHeader(){return this.buildHeaderHtml([$("<a>").attr("href","#Admin").text(this.translate("Administration")),$("<span>").text(this.getLanguage().translate("Auth Log","labels","Admin"))])}updatePageTitle(){this.setPageTitle(this.getLanguage().translate("Auth Log","labels","Admin"))}}e.default=s}),define("views/admin/auth-log-record/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{rowActionsView="views/record/row-actions/view-and-remove";massActionList=["remove"];checkAllResultMassActionList=["remove"];forceSettings=!0}e.default=s}),define("views/admin/auth-log-record/record/detail",["exports","views/record/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;readOnly=!0}e.default=s}),define("views/admin/auth-log-record/record/detail-small",["exports","views/record/detail-small"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;isWide=!0;bottomView="views/record/detail-bottom"}e.default=s}),define("views/admin/auth-log-record/modals/detail",["exports","views/modals/detail"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{sideDisabled=!0;editDisabled=!0}e.default=s}),define("views/admin/auth-log-record/fields/authentication-method",["exports","views/fields/enum"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{setupOptions(){this.params.options=Object.keys(this.getMetadata().get("authenticationMethods")||{})}}e.default=s}),define("views/admin/app-secret/fields/value",["exports","views/fields/text"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{detailTemplateContent="**********";validations=["required"];changingMode=!1;data(){return{isNew:this.model.isNew(),...super.data()}}afterRenderEdit(){var e;super.afterRenderEdit(),this.model.isNew()||this.changingMode||(this.element.innerHTML="",(e=document.createElement("a")).role="button",e.onclick=()=>this.changePassword(),e.textContent=this.translate("change"),this.element.appendChild(e))}onDetailModeSet(){return this.changingMode=!1,super.onDetailModeSet()}fetch(){return this.model.isNew()||this.changingMode?super.fetch():{}}async changePassword(){this.changingMode=!0,await this.reRender()}}e.default=s}),define("views/admin/app-log-record/record/list",["exports","views/record/list"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{forceSettings=!0}e.default=s}),define("controllers/role",["exports","controllers/record"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{checkAccess(e){return!!this.getUser().isAdmin()}}e.default=s}),define("controllers/portal-role",["exports","controllers/record"],function(e,t){var a;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=(a=t)&&a.__esModule?a:{default:a};class s extends t.default{checkAccess(e){return!!this.getUser().isAdmin()}}e.default=s}),define("controllers/admin",["exports","controller","search-manager","views/settings/edit","views/admin/index","di","language"],function(e,t,a,i,s,l,o){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t=r(t),a=r(a),i=r(i),s=r(s),o=r(o);let n,d;function r(e){return e&&e.__esModule?e:{default:e}}function f(d,t,r,a,f,e){var E,C,h,c,u,s=Symbol.metadata||Symbol.for("Symbol.metadata"),O=Object.defineProperty,i=Object.create,A=[i(null),i(null)],l=t.length;function P(s,i,l){return function(e,t){i&&(t=e,e=d);for(var a=0;a<s.length;a++)t=s[a].apply(e,l?[t]:[]);return l?t:e}}function S(e,t,a,s){if("function"==typeof e||!s&&void 0===e)return e;throw new TypeError(t+" must "+(a||"be")+" a function"+(s?"":" or undefined"))}function p(l,t,e,a,s,i,o,n,d,r,f){function h(e){if(!f(e))throw new TypeError("Attempted to access private element on non-instance")}var c=[].concat(t[0]),u=t[3],p=!o,m=1===s,g=3===s,y=4===s,v=2===s;function b(a,s,i){return function(e,t){return s&&(t=e,e=l),i&&i(e),w[a].call(e,t)}}if(!p){var w={},L=[],M=g?"get":y||m?"set":"value";if(d?(r||m?w={get:$(function(){return u(this)},a,"get"),set:function(e){t[4](this,e)}}:w[M]=u,r||$(w[M],a,v?"":M)):r||(w=Object.getOwnPropertyDescriptor(l,a)),!r&&!d){if((C=A[+n][a])&&7!=(C^s))throw Error("Decorating two elements with the same name ("+w[M].name+") is not supported yet");A[+n][a]=s<3?1:s}}for(var x=l,F=c.length-1;0<=F;F-=e?2:1){var _=S(c[F],"A decorator","be",!0),k=e?c[F-1]:void 0,T={},D={kind:["field","accessor","method","getter","setter","class"][s],name:a,metadata:E,addInitializer:function(e,t){if(e.v)throw new TypeError("attempted to call addInitializer after decoration was finished");S(t,"An initializer","be",!0),i.push(t)}.bind(null,T)};if(p)C=_.call(k,x,D),T.v=1,S(C,"class decorators","return")&&(x=C);else if(D.static=n,D.private=d,C=D.access={has:d?f.bind():function(e){return a in e}},y||(C.get=d?v?function(e){return h(e),w.value}:b("get",0,h):function(e){return e[a]}),v||g||(C.set=d?b("set",0,h):function(e,t){e[a]=t}),x=_.call(k,m?{get:w.get,set:w.set}:w[M],D),T.v=1,m){if("object"==typeof x&&x)(C=S(x.get,"accessor.get"))&&(w.get=C),(C=S(x.set,"accessor.set"))&&(w.set=C),(C=S(x.init,"accessor.init"))&&L.unshift(C);else if(void 0!==x)throw new TypeError("accessor decorators must return an object with get, set, or init properties or undefined")}else S(x,(r?"field":"method")+" decorators","return")&&(r?L.unshift(x):w[M]=x)}return s<2&&o.push(P(L,n,1),P(i,n,0)),r||p||(d?m?o.splice(-1,0,b("get",n),b("set",n)):o.push(v?w[M]:S.call.bind(w[M])):O(l,a,w)),x}function o(e){return O(e,s,{configurable:!0,enumerable:!0,value:E})}return E=i(null==(E=void 0!==e?e[s]:E)?null:E),u=[],i=function(e){e&&u.push(P(e))},(e=function(e,t){for(var a=0;a<r.length;a++){var s,i,l=r[a],o=l[1],n=7&o;(8&o)==e&&!n==t&&(s=l[2],i=!!l[3],p(e?d:d.prototype,l,16&o,i?"#"+s:(e=>"symbol"==typeof(e=((e,t)=>{if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0===a)return("string"===t?String:Number)(e);if("object"!=typeof(a=a.call(e,t||"default")))return a;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string"))?e:e+"")(s),n,n<2?[]:e?c=c||[]:h=h||[],u,!!e,i,t,e&&i?function(e){return(e=>{if(Object(e)!==e)throw TypeError("right-hand side of 'in' should be an object, got "+(null!==e?typeof e:"null"));return e})(e)===d}:f))}})(8,0),e(0,0),e(8,1),e(0,1),i(h),i(c),C=u,l||o(d),{e:C,get c(){var e=[];return l&&[o(d=p(d,[t],a,d.name,5,e)),P(e,1)]}}}function $(e,t,a){"symbol"==typeof t&&(t=(t=t.description)?"["+t+"]":"");try{Object.defineProperty(e,"name",{configurable:!0,value:a?a+" "+t:t})}catch(e){}return e}class h extends t.default{static#_=[n,d]=f(this,[],[[(0,l.inject)(o.default),0,"language"]],0,void 0,t.default).e;constructor(){super(...arguments),d(this)}language=n(this);checkAccessGlobal(){return!!this.getUser().isAdmin()}actionPage(e){let s=e.page;if(e.options&&delete(e={...Espo.Utils.parseUrlOptionsParam(e.options),...e}).options,!s)throw new Error;var t="action"+Espo.Utils.upperCaseFirst(s);if(this[t])this[t](e);else{let a=this.getPageDefs(s);if(!a)throw new Espo.Exceptions.NotFound;if(a.view)this.main(a.view,e);else{if(!a.recordView)throw new Espo.Exceptions.NotFound;let t=this.getSettingsModel();t.fetch().then(()=>{t.id="1";var e=new i.default({model:t,headerTemplate:"admin/settings/headers/page",recordView:a.recordView,page:s,label:a.label,optionsToPass:["page","label"]});this.main(e)})}}}actionIndex(e){let t=e.isReturn;var e="index",a=(!(t=this.getRouter().backProcessed?!0:t)&&this.getStoredMainView(e)&&this.clearStoredMainView(e),new s.default);this.main(a,null,e=>{e.render(),this.listenTo(e,"clear-cache",this.clearCache),this.listenTo(e,"rebuild",this.rebuild)},{useStored:t,key:e})}actionUsers(){this.getRouter().dispatch("User","list",{fromAdmin:!0})}actionPortalUsers(){this.getRouter().dispatch("PortalUser","list",{fromAdmin:!0})}actionApiUsers(){this.getRouter().dispatch("ApiUser","list",{fromAdmin:!0})}actionTeams(){this.getRouter().dispatch("Team","list",{fromAdmin:!0})}actionRoles(){this.getRouter().dispatch("Role","list",{fromAdmin:!0})}actionPortalRoles(){this.getRouter().dispatch("PortalRole","list",{fromAdmin:!0})}actionPortals(){this.getRouter().dispatch("Portal","list",{fromAdmin:!0})}actionLeadCapture(){this.getRouter().dispatch("LeadCapture","list",{fromAdmin:!0})}actionEmailFilters(){this.getRouter().dispatch("EmailFilter","list",{fromAdmin:!0})}actionGroupEmailFolders(){this.getRouter().dispatch("GroupEmailFolder","list",{fromAdmin:!0})}actionEmailTemplates(){this.getRouter().dispatch("EmailTemplate","list",{fromAdmin:!0})}actionPdfTemplates(){this.getRouter().dispatch("Template","list",{fromAdmin:!0})}actionDashboardTemplates(){this.getRouter().dispatch("DashboardTemplate","list",{fromAdmin:!0})}actionWebhooks(){this.getRouter().dispatch("Webhook","list",{fromAdmin:!0})}actionLayoutSets(){this.getRouter().dispatch("LayoutSet","list",{fromAdmin:!0})}actionWorkingTimeCalendar(){this.getRouter().dispatch("WorkingTimeCalendar","list",{fromAdmin:!0})}actionAttachments(){this.getRouter().dispatch("Attachment","list",{fromAdmin:!0})}actionAuthenticationProviders(){this.getRouter().dispatch("AuthenticationProvider","list",{fromAdmin:!0})}actionAddressCountries(){this.getRouter().dispatch("AddressCountry","list",{fromAdmin:!0})}actionEmailAddresses(){this.getRouter().dispatch("EmailAddress","list",{fromAdmin:!0})}actionPhoneNumbers(){this.getRouter().dispatch("PhoneNumber","list",{fromAdmin:!0})}actionPersonalEmailAccounts(){this.getRouter().dispatch("EmailAccount","list",{fromAdmin:!0})}actionGroupEmailAccounts(){this.getRouter().dispatch("InboundEmail","list",{fromAdmin:!0})}actionActionHistory(){this.getRouter().dispatch("ActionHistoryRecord","list",{fromAdmin:!0})}actionImport(){this.getRouter().dispatch("Import","index",{fromAdmin:!0})}actionLayouts(e){var t=e.scope||null;this.main("views/admin/layouts/index",{scope:t,type:e.type||null,em:e.em||!1})}actionLabelManager(e){var t=e.scope||null;this.main("views/admin/label-manager/index",{scope:t,language:e.language||null})}actionTemplateManager(e){e=e.name||null;this.main("views/admin/template-manager/index",{name:e})}actionFieldManager(e){var t=e.scope||null;this.main("views/admin/field-manager/index",{scope:t,field:e.field||null})}actionEntityManager(e){var t=e.scope||null;t&&e.edit?this.main("views/admin/entity-manager/edit",{scope:t}):e.create?this.main("views/admin/entity-manager/edit"):t&&e.formula?this.main("views/admin/entity-manager/formula",{scope:t,type:e.type}):t?this.main("views/admin/entity-manager/scope",{scope:t}):this.main("views/admin/entity-manager/index")}actionLinkManager(e){e=e.scope||null;this.main("views/admin/link-manager/index",{scope:e})}actionSystemRequirements(){this.main("views/admin/system-requirements/index")}getSettingsModel(){var e=this.getConfig().clone();return e.defs=this.getConfig().defs,this.listenTo(e,"after:save",()=>{this.getConfig().load(),this._broadcastChannel.postMessage("update:config")}),e}actionAuthTokens(){this.collectionFactory.create("AuthToken",e=>{var t=new a.default(e,{storageKey:"list"});t.loadStored(),e.where=t.getWhere(),e.maxSize=this.getConfig().get("recordsPerPage")||e.maxSize,this.main("views/admin/auth-token/list",{scope:"AuthToken",collection:e,searchManager:t})})}actionAuthLog(){this.collectionFactory.create("AuthLogRecord",e=>{var t=new a.default(e,{storageKey:"list"});t.loadStored(),e.where=t.getWhere(),e.maxSize=this.getConfig().get("recordsPerPage")||e.maxSize,this.main("views/admin/auth-log-record/list",{scope:"AuthLogRecord",collection:e,searchManager:t})})}actionAppSecrets(){this.getRouter().dispatch("AppSecret","list",{fromAdmin:!0})}actionOAuthProviders(){this.getRouter().dispatch("OAuthProvider","list",{fromAdmin:!0})}actionJobs(){this.collectionFactory.create("Job",e=>{var t=new a.default(e,{storageKey:"list"});t.loadStored(),e.where=t.getWhere(),e.maxSize=this.getConfig().get("recordsPerPage")||e.maxSize,this.main("views/admin/job/list",{scope:"Job",collection:e,searchManager:t})})}actionAppLog(){this.collectionFactory.create("AppLogRecord",e=>{var t=new a.default(e,{storageKey:"list"});t.loadStored(),e.where=t.getWhere(),e.maxSize=this.getConfig().get("recordsPerPage")||e.maxSize,this.main("views/list",{scope:"AppLogRecord",collection:e,searchManager:t})})}actionIntegrations(e){e=e.name||null;this.main("views/admin/integrations/index",{integration:e})}actionExtensions(){this.main("views/admin/extensions/index")}rebuild(){this.rebuildRunning||(this.rebuildRunning=!0,Espo.Ui.notify(this.language.translate("pleaseWait","messages")),Espo.Ajax.postRequest("Admin/rebuild").then(()=>{var e=this.language.translate("Rebuild has been done","labels","Admin");Espo.Ui.success(e),this.rebuildRunning=!1}).catch(()=>{this.rebuildRunning=!1}))}clearCache(){this.clearCacheRunning||(this.clearCacheRunning=!0,Espo.Ui.notify(this.language.translate("pleaseWait","messages")),Espo.Ajax.postRequest("Admin/clearCache").then(()=>{var e=this.language.translate("Cache has been cleared","labels","Admin");Espo.Ui.success(e),this.clearCacheRunning=!1}).catch(()=>{this.clearCacheRunning=!1}))}getPageDefs(e){var t,a,s=this.getMetadata().get(["app","adminPanel"])||{};let i=null;for(t in s){for(a of s[t].itemList||[])if(a.url==="#Admin/"+e){i=a;break}if(i)break}return i}}e.default=h});
//# sourceMappingURL=espo-admin.js.map