<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 20px;
            line-height: 1.2;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .company-info {
            font-weight: bold;
        }
        
        .order-number {
            border: 2px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            background-color: #f0f0f0;
        }
        
        .addresses {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .address-section {
            width: 45%;
        }
        
        .address-header {
            background-color: #d0d0d0;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #000;
        }
        
        .address-content {
            padding: 10px 5px;
            border-left: 1px solid #000;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            min-height: 80px;
        }
        
        .order-details {
            margin-bottom: 20px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 2px;
        }
        
        .detail-label {
            width: 120px;
            font-weight: bold;
        }
        
        .detail-value {
            flex: 1;
            border: 1px solid #000;
            padding: 2px 5px;
            background-color: #f9f9f9;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th {
            background-color: #d0d0d0;
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            font-weight: bold;
        }
        
        .items-table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            vertical-align: top;
        }
        
        .items-table td.text-left {
            text-align: left;
        }
        
        .items-table td.text-right {
            text-align: right;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        
        .remarks {
            margin-top: 20px;
        }
        
        .remarks-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .currency {
            font-family: monospace;
        }
    </style>
</head>
<body>

{{#if showCoverPage}}
<!-- Cover Page für BWS -->
<div class="cover-page">
    <h1 style="text-align: center; margin-bottom: 30px;">{{coverPageData.sender}}</h1>
    <div style="text-align: center; font-size: 14px;">
        <p><strong>Datum der Generierung:</strong> {{coverPageData.generationDate}}</p>
        <p><strong>Gesamtanzahl der Seiten:</strong> {{coverPageData.totalPages}}</p>
        <p><strong>Gesamtsumme:</strong> <span class="currency">{{coverPageData.totalSubtotal}} €</span></p>
        <p><strong>Anzahl SalesOrder:</strong> {{coverPageData.salesOrderCount}}</p>
    </div>
</div>
<div class="page-break"></div>
{{/if}}

{{#group brandItems by="salesOrderNumber"}}
<!-- Neue Seite für jeden SalesOrder -->
{{#if @index}}<div class="page-break"></div>{{/if}}

<div class="header">
    <div class="company-info">
        YPEK GmbH, V550
    </div>
    <div class="order-number">
        Auftrag Nr. {{key}}
    </div>
</div>

<div class="addresses">
    <div class="address-section">
        <div class="address-header">Rechnungsadresse</div>
        <div class="address-content">
            <strong>Firma:</strong> {{items.0.accountName}}<br>
            <strong>Geschäftsführer:</strong> CEO<br>
            {{items.0.billingAddress}}
        </div>
    </div>
    
    <div class="address-section">
        <div class="address-header">Lieferadresse</div>
        <div class="address-content">
            <strong>Firma:</strong> {{items.0.accountName}}<br>
            {{items.0.shippingAddress}}
        </div>
    </div>
</div>

<div class="order-details">
    <div class="detail-row">
        <div class="detail-label">Bestelldatum:</div>
        <div class="detail-value">{{formatDate items.0.createdAt 'YYYY-MM-DD HH:mm:ss'}}</div>
    </div>
    <div class="detail-row">
        <div class="detail-label">Lieferdatum:</div>
        <div class="detail-value">{{#if items.0.deliveryDate}}{{formatDate items.0.deliveryDate 'YYYY-MM-DD'}}{{/if}}</div>
    </div>
    <div class="detail-row">
        <div class="detail-label">Untervertreter:</div>
        <div class="detail-value">398</div>
    </div>
    <div class="detail-row">
        <div class="detail-label">Kampagne:</div>
        <div class="detail-value">BESTANDK</div>
    </div>
    <div class="detail-row">
        <div class="detail-label">AdressID BWS:</div>
        <div class="detail-value"></div>
    </div>
    <div class="detail-row">
        <div class="detail-label">Haustier:</div>
        <div class="detail-value">keine</div>
    </div>
</div>

<table class="items-table">
    <thead>
        <tr>
            <th style="width: 8%;">Pos.</th>
            <th style="width: 15%;">Artikelnr.</th>
            <th style="width: 47%;">Artikelbezeichnung</th>
            <th style="width: 10%;">Menge</th>
            <th style="width: 10%;">Stückpreis</th>
            <th style="width: 10%;">Gesamt</th>
        </tr>
    </thead>
    <tbody>
        {{#each items}}
        <tr>
            <td>{{add @index 1}}</td>
            <td>{{productNumber}}</td>
            <td class="text-left">{{productName}}</td>
            <td class="text-right">{{formatNumber quantity '0.000'}}</td>
            <td class="text-right currency">{{formatNumber unitPrice '0.00'}} €</td>
            <td class="text-right currency">{{formatNumber amount '0.00'}} €</td>
        </tr>
        {{/each}}
        
        <!-- Filas vacías para completar la tabla -->
        {{#repeat (subtract 15 items.length)}}
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        {{/repeat}}
        
        <!-- Fila de total -->
        <tr class="total-row">
            <td colspan="4"></td>
            <td><strong>Gesamt:</strong></td>
            <td class="text-right currency"><strong>{{formatNumber items.0.salesOrderAmount '0.00'}} €</strong></td>
        </tr>
    </tbody>
</table>

<div class="remarks">
    <div class="remarks-label">Bemerkung:</div>
    <div style="min-height: 40px; border: 1px solid #ccc; padding: 5px;">
        <!-- Espacio para observaciones adicionales -->
    </div>
</div>

{{/group}}

</body>
</html>
