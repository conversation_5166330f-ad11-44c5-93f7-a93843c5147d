# Template PDF para SalesOrder - Documentación

## 📄 Archivo: `pdf_template.html`

Este template HTML genera PDFs de SalesOrder que replican exactamente el diseño alemán mostrado en la imagen de referencia.

## 🎯 Características del Template

### ✅ **Cover Page (Solo para BWS)**
- Se muestra solo cuando `showCoverPage = true`
- Incluye información de generación y totales
- Datos de `coverPageData.*`

### ✅ **Diseño por SalesOrder**
- **Una página por SalesOrder**
- Header con "YPEK GmbH, V550" y número de orden
- Direcciones de facturación y entrega lado a lado
- Detalles de orden (fechas, campaña, etc.)
- Tabla de items con formato alemán
- Sección de observaciones

### ✅ **Variables Utilizadas**

#### Variables del ServiceAction:
```handlebars
{{showCoverPage}} - true para BWS
{{coverPageData.sender}} - "YPEK GmbH"
{{coverPageData.generationDate}} - Fecha de generación
{{coverPageData.totalPages}} - Total páginas
{{coverPageData.totalSubtotal}} - Suma total
{{coverPageData.salesOrderCount}} - Cantidad SalesOrder
```

#### Variables de Items (agrupados por SalesOrder):
```handlebars
{{#group brandItems by="salesOrderNumber"}}
  {{key}} - Número del SalesOrder
  {{items.0.accountName}} - Nombre del cliente
  {{items.0.billingAddress}} - Dirección facturación
  {{items.0.shippingAddress}} - Dirección entrega
  {{items.0.createdAt}} - Fecha creación
  {{items.0.deliveryDate}} - Fecha entrega
  {{items.0.salesOrderAmount}} - Total del SalesOrder
  
  {{#each items}}
    {{productNumber}} - Número producto
    {{productName}} - Nombre producto
    {{quantity}} - Cantidad
    {{unitPrice}} - Precio unitario
    {{amount}} - Total item
  {{/each}}
{{/group}}
```

## 🔧 Funciones Helper Necesarias

El template usa funciones helper de Handlebars que deben estar disponibles:

```javascript
// Formatear fechas
{{formatDate createdAt 'YYYY-MM-DD HH:mm:ss'}}
{{formatDate deliveryDate 'YYYY-MM-DD'}}

// Formatear números
{{formatNumber quantity '0.000'}}
{{formatNumber unitPrice '0.00'}}
{{formatNumber amount '0.00'}}

// Operaciones matemáticas
{{add @index 1}}
{{subtract 15 items.length}}

// Agrupar items por SalesOrder
{{#group brandItems by="salesOrderNumber"}}

// Repetir filas vacías
{{#repeat count}}
```

## 📋 Estructura del PDF Generado

### **Para BWS (con Cover Page):**
1. **Página 1:** Cover Page con estadísticas
2. **Página 2+:** Una página por cada SalesOrder

### **Para BBH (sin Cover Page):**
1. **Página 1+:** Una página por cada SalesOrder

## 🎨 Estilos CSS Implementados

- **Layout responsive** con flexbox
- **Bordes y fondos** que replican el diseño original
- **Tipografía** Arial con tamaños apropiados
- **Tablas** con bordes y headers grises
- **Saltos de página** automáticos entre SalesOrder
- **Formato monospace** para valores monetarios

## 📊 Ejemplo de Uso en EspoCRM

1. **Crear Template PDF** en EspoCRM:
   - Ir a Administration > PDF Templates
   - Crear nuevo template para entidad "SalesOrder"
   - Copiar el contenido de `pdf_template.html`
   - Guardar con nombre descriptivo (ej: "SalesOrder BWS Report")

2. **Configurar ServiceAction:**
   - El ServiceAction ya está configurado para usar este template
   - Pasar el `pdfTemplateId` correcto en los parámetros

3. **Variables disponibles automáticamente:**
   - Todas las variables del ServiceAction están disponibles
   - Los items están pre-filtrados por brand
   - Los datos están agrupados por SalesOrder

## 🚀 Resultado Final

El PDF generado será idéntico al diseño de la imagen:
- ✅ Header con YPEK GmbH y número de orden
- ✅ Direcciones lado a lado con headers grises
- ✅ Detalles de orden en formato tabla
- ✅ Tabla de items con columnas alemanas
- ✅ Totales y observaciones
- ✅ Una página por SalesOrder
- ✅ Cover page para BWS (opcional)

## 📝 Notas Técnicas

- El template usa `{{#group}}` para agrupar items por SalesOrder
- Cada grupo genera una página separada
- Las filas vacías completan la tabla hasta 15 líneas
- Los formatos de fecha y número deben configurarse en EspoCRM
- El CSS está optimizado para generación PDF
