# Instalación y Configuración del ServiceAction GenerateAndSendPdf

## Archivos creados

El ServiceAction incluye los siguientes archivos:

```
zwischenpuls/custom/Espo/Custom/ServiceActions/
├── GenerateAndSendPdf.php          # Clase principal del ServiceAction
├── README_GenerateAndSendPdf.md    # Documentación técnica
├── EXAMPLE_Usage.md                # Ejemplos de uso
├── INSTALLATION.md                 # Este archivo
└── Tests/
    └── GenerateAndSendPdfTest.php  # Tests unitarios
```

## Requisitos del sistema

- EspoCRM con Advanced Pack instalado
- Módulo de Workflows activo
- Configuración SMTP funcional para envío de emails
- Templates PDF configurados en el sistema

## Instalación

### 1. Verificar ubicación de archivos

Los archivos deben estar ubicados en:
```
[ESPOCRM_ROOT]/custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php
```

### 2. Limpiar caché

Después de crear los archivos, ejecutar:

```bash
cd [ESPOCRM_ROOT]
php rebuild.php
```

O desde la interfaz web:
- Ir a Administration > Rebuild
- Hacer clic en "Rebuild"

### 3. Verificar permisos

Asegurar que los archivos tengan los permisos correctos:

```bash
chmod 644 custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php
chown www-data:www-data custom/Espo/Custom/ServiceActions/GenerateAndSendPdf.php
```

## Configuración

### 1. Crear Templates PDF

Antes de usar el ServiceAction, crear templates PDF:

1. Ir a Administration > PDF Templates
2. Crear un nuevo template
3. Configurar el template para la entidad deseada
4. Copiar el ID del template para usar en el ServiceAction

### 2. Configurar SMTP

Verificar que el sistema tenga configuración SMTP:

1. Ir a Administration > Outbound Emails
2. Configurar servidor SMTP
3. Probar envío de email

### 3. Crear Workflow

1. Ir a Administration > Workflows
2. Crear nuevo Workflow
3. Seleccionar entidad objetivo
4. Configurar condiciones de activación
5. Agregar acción "Run Service Action"
6. Especificar clase: `GenerateAndSendPdf`
7. Configurar datos JSON

## Verificación de instalación

### 1. Verificar que la clase se carga correctamente

Crear un Workflow de prueba con datos mínimos:

```json
{
  "pdfTemplateId": "ID_DE_TEMPLATE_EXISTENTE",
  "emailAddresses": "<EMAIL>"
}
```

### 2. Revisar logs

Verificar en `data/logs/espo.log` que no hay errores de carga de clase.

### 3. Probar funcionalidad

1. Crear o modificar una entidad que active el Workflow
2. Verificar que se genera el PDF
3. Verificar que se envía el email
4. Revisar la entidad Email creada en el sistema

## Troubleshooting

### Error: "Class not found"

**Causa:** La clase no se está cargando correctamente.

**Solución:**
1. Verificar ubicación del archivo
2. Verificar namespace correcto
3. Ejecutar rebuild
4. Verificar permisos de archivo

### Error: "PDF Template not found"

**Causa:** El ID del template PDF no existe.

**Solución:**
1. Verificar que el template existe en Administration > PDF Templates
2. Copiar el ID correcto del template
3. Verificar que el template sea compatible con la entidad

### Error: "SMTP not configured"

**Causa:** No hay configuración SMTP en el sistema.

**Solución:**
1. Configurar SMTP en Administration > Outbound Emails
2. Probar configuración enviando un email de prueba

### Error: "Permission denied"

**Causa:** Problemas de permisos de archivo.

**Solución:**
```bash
chmod -R 644 custom/Espo/Custom/ServiceActions/
chown -R www-data:www-data custom/Espo/Custom/ServiceActions/
```

## Personalización

### Modificar templates de email

Para personalizar el contenido de los emails, editar los métodos:
- `generateEmailSubject()`
- `generateEmailBody()`

### Modificar nomenclatura de archivos

Para cambiar cómo se nombran los archivos PDF, editar el método:
- `generateFileName()`

### Agregar validaciones adicionales

Para agregar más validaciones, modificar el método:
- `run()` al inicio

## Mantenimiento

### Logs

El ServiceAction registra errores en el log estándar de EspoCRM:
- Ubicación: `data/logs/espo.log`
- Nivel: Error para fallos críticos

### Monitoreo

Revisar periódicamente:
1. Logs de errores
2. Emails enviados vs. esperados
3. Attachments creados
4. Espacio en disco (los PDFs se almacenan permanentemente)

### Actualizaciones

Al actualizar EspoCRM:
1. Hacer backup de los archivos custom
2. Verificar compatibilidad después de la actualización
3. Ejecutar rebuild si es necesario

## Soporte

Para problemas específicos:
1. Revisar logs del sistema
2. Verificar configuración SMTP
3. Probar con datos mínimos
4. Verificar permisos y ubicación de archivos
