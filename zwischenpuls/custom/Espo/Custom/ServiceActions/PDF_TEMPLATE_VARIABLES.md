# Variables disponibles en templates PDF para GenerateAndSendPdf

## Variables del ServiceAction

Cuando uses el ServiceAction `GenerateAndSendPdf`, las siguientes variables estarán disponibles en tu template PDF:

### Variables configurables:
- `{{brand}}` - La marca especificada en el parámetro `brand`
- `{{status}}` - El estado especificado en el parámetro `status`
- `{{withDate}}` - Booleano que indica si se debe incluir fecha (true/false)

### Variables automáticas:
- `{{currentDate}}` - Fecha actual en formato Y-m-d (ej: 2024-01-15)
- `{{currentDateTime}}` - Fecha y hora actual en formato Y-m-d H:i:s (ej: 2024-01-15 14:30:25)

## Ejemplo de uso en template PDF

```html
<h1>{{brand}} - Documento {{entityType}}</h1>

<p><strong>Entidad:</strong> {{name}}</p>
<p><strong>Estado:</strong> {{status}}</p>
<p><strong>Fecha de generación:</strong> {{currentDate}}</p>

{{#if withDate}}
<p><em>Documento generado con fecha: {{currentDateTime}}</em></p>
{{/if}}

<div class="content">
    <!-- Contenido del documento -->
    <p>Este documento ha sido generado automáticamente para {{brand}}.</p>
</div>
```

## Configuración del ServiceAction

```json
{
  "pdfTemplateId": "tu-template-id",
  "emailAddresses": "<EMAIL>",
  "brand": "Mi Empresa",
  "status": "Aprobado",
  "withDate": true
}
```

## Resultado en el PDF

Con la configuración anterior, las variables se resolverán así:
- `{{brand}}` → "Mi Empresa"
- `{{status}}` → "Aprobado"
- `{{withDate}}` → true
- `{{currentDate}}` → "2024-01-15"
- `{{currentDateTime}}` → "2024-01-15 14:30:25"

## Notas importantes

1. Las variables están disponibles además de todas las variables normales de la entidad
2. Si una variable no se proporciona (ej: brand es null), aparecerá vacía en el template
3. Puedes usar condicionales Handlebars para mostrar contenido solo si la variable existe
4. Las fechas se generan en el momento de crear el PDF, no cuando se configura el workflow
