<?php

namespace Espo\Custom\ServiceActions;

use Espo\Core\Exceptions\Error;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Core\Select\SelectBuilderFactory;
use Espo\Core\Utils\DateTime;
use Espo\Core\Utils\Log;
use Espo\Core\Utils\Util;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\Modules\Advanced\Tools\Workflow\Action\RunAction\ServiceAction;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\Data;
use Espo\Tools\Pdf\Params;
use Espo\Tools\Pdf\Service as PdfService;
use stdClass;

class GenerateAndSendPdf implements ServiceAction
{
    public function __construct(
        private EntityManager $entityManager,
        private InjectableFactory $injectableFactory,
        private EmailSender $emailSender,
        private FileStorageManager $fileStorageManager,
        private SelectBuilderFactory $selectBuilderFactory,
        private Log $log
    ) {}

    public function run(Entity $entity, mixed $data): mixed
    {
        $this->log->info('GenerateAndSendPdf: Starting execution', ['entityId' => $entity->getId()]);

        if (!is_object($data)) {
            $this->log->error('GenerateAndSendPdf: Data must be an object', ['dataType' => gettype($data)]);
            throw new Error("Data must be an object");
        }

        $pdfTemplateId = $data->pdfTemplateId ?? null;
        $emailAddresses = $data->emailAddresses ?? [];
        $brand = $data->brand ?? null;
        $withDate = $data->withDate ?? false;

        $this->log->info('GenerateAndSendPdf: Parameters received', [
            'pdfTemplateId' => $pdfTemplateId,
            'emailAddresses' => $emailAddresses,
            'brand' => $brand,
            'withDate' => $withDate
        ]);

        if (!$pdfTemplateId) {
            $this->log->error('GenerateAndSendPdf: PDF Template ID is required');
            throw new Error("PDF Template ID is required");
        }

        if (empty($emailAddresses)) {
            $this->log->error('GenerateAndSendPdf: Email addresses are required');
            throw new Error("Email addresses are required");
        }

        if (is_string($emailAddresses)) {
            $emailAddresses = array_filter(array_map('trim', explode(',', $emailAddresses)));
        } elseif (!is_array($emailAddresses)) {
            $emailAddresses = [$emailAddresses];
        }

        $emailAddresses = array_filter($emailAddresses, fn($email) => !empty($email));
        $this->log->info('GenerateAndSendPdf: Email addresses processed', ['count' => count($emailAddresses), 'emails' => $emailAddresses]);

        // Buscar SalesOrder que cumplan con los filtros
        $this->log->info('GenerateAndSendPdf: Searching for SalesOrders', ['brand' => $brand, 'withDate' => $withDate]);
        $salesOrders = $this->findSalesOrders($brand, $withDate);
        $this->log->info('GenerateAndSendPdf: SalesOrders found', ['count' => count($salesOrders)]);

        if (empty($salesOrders)) {
            $this->log->warning('GenerateAndSendPdf: No SalesOrders found matching criteria');
            return [
                'success' => true,
                'message' => 'No SalesOrder found matching criteria',
                'emailsSent' => 0
            ];
        }

        // Generar 1 solo PDF con todos los SalesOrder encontrados
        $this->log->info('GenerateAndSendPdf: Starting PDF generation');
        $attachment = $this->generatePdf($salesOrders, $pdfTemplateId, $data);
        $this->log->info('GenerateAndSendPdf: PDF generated successfully', ['attachmentId' => $attachment->getId()]);

        $this->log->info('GenerateAndSendPdf: Starting email sending');
        $this->sendEmail($attachment, $emailAddresses, $data->status ?? null, $brand, count($salesOrders));
        $this->log->info('GenerateAndSendPdf: Email sending completed');

        $this->log->info('GenerateAndSendPdf: Execution completed successfully');
        return [
            'success' => true,
            'attachmentId' => $attachment->getId(),
            'salesOrdersProcessed' => count($salesOrders),
            'emailsSent' => count($emailAddresses)
        ];
    }

    private function findSalesOrders(?string $brand, bool $withDate): array
    {
        $this->log->info('GenerateAndSendPdf: findSalesOrders called', ['brand' => $brand, 'withDate' => $withDate]);

        // Usar directamente el repositorio para hacer la consulta
        $repository = $this->entityManager->getRDBRepository('SalesOrder');

        $whereClause = [
            'status' => 'Draft'
        ];

        // Filtro por deliveryDate
        if ($withDate) {
            $whereClause['deliveryDate!='] = null;
        } else {
            $whereClause['OR'] = [
                ['deliveryDate=' => null],
                ['deliveryDate=' => '']
            ];
        }

        $selectParams = [
            'whereClause' => $whereClause,
            'orderBy' => 'number',
            'order' => 'ASC'
        ];

        $this->log->info('GenerateAndSendPdf: Executing SalesOrder query', ['whereClause' => $whereClause]);
        $collection = $repository->find($selectParams);
        $salesOrders = iterator_to_array($collection);
        $this->log->info('GenerateAndSendPdf: Initial SalesOrders found', ['count' => count($salesOrders)]);

        // Si hay filtro por brand, filtrar los SalesOrder que tengan items de ese brand
        if ($brand) {
            $this->log->info('GenerateAndSendPdf: Filtering by brand', ['brand' => $brand]);
            $filteredSalesOrders = [];
            foreach ($salesOrders as $salesOrder) {
                if ($this->salesOrderHasBrandItems($salesOrder, $brand)) {
                    $filteredSalesOrders[] = $salesOrder;
                    $this->log->debug('GenerateAndSendPdf: SalesOrder has brand items', ['salesOrderId' => $salesOrder->getId()]);
                }
            }
            $this->log->info('GenerateAndSendPdf: Filtered SalesOrders by brand', ['count' => count($filteredSalesOrders)]);
            return $filteredSalesOrders;
        }

        return $salesOrders;
    }

    private function salesOrderHasBrandItems(Entity $salesOrder, string $brand): bool
    {
        $salesOrderId = $salesOrder->getId();
        $this->log->debug('GenerateAndSendPdf: Checking brand items for SalesOrder', ['salesOrderId' => $salesOrderId, 'brand' => $brand]);

        $itemCollection = $this->entityManager
            ->getRDBRepository('SalesOrderItem')
            ->where(['salesOrderId' => $salesOrderId])
            ->find();

        $itemCount = count($itemCollection);
        $this->log->debug('GenerateAndSendPdf: Items found for SalesOrder', ['salesOrderId' => $salesOrderId, 'itemCount' => $itemCount]);

        foreach ($itemCollection as $item) {
            $product = $item->get('product');
            if ($product) {
                $productBrand = $product->get('brand');
                $this->log->debug('GenerateAndSendPdf: Checking item product brand', [
                    'itemId' => $item->getId(),
                    'productId' => $product->getId(),
                    'productBrand' => $productBrand,
                    'targetBrand' => $brand
                ]);

                if ($productBrand === $brand) {
                    $this->log->debug('GenerateAndSendPdf: Brand match found', ['salesOrderId' => $salesOrderId]);
                    return true;
                }
            } else {
                $this->log->warning('GenerateAndSendPdf: Item has no product', ['itemId' => $item->getId()]);
            }
        }

        $this->log->debug('GenerateAndSendPdf: No brand match found', ['salesOrderId' => $salesOrderId]);
        return false;
    }

    private function generatePdf(array $salesOrders, string $templateId, object $inputData): Attachment
    {
        $this->log->info('GenerateAndSendPdf: generatePdf called', [
            'salesOrderCount' => count($salesOrders),
            'templateId' => $templateId
        ]);

        if (empty($salesOrders)) {
            $this->log->error('GenerateAndSendPdf: No SalesOrder to generate PDF');
            throw new Error("No SalesOrder to generate PDF");
        }

        // Preparar datos adicionales para el template
        $this->log->info('GenerateAndSendPdf: Preparing template data');
        $templateData = $this->prepareTemplateData($salesOrders, $inputData);
        $this->log->info('GenerateAndSendPdf: Template data prepared', [
            'brandItemsCount' => count($templateData->brandItems ?? []),
            'brand' => $templateData->brand ?? null,
            'totalAmount' => $templateData->totalAmount ?? null
        ]);

        $data = Data::create()->withAdditionalTemplateData($templateData);

        // Usar el primer SalesOrder como base para generar el PDF
        $firstSalesOrder = $salesOrders[0];
        $this->log->info('GenerateAndSendPdf: Using first SalesOrder as base', [
            'salesOrderId' => $firstSalesOrder->getId(),
            'salesOrderNumber' => $firstSalesOrder->get('number')
        ]);

        $pdfService = $this->injectableFactory->create(PdfService::class);
        $params = Params::create()->withAcl(false);

        try {
            $this->log->info('GenerateAndSendPdf: Calling PDF service generate');
            $contents = $pdfService->generate(
                $firstSalesOrder->getEntityType(),
                $firstSalesOrder->getId(),
                $templateId,
                $params,
                $data
            );
            $this->log->info('GenerateAndSendPdf: PDF content generated successfully');

            $fileName = $this->generateFileName($inputData);
            $this->log->info('GenerateAndSendPdf: Generated filename', ['fileName' => $fileName]);

            $attachment = $this->entityManager->getNewEntity(Attachment::ENTITY_TYPE);

            $attachment
                ->setName($fileName)
                ->setType('application/pdf')
                ->setSize($contents->getStream()->getSize())
                ->setRole(Attachment::ROLE_ATTACHMENT);

            $this->log->info('GenerateAndSendPdf: Saving attachment entity');
            $this->entityManager->saveEntity($attachment);
            $this->log->info('GenerateAndSendPdf: Storing file content');
            $this->fileStorageManager->putStream($attachment, $contents->getStream());
            $this->log->info('GenerateAndSendPdf: Attachment created successfully', ['attachmentId' => $attachment->getId()]);

            return $attachment;
        } catch (\Exception $e) {
            $this->log->error('GenerateAndSendPdf: Error generating PDF', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new Error("Error generating PDF: " . $e->getMessage());
        }
    }

    private function prepareTemplateData(array $salesOrders, object $inputData): stdClass
    {
        $this->log->info('GenerateAndSendPdf: prepareTemplateData called', ['salesOrderCount' => count($salesOrders)]);

        $brand = $inputData->brand ?? null;
        $currentDate = DateTime::createNow()->format('Y-m-d');
        $currentDateTime = DateTime::createNow()->format('Y-m-d H:i:s');

        // Preparar datos básicos
        $templateData = new stdClass();

        // Variables del ServiceAction
        foreach (get_object_vars($inputData) as $key => $value) {
            if (!in_array($key, ['pdfTemplateId', 'emailAddresses'])) {
                $templateData->$key = $value;
            }
        }
        $this->log->debug('GenerateAndSendPdf: Input data variables copied', ['variables' => array_keys(get_object_vars($templateData))]);

        // Variables de fecha/hora
        $templateData->currentDate = $currentDate;
        $templateData->currentDateTime = $currentDateTime;
        $templateData->generationDate = $currentDateTime;

        // Estadísticas generales
        $templateData->totalSalesOrders = count($salesOrders);
        $templateData->sender = 'YPEK GmbH';

        // Preparar array plano de items filtrados por brand
        $this->log->info('GenerateAndSendPdf: Getting brand items', ['brand' => $brand]);
        $brandItems = $this->getBrandItems($salesOrders, $brand);
        $templateData->brandItems = $brandItems;
        $this->log->info('GenerateAndSendPdf: Brand items prepared', ['count' => count($brandItems)]);

        // Calcular totales de items
        $totalAmount = 0;
        $totalItems = count($brandItems);

        foreach ($brandItems as $item) {
            $totalAmount += $item->amount ?? 0;
        }

        $templateData->totalAmount = $totalAmount;
        $templateData->totalItems = $totalItems;

        // Agregar sufijo _RAW para campos que necesitan formateo
        $templateData->totalAmount_RAW = $totalAmount;
        $templateData->currentDate_RAW = $currentDate;
        $templateData->currentDateTime_RAW = $currentDateTime;

        $this->log->info('GenerateAndSendPdf: Template data prepared successfully', [
            'totalItems' => $totalItems,
            'totalAmount' => $totalAmount,
            'brand' => $brand
        ]);

        // Variables específicas para BWS (cover page)
        if ($brand === 'BWS') {
            $templateData->showCoverPage = true;
            $templateData->coverPageData = $this->prepareCoverPageData($salesOrders, $currentDateTime);
        } else {
            $templateData->showCoverPage = false;
        }

        return $templateData;
    }

    private function generateFileName(object $inputData): string
    {
        $brand = $inputData->brand ?? 'unknown';
        $withDate = $inputData->withDate ?? false;
        $currentDate = DateTime::createNow()->format('Y-m-d');

        $dateStatus = $withDate ? 'with-date' : 'no-date';

        // Formato: salesorders_BWS_no-date_2025-05-28.pdf
        $fileName = "salesorders_{$brand}_{$dateStatus}_{$currentDate}";
        $fileName = Util::sanitizeFileName($fileName);

        return $fileName . '.pdf';
    }

    private function getBrandItems(array $salesOrders, ?string $brand): array
    {
        $this->log->info('GenerateAndSendPdf: getBrandItems called', [
            'salesOrderCount' => count($salesOrders),
            'brand' => $brand
        ]);

        $brandItems = [];

        foreach ($salesOrders as $salesOrder) {
            $salesOrderId = $salesOrder->getId();
            $this->log->debug('GenerateAndSendPdf: Processing SalesOrder', ['salesOrderId' => $salesOrderId]);

            // Cargar datos de cuenta/contacto una vez por SalesOrder
            $account = $salesOrder->get('account');
            $accountName = $account ? $account->get('name') : '';
            $billingAddress = $account ? $account->get('billingAddress') : '';
            $shippingAddress = $account ? $account->get('shippingAddress') : '';

            $this->log->debug('GenerateAndSendPdf: Account data loaded', [
                'salesOrderId' => $salesOrderId,
                'accountName' => $accountName
            ]);

            // Cargar items del SalesOrder
            $itemCollection = $this->entityManager
                ->getRDBRepository('SalesOrderItem')
                ->where(['salesOrderId' => $salesOrderId])
                ->find();

            $itemCount = count($itemCollection);
            $this->log->debug('GenerateAndSendPdf: Items loaded for SalesOrder', [
                'salesOrderId' => $salesOrderId,
                'itemCount' => $itemCount
            ]);

            foreach ($itemCollection as $item) {
                $product = $item->get('product');

                if ($product) {
                    $productBrand = $product->get('brand');
                    $this->log->debug('GenerateAndSendPdf: Processing item', [
                        'itemId' => $item->getId(),
                        'productId' => $product->getId(),
                        'productBrand' => $productBrand,
                        'targetBrand' => $brand
                    ]);

                    // Solo incluir items cuyo producto tenga el brand especificado
                    if (!$brand || $productBrand === $brand) {
                        $itemData = new stdClass();

                        // Datos del item
                        $itemData->itemId = $item->getId();
                        $itemData->quantity = $item->get('quantity');
                        $itemData->quantity_RAW = $item->get('quantity');
                        $itemData->unitPrice = $item->get('unitPrice');
                        $itemData->unitPrice_RAW = $item->get('unitPrice');
                        $itemData->amount = $item->get('amount');
                        $itemData->amount_RAW = $item->get('amount');

                        // Datos del producto
                        $itemData->productName = $product->get('name');
                        $itemData->productNumber = $product->get('productNumber') ?? $product->get('sku');
                        $itemData->productBrand = $productBrand;

                        // Datos del SalesOrder
                        $itemData->salesOrderId = $salesOrderId;
                        $itemData->salesOrderNumber = $salesOrder->get('number');
                        $itemData->salesOrderAmount = $salesOrder->get('amount');
                        $itemData->salesOrderAmount_RAW = $salesOrder->get('amount');
                        $itemData->salesOrderStatus = $salesOrder->get('status');
                        $itemData->deliveryDate = $salesOrder->get('deliveryDate');
                        $itemData->deliveryDate_RAW = $salesOrder->get('deliveryDate');
                        $itemData->createdAt = $salesOrder->get('createdAt');
                        $itemData->createdAt_RAW = $salesOrder->get('createdAt');

                        // Datos del cliente
                        $itemData->accountName = $accountName;
                        $itemData->billingAddress = $billingAddress;
                        $itemData->shippingAddress = $shippingAddress;

                        $brandItems[] = $itemData;
                        $this->log->debug('GenerateAndSendPdf: Item added to brandItems', ['itemId' => $item->getId()]);
                    } else {
                        $this->log->debug('GenerateAndSendPdf: Item filtered out by brand', [
                            'itemId' => $item->getId(),
                            'productBrand' => $productBrand,
                            'targetBrand' => $brand
                        ]);
                    }
                } else {
                    $this->log->warning('GenerateAndSendPdf: Item has no product', ['itemId' => $item->getId()]);
                }
            }
        }

        $this->log->info('GenerateAndSendPdf: getBrandItems completed', ['totalBrandItems' => count($brandItems)]);
        return $brandItems;
    }

    private function prepareCoverPageData(array $salesOrders, string $currentDateTime): stdClass
    {
        $coverData = new stdClass();
        $coverData->sender = 'YPEK GmbH';
        $coverData->generationDate = $currentDateTime;
        $coverData->generationDate_RAW = $currentDateTime;
        $coverData->totalPages = count($salesOrders); // Aproximación

        $totalSubtotal = 0;
        foreach ($salesOrders as $salesOrder) {
            $totalSubtotal += $salesOrder->get('amount') ?? 0;
        }
        $coverData->totalSubtotal = $totalSubtotal;
        $coverData->totalSubtotal_RAW = $totalSubtotal;
        $coverData->salesOrderCount = count($salesOrders);

        return $coverData;
    }

    private function sendEmail(Attachment $attachment, array $emailAddresses, ?string $status, ?string $brand, int $salesOrderCount): void
    {
        $this->log->info('GenerateAndSendPdf: sendEmail called', [
            'attachmentId' => $attachment->getId(),
            'emailAddresses' => $emailAddresses,
            'status' => $status,
            'brand' => $brand,
            'salesOrderCount' => $salesOrderCount
        ]);

        $emailAddresses = array_filter(array_map('trim', $emailAddresses), fn($email) => !empty($email));
        $this->log->info('GenerateAndSendPdf: Email addresses processed', ['cleanedAddresses' => $emailAddresses]);

        if (empty($emailAddresses)) {
            $this->log->warning('GenerateAndSendPdf: No valid email addresses found');
            return;
        }

        /** @var Email $email */
        $email = $this->entityManager->getNewEntity(Email::ENTITY_TYPE);
        $subject = $this->generateEmailSubject($brand, $status, $salesOrderCount);
        $body = $this->generateEmailBody($brand, $status, $salesOrderCount);

        $this->log->info('GenerateAndSendPdf: Email content generated', [
            'subject' => $subject,
            'bodyLength' => strlen($body)
        ]);

        $email
            ->setSubject($subject)
            ->setBody($body)
            ->setIsHtml(true)
            ->set('attachmentsIds', [$attachment->getId()]);

        foreach ($emailAddresses as $emailAddress) {
            $this->log->debug('GenerateAndSendPdf: Adding email address', ['address' => $emailAddress]);
            $email->addToAddress($emailAddress);
        }

        try {
            $this->log->info('GenerateAndSendPdf: Sending email via EmailSender');
            $this->emailSender->send($email);
            $this->log->info('GenerateAndSendPdf: Email sent successfully');
        } catch (\Exception $e) {
            $this->log->error('GenerateAndSendPdf: Error sending email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new Error("Error sending email: " . $e->getMessage());
        }
    }

    private function generateEmailSubject(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $parts = [];

        if ($brand) {
            $parts[] = $brand;
        }

        $parts[] = "SalesOrder Report";
        $parts[] = "{$salesOrderCount} orders";

        if ($status) {
            $parts[] = "({$status})";
        }

        return implode(' - ', $parts);
    }

    private function generateEmailBody(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $body = "<p>Estimado/a,</p>";
        $body .= "<p>Adjunto encontrará el reporte PDF con <strong>{$salesOrderCount} SalesOrder</strong> para revisión interna.</p>";

        if ($brand) {
            $body .= "<p>Marca: <strong>{$brand}</strong></p>";
        }

        if ($status) {
            $body .= "<p>Estado: <strong>{$status}</strong></p>";
        }

        $body .= "<p>Por favor, revise el documento antes de enviarlo a los partners.</p>";
        $body .= "<p>Saludos cordiales.</p>";

        return $body;
    }
}
