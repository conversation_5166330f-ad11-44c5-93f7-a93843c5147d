<?php

namespace Espo\Custom\ServiceActions;

use Espo\Core\Exceptions\Error;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Core\Select\SelectBuilderFactory;
use Espo\Core\Utils\DateTime;
use Espo\Core\Utils\Util;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\Modules\Advanced\Tools\Workflow\Action\RunAction\ServiceAction;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\MassService as PdfMassService;

class GenerateAndSendPdf implements ServiceAction
{
    public function __construct(
        private EntityManager $entityManager,
        private InjectableFactory $injectableFactory,
        private EmailSender $emailSender,
        private FileStorageManager $fileStorageManager,
        private SelectBuilderFactory $selectBuilderFactory
    ) {}

    public function run(Entity $entity, mixed $data): mixed
    {
        if (!is_object($data)) {
            throw new Error("Data must be an object");
        }

        $pdfTemplateId = $data->pdfTemplateId ?? null;
        $emailAddresses = $data->emailAddresses ?? [];
        $brand = $data->brand ?? null;
        $withDate = $data->withDate ?? false;

        if (!$pdfTemplateId) {
            throw new Error("PDF Template ID is required");
        }

        if (empty($emailAddresses)) {
            throw new Error("Email addresses are required");
        }

        if (is_string($emailAddresses)) {
            $emailAddresses = array_filter(array_map('trim', explode(',', $emailAddresses)));
        } elseif (!is_array($emailAddresses)) {
            $emailAddresses = [$emailAddresses];
        }

        $emailAddresses = array_filter($emailAddresses, fn($email) => !empty($email));

        // Buscar SalesOrder que cumplan con los filtros
        $salesOrders = $this->findSalesOrders($brand, $withDate);

        if (empty($salesOrders)) {
            return [
                'success' => true,
                'message' => 'No SalesOrder found matching criteria',
                'emailsSent' => 0
            ];
        }

        // Generar 1 solo PDF con todos los SalesOrder encontrados
        $attachment = $this->generatePdf($salesOrders, $pdfTemplateId, $data);
        $this->sendEmail($attachment, $emailAddresses, $data->status ?? null, $brand, count($salesOrders));

        return [
            'success' => true,
            'attachmentId' => $attachment->getId(),
            'salesOrdersProcessed' => count($salesOrders),
            'emailsSent' => count($emailAddresses)
        ];
    }

    private function findSalesOrders(?string $brand, bool $withDate): array
    {
        $selectBuilder = $this->selectBuilderFactory
            ->create()
            ->from('SalesOrder')
            ->leftJoin('items')
            ->leftJoin('items.product');

        // Solo SalesOrder con status OFFER
        $selectBuilder->where([
            'status' => 'OFFER'
        ]);

        // Filtro por brand del producto si se proporciona
        if ($brand) {
            $selectBuilder->where([
                'items.product.brand' => $brand
            ]);
        }

        // Filtro por deliveryDate
        if ($withDate) {
            $selectBuilder->where([
                'deliveryDate!=' => null
            ]);
        } else {
            $selectBuilder->where([
                'OR' => [
                    ['deliveryDate=' => null],
                    ['deliveryDate=' => '']
                ]
            ]);
        }

        // Ordenar por número de SalesOrder
        $selectBuilder->order('number', 'ASC');

        // Agrupar para evitar duplicados por múltiples items
        $selectBuilder->group('id');

        $query = $selectBuilder->build();

        $collection = $this->entityManager
            ->getRDBRepository('SalesOrder')
            ->clone($query)
            ->find();

        return iterator_to_array($collection);
    }

    private function generatePdf(array $salesOrders, string $templateId, object $inputData): Attachment
    {
        if (empty($salesOrders)) {
            throw new Error("No SalesOrder to generate PDF");
        }

        $pdfMassService = $this->injectableFactory->create(PdfMassService::class);

        // Extraer IDs de los SalesOrder
        $idList = array_map(fn($salesOrder) => $salesOrder->getId(), $salesOrders);

        try {
            // Usar el servicio de PDF masivo para generar un solo PDF con todos los SalesOrder
            $attachmentId = $pdfMassService->generate(
                'SalesOrder',
                $idList,
                $templateId,
                false // withAcl = false
            );

            $attachment = $this->entityManager->getEntityById(Attachment::ENTITY_TYPE, $attachmentId);

            if (!$attachment) {
                throw new Error("Failed to retrieve generated PDF attachment");
            }

            // Actualizar el nombre del archivo con el formato requerido
            $fileName = $this->generateFileName($inputData);
            $attachment->setName($fileName);
            $this->entityManager->saveEntity($attachment);

            return $attachment;
        } catch (\Exception $e) {
            throw new Error("Error generating PDF: " . $e->getMessage());
        }
    }

    private function generateFileName(object $inputData): string
    {
        $brand = $inputData->brand ?? 'unknown';
        $withDate = $inputData->withDate ?? false;
        $currentDate = DateTime::createNow()->format('Y-m-d');

        $dateStatus = $withDate ? 'with-date' : 'no-date';

        // Formato: salesorders_BWS_no-date_2025-05-28.pdf
        $fileName = "salesorders_{$brand}_{$dateStatus}_{$currentDate}";
        $fileName = Util::sanitizeFileName($fileName);

        return $fileName . '.pdf';
    }

    private function sendEmail(Attachment $attachment, array $emailAddresses, ?string $status, ?string $brand, int $salesOrderCount): void
    {
        $emailAddresses = array_filter(array_map('trim', $emailAddresses), fn($email) => !empty($email));

        if (empty($emailAddresses)) {
            return;
        }

        /** @var Email $email */
        $email = $this->entityManager->getNewEntity(Email::ENTITY_TYPE);
        $subject = $this->generateEmailSubject($brand, $status, $salesOrderCount);
        $body = $this->generateEmailBody($brand, $status, $salesOrderCount);

        $email
            ->setSubject($subject)
            ->setBody($body)
            ->setIsHtml(true)

            ->set('attachmentsIds', [$attachment->getId()]);

        foreach ($emailAddresses as $emailAddress) {
            $email->addToAddress($emailAddress);
        }

        try {
            $this->emailSender->send($email);
        } catch (\Exception $e) {
            throw new Error("Error sending email: " . $e->getMessage());
        }
    }

    private function generateEmailSubject(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $parts = [];

        if ($brand) {
            $parts[] = $brand;
        }

        $parts[] = "SalesOrder Report";
        $parts[] = "{$salesOrderCount} orders";

        if ($status) {
            $parts[] = "({$status})";
        }

        return implode(' - ', $parts);
    }

    private function generateEmailBody(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $body = "<p>Estimado/a,</p>";
        $body .= "<p>Adjunto encontrará el reporte PDF con <strong>{$salesOrderCount} SalesOrder</strong> para revisión interna.</p>";

        if ($brand) {
            $body .= "<p>Marca: <strong>{$brand}</strong></p>";
        }

        if ($status) {
            $body .= "<p>Estado: <strong>{$status}</strong></p>";
        }

        $body .= "<p>Por favor, revise el documento antes de enviarlo a los partners.</p>";
        $body .= "<p>Saludos cordiales.</p>";

        return $body;
    }
}
