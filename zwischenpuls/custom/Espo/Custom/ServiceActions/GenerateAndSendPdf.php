<?php

namespace Espo\Custom\ServiceActions;

use Espo\Core\Exceptions\Error;
use Espo\Core\Exceptions\NotFound;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Core\Utils\DateTime;
use Espo\Core\Utils\Util;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\Modules\Advanced\Tools\Workflow\Action\RunAction\ServiceAction;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\Params;
use Espo\Tools\Pdf\Service as PdfService;

/**
 * ServiceAction que genera un PDF usando un template y lo envía por correo electrónico
 */
class GenerateAndSendPdf implements ServiceAction
{
    public function __construct(
        private EntityManager $entityManager,
        private InjectableFactory $injectableFactory,
        private EmailSender $emailSender,
        private FileStorageManager $fileStorageManager
    ) {}

    /**
     * @param Entity $entity La entidad sobre la cual generar el PDF
     * @param mixed $data Datos que incluyen: brand, pdfTemplateId, status, withDate, emailAddresses
     * @return mixed
     * @throws Error
     * @throws NotFound
     */
    public function run(Entity $entity, mixed $data): mixed
    {
        // Validar que $data sea un objeto
        if (!is_object($data)) {
            throw new Error("Data must be an object");
        }

        // Extraer parámetros
        $brand = $data->brand ?? null;
        $pdfTemplateId = $data->pdfTemplateId ?? null;
        $status = $data->status ?? null;
        $withDate = $data->withDate ?? false;
        $emailAddresses = $data->emailAddresses ?? [];

        // Validaciones
        if (!$pdfTemplateId) {
            throw new Error("PDF Template ID is required");
        }

        if (empty($emailAddresses)) {
            throw new Error("Email addresses are required");
        }

        // Convertir emailAddresses a array si es necesario
        if (is_string($emailAddresses)) {
            $emailAddresses = array_map('trim', explode(',', $emailAddresses));
        } elseif (!is_array($emailAddresses)) {
            $emailAddresses = [$emailAddresses];
        }

        // Generar el PDF
        $attachment = $this->generatePdf($entity, $pdfTemplateId, $brand, $withDate);

        // Enviar el email
        $this->sendEmail($entity, $attachment, $emailAddresses, $status, $brand);

        return [
            'success' => true,
            'attachmentId' => $attachment->getId(),
            'emailsSent' => count($emailAddresses)
        ];
    }

    /**
     * Genera el PDF usando el template especificado
     */
    private function generatePdf(Entity $entity, string $templateId, ?string $brand, bool $withDate): Attachment
    {
        /** @var PdfService $pdfService */
        $pdfService = $this->injectableFactory->create(PdfService::class);

        // Parámetros para la generación del PDF
        $params = Params::create()->withAcl(false);

        try {
            $contents = $pdfService->generate(
                $entity->getEntityType(),
                $entity->getId(),
                $templateId,
                $params
            );
        } catch (\Exception $e) {
            throw new Error("Error generating PDF: " . $e->getMessage());
        }

        // Crear el nombre del archivo
        $fileName = $this->generateFileName($entity, $brand, $withDate);

        // Crear el attachment
        /** @var Attachment $attachment */
        $attachment = $this->entityManager->getNewEntity(Attachment::ENTITY_TYPE);

        $attachment
            ->setName($fileName)
            ->setType('application/pdf')
            ->setSize($contents->getStream()->getSize())
            ->setRole(Attachment::ROLE_ATTACHMENT);

        $this->entityManager->saveEntity($attachment);

        // Guardar el contenido del archivo
        $this->fileStorageManager->putStream($attachment, $contents->getStream());

        return $attachment;
    }

    /**
     * Genera el nombre del archivo PDF
     */
    private function generateFileName(Entity $entity, ?string $brand, bool $withDate): string
    {
        $parts = [];

        // Agregar brand si está disponible
        if ($brand) {
            $parts[] = $brand;
        }

        // Agregar tipo de entidad y nombre/ID
        $entityName = $entity->get('name') ?? $entity->getId();
        $parts[] = $entity->getEntityType() . '_' . $entityName;

        // Agregar fecha si se solicita
        if ($withDate) {
            $parts[] = DateTime::createNow()->format('Y-m-d');
        }

        $fileName = implode('_', $parts);
        $fileName = Util::sanitizeFileName($fileName);

        return $fileName . '.pdf';
    }

    /**
     * Envía el email con el PDF adjunto
     */
    private function sendEmail(Entity $entity, Attachment $attachment, array $emailAddresses, ?string $status, ?string $brand): void
    {
        foreach ($emailAddresses as $emailAddress) {
            $emailAddress = trim($emailAddress);
            
            if (empty($emailAddress)) {
                continue;
            }

            /** @var Email $email */
            $email = $this->entityManager->getNewEntity(Email::ENTITY_TYPE);

            // Configurar el email
            $subject = $this->generateEmailSubject($entity, $brand, $status);
            $body = $this->generateEmailBody($entity, $brand, $status);

            $email
                ->setSubject($subject)
                ->setBody($body)
                ->setIsHtml(true)
                ->addToAddress($emailAddress)
                ->set('parentId', $entity->getId())
                ->set('parentType', $entity->getEntityType())
                ->set('attachmentsIds', [$attachment->getId()]);

            try {
                $this->emailSender->send($email);
            } catch (\Exception $e) {
                throw new Error("Error sending email to {$emailAddress}: " . $e->getMessage());
            }
        }
    }

    /**
     * Genera el asunto del email
     */
    private function generateEmailSubject(Entity $entity, ?string $brand, ?string $status): string
    {
        $parts = [];

        if ($brand) {
            $parts[] = $brand;
        }

        $parts[] = $entity->getEntityType();
        
        $entityName = $entity->get('name') ?? $entity->getId();
        $parts[] = $entityName;

        if ($status) {
            $parts[] = "({$status})";
        }

        return implode(' - ', $parts);
    }

    /**
     * Genera el cuerpo del email
     */
    private function generateEmailBody(Entity $entity, ?string $brand, ?string $status): string
    {
        $entityName = $entity->get('name') ?? $entity->getId();
        $entityType = $entity->getEntityType();

        $body = "<p>Estimado/a,</p>";
        $body .= "<p>Adjunto encontrará el documento PDF solicitado para el {$entityType}: <strong>{$entityName}</strong>.</p>";

        if ($brand) {
            $body .= "<p>Marca: <strong>{$brand}</strong></p>";
        }

        if ($status) {
            $body .= "<p>Estado: <strong>{$status}</strong></p>";
        }

        $body .= "<p>Saludos cordiales.</p>";

        return $body;
    }
}
