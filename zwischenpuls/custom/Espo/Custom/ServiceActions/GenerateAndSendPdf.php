<?php

namespace Espo\Custom\ServiceActions;

use Espo\Core\Exceptions\Error;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Core\Select\SelectBuilderFactory;
use Espo\Core\Utils\DateTime;
use Espo\Core\Utils\Util;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\Modules\Advanced\Tools\Workflow\Action\RunAction\ServiceAction;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\Data;
use Espo\Tools\Pdf\Params;
use Espo\Tools\Pdf\Service as PdfService;
use stdClass;

class GenerateAndSendPdf implements ServiceAction
{
    public function __construct(
        private EntityManager $entityManager,
        private InjectableFactory $injectableFactory,
        private EmailSender $emailSender,
        private FileStorageManager $fileStorageManager,
        private SelectBuilderFactory $selectBuilderFactory
    ) {}

    public function run(Entity $entity, mixed $data): mixed
    {
        if (!is_object($data)) {
            throw new Error("Data must be an object");
        }

        $pdfTemplateId = $data->pdfTemplateId ?? null;
        $emailAddresses = $data->emailAddresses ?? [];
        $brand = $data->brand ?? null;
        $withDate = $data->withDate ?? false;

        if (!$pdfTemplateId) {
            throw new Error("PDF Template ID is required");
        }

        if (empty($emailAddresses)) {
            throw new Error("Email addresses are required");
        }

        if (is_string($emailAddresses)) {
            $emailAddresses = array_filter(array_map('trim', explode(',', $emailAddresses)));
        } elseif (!is_array($emailAddresses)) {
            $emailAddresses = [$emailAddresses];
        }

        $emailAddresses = array_filter($emailAddresses, fn($email) => !empty($email));

        // Buscar SalesOrder que cumplan con los filtros
        $salesOrders = $this->findSalesOrders($brand, $withDate);

        if (empty($salesOrders)) {
            return [
                'success' => true,
                'message' => 'No SalesOrder found matching criteria',
                'emailsSent' => 0
            ];
        }

        // Generar 1 solo PDF con todos los SalesOrder encontrados
        $attachment = $this->generatePdf($salesOrders, $pdfTemplateId, $data);
        $this->sendEmail($attachment, $emailAddresses, $data->status ?? null, $brand, count($salesOrders));

        return [
            'success' => true,
            'attachmentId' => $attachment->getId(),
            'salesOrdersProcessed' => count($salesOrders),
            'emailsSent' => count($emailAddresses)
        ];
    }

    private function findSalesOrders(?string $brand, bool $withDate): array
    {
        // Usar directamente el repositorio para hacer la consulta
        $repository = $this->entityManager->getRDBRepository('SalesOrder');

        $whereClause = [
            'status' => 'Draft'
        ];

        // Filtro por deliveryDate
        if ($withDate) {
            $whereClause['deliveryDate!='] = null;
        } else {
            $whereClause['OR'] = [
                ['deliveryDate=' => null],
                ['deliveryDate=' => '']
            ];
        }

        $selectParams = [
            'whereClause' => $whereClause,
            'orderBy' => 'number',
            'order' => 'ASC'
        ];

        $collection = $repository->find($selectParams);
        $salesOrders = iterator_to_array($collection);

        // Si hay filtro por brand, filtrar los SalesOrder que tengan items de ese brand
        if ($brand) {
            $filteredSalesOrders = [];
            foreach ($salesOrders as $salesOrder) {
                if ($this->salesOrderHasBrandItems($salesOrder, $brand)) {
                    $filteredSalesOrders[] = $salesOrder;
                }
            }
            return $filteredSalesOrders;
        }

        return $salesOrders;
    }

    private function salesOrderHasBrandItems(Entity $salesOrder, string $brand): bool
    {
        $itemCollection = $this->entityManager
            ->getRDBRepository('SalesOrderItem')
            ->where(['salesOrderId' => $salesOrder->getId()])
            ->find();

        foreach ($itemCollection as $item) {
            $product = $item->get('product');
            if ($product && $product->get('brand') === $brand) {
                return true;
            }
        }

        return false;
    }

    private function generatePdf(array $salesOrders, string $templateId, object $inputData): Attachment
    {
        if (empty($salesOrders)) {
            throw new Error("No SalesOrder to generate PDF");
        }

        // Preparar datos adicionales para el template
        $templateData = $this->prepareTemplateData($salesOrders, $inputData);
        $data = Data::create()->withAdditionalTemplateData($templateData);

        // Usar el primer SalesOrder como base para generar el PDF
        $firstSalesOrder = $salesOrders[0];
        $pdfService = $this->injectableFactory->create(PdfService::class);
        $params = Params::create()->withAcl(false);

        try {
            $contents = $pdfService->generate(
                $firstSalesOrder->getEntityType(),
                $firstSalesOrder->getId(),
                $templateId,
                $params,
                $data
            );

            $fileName = $this->generateFileName($inputData);
            $attachment = $this->entityManager->getNewEntity(Attachment::ENTITY_TYPE);

            $attachment
                ->setName($fileName)
                ->setType('application/pdf')
                ->setSize($contents->getStream()->getSize())
                ->setRole(Attachment::ROLE_ATTACHMENT);

            $this->entityManager->saveEntity($attachment);
            $this->fileStorageManager->putStream($attachment, $contents->getStream());

            return $attachment;
        } catch (\Exception $e) {
            throw new Error("Error generating PDF: " . $e->getMessage());
        }
    }

    private function prepareTemplateData(array $salesOrders, object $inputData): stdClass
    {
        $brand = $inputData->brand ?? null;
        $currentDate = DateTime::createNow()->format('Y-m-d');
        $currentDateTime = DateTime::createNow()->format('Y-m-d H:i:s');

        // Preparar datos básicos
        $templateData = new stdClass();

        // Variables del ServiceAction
        foreach (get_object_vars($inputData) as $key => $value) {
            if (!in_array($key, ['pdfTemplateId', 'emailAddresses'])) {
                $templateData->$key = $value;
            }
        }

        // Variables de fecha/hora
        $templateData->currentDate = $currentDate;
        $templateData->currentDateTime = $currentDateTime;
        $templateData->generationDate = $currentDateTime;

        // Estadísticas generales
        $templateData->totalSalesOrders = count($salesOrders);
        $templateData->sender = 'YPEK GmbH';

        // Preparar array plano de items filtrados por brand
        $brandItems = $this->getBrandItems($salesOrders, $brand);
        $templateData->brandItems = $brandItems;

        // Calcular totales de items
        $totalAmount = 0;
        $totalItems = count($brandItems);

        foreach ($brandItems as $item) {
            $totalAmount += $item->amount ?? 0;
        }

        $templateData->totalAmount = $totalAmount;
        $templateData->totalItems = $totalItems;

        // Agregar sufijo _RAW para campos que necesitan formateo
        $templateData->totalAmount_RAW = $totalAmount;
        $templateData->currentDate_RAW = $currentDate;
        $templateData->currentDateTime_RAW = $currentDateTime;

        // Variables específicas para BWS (cover page)
        if ($brand === 'BWS') {
            $templateData->showCoverPage = true;
            $templateData->coverPageData = $this->prepareCoverPageData($salesOrders, $currentDateTime);
        } else {
            $templateData->showCoverPage = false;
        }

        return $templateData;
    }

    private function generateFileName(object $inputData): string
    {
        $brand = $inputData->brand ?? 'unknown';
        $withDate = $inputData->withDate ?? false;
        $currentDate = DateTime::createNow()->format('Y-m-d');

        $dateStatus = $withDate ? 'with-date' : 'no-date';

        // Formato: salesorders_BWS_no-date_2025-05-28.pdf
        $fileName = "salesorders_{$brand}_{$dateStatus}_{$currentDate}";
        $fileName = Util::sanitizeFileName($fileName);

        return $fileName . '.pdf';
    }

    private function getBrandItems(array $salesOrders, ?string $brand): array
    {
        $brandItems = [];

        foreach ($salesOrders as $salesOrder) {
            // Cargar datos de cuenta/contacto una vez por SalesOrder
            $account = $salesOrder->get('account');
            $accountName = $account ? $account->get('name') : '';
            $billingAddress = $account ? $account->get('billingAddress') : '';
            $shippingAddress = $account ? $account->get('shippingAddress') : '';

            // Cargar items del SalesOrder
            $itemCollection = $this->entityManager
                ->getRDBRepository('SalesOrderItem')
                ->where(['salesOrderId' => $salesOrder->getId()])
                ->find();

            foreach ($itemCollection as $item) {
                $product = $item->get('product');

                // Solo incluir items cuyo producto tenga el brand especificado
                if ($product && (!$brand || $product->get('brand') === $brand)) {
                    $itemData = new stdClass();

                    // Datos del item
                    $itemData->itemId = $item->getId();
                    $itemData->quantity = $item->get('quantity');
                    $itemData->quantity_RAW = $item->get('quantity');
                    $itemData->unitPrice = $item->get('unitPrice');
                    $itemData->unitPrice_RAW = $item->get('unitPrice');
                    $itemData->amount = $item->get('amount');
                    $itemData->amount_RAW = $item->get('amount');

                    // Datos del producto
                    $itemData->productName = $product->get('name');
                    $itemData->productNumber = $product->get('productNumber') ?? $product->get('sku');
                    $itemData->productBrand = $product->get('brand');

                    // Datos del SalesOrder
                    $itemData->salesOrderId = $salesOrder->getId();
                    $itemData->salesOrderNumber = $salesOrder->get('number');
                    $itemData->salesOrderAmount = $salesOrder->get('amount');
                    $itemData->salesOrderAmount_RAW = $salesOrder->get('amount');
                    $itemData->salesOrderStatus = $salesOrder->get('status');
                    $itemData->deliveryDate = $salesOrder->get('deliveryDate');
                    $itemData->deliveryDate_RAW = $salesOrder->get('deliveryDate');
                    $itemData->createdAt = $salesOrder->get('createdAt');
                    $itemData->createdAt_RAW = $salesOrder->get('createdAt');

                    // Datos del cliente
                    $itemData->accountName = $accountName;
                    $itemData->billingAddress = $billingAddress;
                    $itemData->shippingAddress = $shippingAddress;

                    $brandItems[] = $itemData;
                }
            }
        }

        return $brandItems;
    }

    private function prepareCoverPageData(array $salesOrders, string $currentDateTime): stdClass
    {
        $coverData = new stdClass();
        $coverData->sender = 'YPEK GmbH';
        $coverData->generationDate = $currentDateTime;
        $coverData->generationDate_RAW = $currentDateTime;
        $coverData->totalPages = count($salesOrders); // Aproximación

        $totalSubtotal = 0;
        foreach ($salesOrders as $salesOrder) {
            $totalSubtotal += $salesOrder->get('amount') ?? 0;
        }
        $coverData->totalSubtotal = $totalSubtotal;
        $coverData->totalSubtotal_RAW = $totalSubtotal;
        $coverData->salesOrderCount = count($salesOrders);

        return $coverData;
    }

    private function sendEmail(Attachment $attachment, array $emailAddresses, ?string $status, ?string $brand, int $salesOrderCount): void
    {
        $emailAddresses = array_filter(array_map('trim', $emailAddresses), fn($email) => !empty($email));

        if (empty($emailAddresses)) {
            return;
        }

        /** @var Email $email */
        $email = $this->entityManager->getNewEntity(Email::ENTITY_TYPE);
        $subject = $this->generateEmailSubject($brand, $status, $salesOrderCount);
        $body = $this->generateEmailBody($brand, $status, $salesOrderCount);

        $email
            ->setSubject($subject)
            ->setBody($body)
            ->setIsHtml(true)

            ->set('attachmentsIds', [$attachment->getId()]);

        foreach ($emailAddresses as $emailAddress) {
            $email->addToAddress($emailAddress);
        }

        try {
            $this->emailSender->send($email);
        } catch (\Exception $e) {
            throw new Error("Error sending email: " . $e->getMessage());
        }
    }

    private function generateEmailSubject(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $parts = [];

        if ($brand) {
            $parts[] = $brand;
        }

        $parts[] = "SalesOrder Report";
        $parts[] = "{$salesOrderCount} orders";

        if ($status) {
            $parts[] = "({$status})";
        }

        return implode(' - ', $parts);
    }

    private function generateEmailBody(?string $brand, ?string $status, int $salesOrderCount): string
    {
        $body = "<p>Estimado/a,</p>";
        $body .= "<p>Adjunto encontrará el reporte PDF con <strong>{$salesOrderCount} SalesOrder</strong> para revisión interna.</p>";

        if ($brand) {
            $body .= "<p>Marca: <strong>{$brand}</strong></p>";
        }

        if ($status) {
            $body .= "<p>Estado: <strong>{$status}</strong></p>";
        }

        $body .= "<p>Por favor, revise el documento antes de enviarlo a los partners.</p>";
        $body .= "<p>Saludos cordiales.</p>";

        return $body;
    }
}
