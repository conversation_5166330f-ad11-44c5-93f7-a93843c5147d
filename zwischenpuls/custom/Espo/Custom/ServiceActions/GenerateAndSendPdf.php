<?php

namespace Espo\Custom\ServiceActions;

use Espo\Core\Exceptions\Error;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Core\Utils\DateTime;
use Espo\Core\Utils\Util;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\Modules\Advanced\Tools\Workflow\Action\RunAction\ServiceAction;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\Data;
use Espo\Tools\Pdf\Params;
use Espo\Tools\Pdf\Service as PdfService;
use stdClass;

class GenerateAndSendPdf implements ServiceAction
{
    public function __construct(
        private EntityManager $entityManager,
        private InjectableFactory $injectableFactory,
        private EmailSender $emailSender,
        private FileStorageManager $fileStorageManager
    ) {}

    public function run(Entity $entity, mixed $data): mixed
    {
        if (!is_object($data)) {
            throw new Error("Data must be an object");
        }

        $pdfTemplateId = $data->pdfTemplateId ?? null;
        $emailAddresses = $data->emailAddresses ?? [];
        $brand = $data->brand ?? null;
        $status = $data->status ?? null;

        if (!$pdfTemplateId) {
            throw new Error("PDF Template ID is required");
        }

        if (empty($emailAddresses)) {
            throw new Error("Email addresses are required");
        }

        if (is_string($emailAddresses)) {
            $emailAddresses = array_filter(array_map('trim', explode(',', $emailAddresses)));
        } elseif (!is_array($emailAddresses)) {
            $emailAddresses = [$emailAddresses];
        }
        
        $emailAddresses = array_filter($emailAddresses, fn($email) => !empty($email));

        $attachment = $this->generatePdf($entity, $pdfTemplateId, $data);
        $this->sendEmail($entity, $attachment, $emailAddresses, $status, $brand);

        return [
            'success' => true,
            'attachmentId' => $attachment->getId(),
            'emailsSent' => count($emailAddresses)
        ];
    }

    private function generatePdf(Entity $entity, string $templateId, object $inputData): Attachment
    {
        $pdfService = $this->injectableFactory->create(PdfService::class);
        $params = Params::create()->withAcl(false);

        // Pasar todas las variables del input al template PDF
        $templateData = new stdClass();
        foreach (get_object_vars($inputData) as $key => $value) {
            if ($key !== 'pdfTemplateId' && $key !== 'emailAddresses') {
                $templateData->$key = $value;
            }
        }

        $data = Data::create()->withAdditionalTemplateData($templateData);

        try {
            $contents = $pdfService->generate(
                $entity->getEntityType(),
                $entity->getId(),
                $templateId,
                $params,
                $data
            );
        } catch (\Exception $e) {
            throw new Error("Error generating PDF: " . $e->getMessage());
        }

        $fileName = $this->generateFileName($entity, $inputData);
        $attachment = $this->entityManager->getNewEntity(Attachment::ENTITY_TYPE);

        $attachment
            ->setName($fileName)
            ->setType('application/pdf')
            ->setSize($contents->getStream()->getSize())
            ->setRole(Attachment::ROLE_ATTACHMENT);

        $this->entityManager->saveEntity($attachment);
        $this->fileStorageManager->putStream($attachment, $contents->getStream());

        return $attachment;
    }

    private function generateFileName(Entity $entity, object $inputData): string
    {
        $parts = [];

        $brand = $inputData->brand ?? null;
        $status = $inputData->status ?? null;
        $withDate = $inputData->withDate ?? false;

        if ($brand) {
            $parts[] = $brand;
        }

        $entityName = $entity->get('name') ?? $entity->getId();
        $parts[] = $entity->getEntityType() . '_' . $entityName;

        if ($status) {
            $parts[] = $status;
        }

        if ($withDate) {
            $parts[] = DateTime::createNow()->format('Y-m-d');
        }

        $fileName = implode('_', $parts);
        $fileName = Util::sanitizeFileName($fileName);

        return $fileName . '.pdf';
    }

    private function sendEmail(Entity $entity, Attachment $attachment, array $emailAddresses, ?string $status, ?string $brand): void
    {
        $emailAddresses = array_filter(array_map('trim', $emailAddresses), fn($email) => !empty($email));

        if (empty($emailAddresses)) {
            return;
        }

        /** @var Email $email */
        $email = $this->entityManager->getNewEntity(Email::ENTITY_TYPE);
        $subject = $this->generateEmailSubject($entity, $brand, $status);
        $body = $this->generateEmailBody($entity, $brand, $status);

        $email
            ->setSubject($subject)
            ->setBody($body)
            ->setIsHtml(true)
            ->setParent($entity)
            ->set('parentId', $entity->getId())
            ->set('parentType', $entity->getEntityType())
            ->set('attachmentsIds', [$attachment->getId()]);

        foreach ($emailAddresses as $emailAddress) {
            $email->addToAddress($emailAddress);
        }

        try {
            $this->emailSender->send($email);
        } catch (\Exception $e) {
            throw new Error("Error sending email: " . $e->getMessage());
        }
    }

    private function generateEmailSubject(Entity $entity, ?string $brand, ?string $status): string
    {
        $parts = [];

        if ($brand) {
            $parts[] = $brand;
        }

        $parts[] = $entity->getEntityType();
        
        $entityName = $entity->get('name') ?? $entity->getId();
        $parts[] = $entityName;

        if ($status) {
            $parts[] = "({$status})";
        }

        return implode(' - ', $parts);
    }

    private function generateEmailBody(Entity $entity, ?string $brand, ?string $status): string
    {
        $entityName = $entity->get('name') ?? $entity->getId();
        $entityType = $entity->getEntityType();

        $body = "<p>Estimado/a,</p>";
        $body .= "<p>Adjunto encontrará el documento PDF solicitado para el {$entityType}: <strong>{$entityName}</strong>.</p>";

        if ($brand) {
            $body .= "<p>Marca: <strong>{$brand}</strong></p>";
        }

        if ($status) {
            $body .= "<p>Estado: <strong>{$status}</strong></p>";
        }

        $body .= "<p>Saludos cordiales.</p>";

        return $body;
    }
}
