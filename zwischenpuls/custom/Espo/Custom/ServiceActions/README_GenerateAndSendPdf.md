# ServiceAction: GenerateAndSendPdf

## Descripción

Este ServiceAction genera un PDF usando un template específico y lo envía por correo electrónico a las direcciones especificadas.

## Ubicación

`Espo\Custom\ServiceActions\GenerateAndSendPdf`

## Parámetros de entrada

El ServiceAction recibe los siguientes parámetros a través del objeto `$data`:

### Parámetros requeridos:

- **`pdfTemplateId`** (string): ID del template PDF que se usará para generar el documento
- **`emailAddresses`** (string|array): Direcciones de correo electrónico donde enviar el PDF
  - Puede ser una cadena con direcciones separadas por comas: `"<EMAIL>, <EMAIL>"`
  - O un array de direcciones: `["<EMAIL>", "<EMAIL>"]`

### Parámetros opcionales:

- **`brand`** (string): Marca o identificador que se incluirá en el nombre del archivo y en el email
- **`status`** (string): Estado que se mostrará en el asunto y cuerpo del email
- **`withDate`** (boolean): Si es `true`, incluye la fecha actual en el nombre del archivo PDF

## Ejemplo de uso en Workflow

```json
{
  "pdfTemplateId": "64a1b2c3d4e5f6789012345",
  "emailAddresses": "<EMAIL>, <EMAIL>",
  "brand": "MiMarca",
  "status": "Aprobado",
  "withDate": true
}
```

## Funcionalidad

### 1. Generación del PDF
- Utiliza el servicio `PdfService` de EspoCRM para generar el PDF
- Aplica el template especificado a la entidad actual
- Crea un archivo attachment con el PDF generado

### 2. Nomenclatura del archivo
El nombre del archivo PDF se genera automáticamente con el siguiente formato:
- `[brand]_[EntityType]_[EntityName]_[fecha].pdf`

Ejemplos:
- `MiMarca_Quote_Q-001_2024-01-15.pdf` (con fecha)
- `MiMarca_Account_Acme Corp.pdf` (sin fecha)

### 3. Envío de email
- Crea un email individual para cada dirección especificada
- Adjunta el PDF generado
- Establece la entidad actual como parent del email
- Genera automáticamente el asunto y cuerpo del email

### 4. Contenido del email

**Asunto:** `[brand] - [EntityType] - [EntityName] ([status])`

**Cuerpo:** Email HTML con información de la entidad, marca y estado

## Valor de retorno

El ServiceAction retorna un array con la siguiente información:

```php
[
    'success' => true,
    'attachmentId' => 'id-del-attachment-creado',
    'emailsSent' => 2  // número de emails enviados
]
```

## Manejo de errores

El ServiceAction lanza excepciones `Error` en los siguientes casos:
- Si `$data` no es un objeto
- Si no se proporciona `pdfTemplateId`
- Si no se proporcionan `emailAddresses`
- Si hay errores en la generación del PDF
- Si hay errores en el envío de emails

## Dependencias

El ServiceAction utiliza las siguientes dependencias inyectadas:
- `EntityManager`: Para crear entidades y guardar datos
- `InjectableFactory`: Para crear instancias de servicios
- `EmailSender`: Para enviar los correos electrónicos
- `FileStorageManager`: Para guardar el contenido del PDF

## Configuración en Workflow

Para usar este ServiceAction en un Workflow:

1. Crear un Workflow con acción "Run Service Action"
2. Especificar el nombre de la clase: `GenerateAndSendPdf`
3. Configurar los datos JSON con los parámetros necesarios

## Notas importantes

- El PDF se genera sin aplicar ACL (`withAcl(false)`) para asegurar acceso completo a los datos
- Cada email se envía individualmente, no en copia (BCC)
- El attachment se guarda permanentemente en el sistema
- Los emails quedan registrados en el historial de la entidad
