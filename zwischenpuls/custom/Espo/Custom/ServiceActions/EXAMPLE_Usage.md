# Ejemplo de uso del ServiceAction GenerateAndSendPdf

## Escenario: Envío automático de cotizaciones en PDF

Supongamos que queremos enviar automáticamente una cotización en PDF cuando su estado cambia a "Aprobada".

### 1. Configuración del Workflow

**Entidad:** Quote (Cotización)
**Tipo:** After record saved
**Condiciones:** Status equals "Approved"

### 2. Configuración de la acción

**Tipo de acción:** Run Service Action
**Nombre del servicio:** `GenerateAndSendPdf`

### 3. Datos JSON para la acción

#### Ejemplo básico:
```json
{
  "pdfTemplateId": "64a1b2c3d4e5f6789012345",
  "emailAddresses": "<EMAIL>"
}
```

#### Ejemplo completo:
```json
{
  "pdfTemplateId": "64a1b2c3d4e5f6789012345",
  "emailAddresses": "<EMAIL>, <EMAIL>",
  "brand": "MiEmpresa",
  "status": "Aprobada",
  "withDate": true
}
```

#### Ejemplo usando campos de la entidad:
```json
{
  "pdfTemplateId": "64a1b2c3d4e5f6789012345",
  "emailAddresses": "{billingContactEmailAddress}",
  "brand": "{accountName}",
  "status": "{status}",
  "withDate": true
}
```

### 4. Resultado esperado

Cuando una cotización cambie a estado "Aprobada":

1. Se generará un PDF usando el template especificado
2. El archivo se nombrará como: `MiEmpresa_Quote_Q-001_2024-01-15.pdf`
3. Se enviará un email a las direcciones especificadas con:
   - **Asunto:** "MiEmpresa - Quote - Q-001 (Aprobada)"
   - **Cuerpo:** Email HTML con información de la cotización
   - **Adjunto:** El PDF generado

## Escenario: Envío de facturas con múltiples destinatarios

### Configuración para facturas:

```json
{
  "pdfTemplateId": "invoice-template-id",
  "emailAddresses": [
    "{billingContactEmailAddress}",
    "<EMAIL>",
    "<EMAIL>"
  ],
  "brand": "Facturación Automática",
  "status": "Enviada",
  "withDate": true
}
```

## Escenario: Reportes mensuales

### Para envío de reportes automáticos:

```json
{
  "pdfTemplateId": "monthly-report-template",
  "emailAddresses": "<EMAIL>, <EMAIL>",
  "brand": "Reporte Mensual",
  "status": "Completado",
  "withDate": true
}
```

## Variables disponibles en los datos JSON

Puedes usar cualquier campo de la entidad actual en los valores JSON usando la sintaxis `{fieldName}`:

- `{name}` - Nombre de la entidad
- `{emailAddress}` - Email principal
- `{status}` - Estado actual
- `{accountName}` - Nombre de la cuenta (si aplica)
- `{assignedUserName}` - Usuario asignado
- `{createdAt}` - Fecha de creación
- `{modifiedAt}` - Fecha de modificación

## Consejos de configuración

### 1. Validación de emails
El sistema validará automáticamente que las direcciones de email no estén vacías.

### 2. Múltiples direcciones
Puedes especificar múltiples direcciones de varias formas:
- Como string separado por comas: `"<EMAIL>, <EMAIL>"`
- Como array JSON: `["<EMAIL>", "<EMAIL>"]`

### 3. Nombres de archivo
Los nombres de archivo se sanitizan automáticamente para evitar caracteres especiales.

### 4. Manejo de errores
Si algún email falla, el proceso continuará con los siguientes emails. Los errores se registrarán en los logs del sistema.

## Verificación del funcionamiento

Para verificar que el ServiceAction funciona correctamente:

1. **Logs del sistema:** Revisar los logs de EspoCRM para errores
2. **Entidad Email:** Verificar que se crearon los emails en el sistema
3. **Attachments:** Confirmar que se crearon los archivos PDF
4. **Bandeja de entrada:** Verificar que los emails llegaron a los destinatarios

## Troubleshooting

### Error: "PDF Template ID is required"
- Verificar que el ID del template PDF sea válido
- Confirmar que el template existe en el sistema

### Error: "Email addresses are required"
- Verificar que se especificaron direcciones de email
- Confirmar que los campos de email de la entidad no estén vacíos

### Error: "Error generating PDF"
- Verificar que el template PDF sea compatible con la entidad
- Confirmar que la entidad tenga los datos necesarios para el template

### Error: "Error sending email"
- Verificar la configuración SMTP del sistema
- Confirmar que las direcciones de email sean válidas
