<?php

namespace Espo\Custom\ServiceActions\Tests;

use Espo\Core\Exceptions\Error;
use Espo\Core\FileStorage\Manager as FileStorageManager;
use Espo\Core\InjectableFactory;
use Espo\Core\Mail\EmailSender;
use Espo\Custom\ServiceActions\GenerateAndSendPdf;
use Espo\Entities\Attachment;
use Espo\Entities\Email;
use Espo\ORM\Entity;
use Espo\ORM\EntityManager;
use Espo\Tools\Pdf\Contents;
use Espo\Tools\Pdf\Service as PdfService;
use PHPUnit\Framework\TestCase;
use Psr\Http\Message\StreamInterface;
use stdClass;

class GenerateAndSendPdfTest extends TestCase
{
    private $entityManager;
    private $injectableFactory;
    private $emailSender;
    private $fileStorageManager;
    private $pdfService;
    private $serviceAction;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->injectableFactory = $this->createMock(InjectableFactory::class);
        $this->emailSender = $this->createMock(EmailSender::class);
        $this->fileStorageManager = $this->createMock(FileStorageManager::class);
        $this->pdfService = $this->createMock(PdfService::class);

        $this->serviceAction = new GenerateAndSendPdf(
            $this->entityManager,
            $this->injectableFactory,
            $this->emailSender,
            $this->fileStorageManager
        );
    }

    public function testRunWithValidData()
    {
        // Preparar datos de entrada
        $entity = $this->createMockEntity();
        $data = $this->createValidData();

        // Configurar mocks
        $this->setupMocksForSuccessfulExecution();

        // Ejecutar
        $result = $this->serviceAction->run($entity, $data);

        // Verificar resultado
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('attachmentId', $result);
        $this->assertEquals(2, $result['emailsSent']);
    }

    public function testRunWithInvalidDataThrowsError()
    {
        $entity = $this->createMockEntity();
        $invalidData = "invalid data"; // No es un objeto

        $this->expectException(Error::class);
        $this->expectExceptionMessage("Data must be an object");

        $this->serviceAction->run($entity, $invalidData);
    }

    public function testRunWithoutPdfTemplateIdThrowsError()
    {
        $entity = $this->createMockEntity();
        $data = new stdClass();
        $data->emailAddresses = ["<EMAIL>"];
        // No se incluye pdfTemplateId

        $this->expectException(Error::class);
        $this->expectExceptionMessage("PDF Template ID is required");

        $this->serviceAction->run($entity, $data);
    }

    public function testRunWithoutEmailAddressesThrowsError()
    {
        $entity = $this->createMockEntity();
        $data = new stdClass();
        $data->pdfTemplateId = "template123";
        // No se incluyen emailAddresses

        $this->expectException(Error::class);
        $this->expectExceptionMessage("Email addresses are required");

        $this->serviceAction->run($entity, $data);
    }

    public function testEmailAddressesAsString()
    {
        $entity = $this->createMockEntity();
        $data = new stdClass();
        $data->pdfTemplateId = "template123";
        $data->emailAddresses = "<EMAIL>, <EMAIL>";

        $this->setupMocksForSuccessfulExecution();

        $result = $this->serviceAction->run($entity, $data);

        $this->assertEquals(2, $result['emailsSent']);
    }

    private function createMockEntity(): Entity
    {
        $entity = $this->createMock(Entity::class);
        $entity->method('getEntityType')->willReturn('Quote');
        $entity->method('getId')->willReturn('quote123');
        $entity->method('get')->willReturnMap([
            ['name', 'Test Quote'],
            ['id', 'quote123']
        ]);

        return $entity;
    }

    private function createValidData(): stdClass
    {
        $data = new stdClass();
        $data->pdfTemplateId = "template123";
        $data->emailAddresses = ["<EMAIL>", "<EMAIL>"];
        $data->brand = "TestBrand";
        $data->status = "Approved";
        $data->withDate = true;

        return $data;
    }

    private function setupMocksForSuccessfulExecution(): void
    {
        // Mock PDF Service
        $this->injectableFactory
            ->method('create')
            ->with(PdfService::class)
            ->willReturn($this->pdfService);

        // Mock PDF Contents
        $stream = $this->createMock(StreamInterface::class);
        $stream->method('getSize')->willReturn(1024);

        $contents = $this->createMock(Contents::class);
        $contents->method('getStream')->willReturn($stream);

        $this->pdfService
            ->method('generate')
            ->willReturn($contents);

        // Mock Attachment
        $attachment = $this->createMock(Attachment::class);
        $attachment->method('getId')->willReturn('attachment123');

        $this->entityManager
            ->method('getNewEntity')
            ->willReturnMap([
                [Attachment::ENTITY_TYPE, $attachment],
                [Email::ENTITY_TYPE, $this->createMock(Email::class)]
            ]);

        // Mock Email Sender
        $this->emailSender
            ->method('send')
            ->willReturn(null);
    }
}
